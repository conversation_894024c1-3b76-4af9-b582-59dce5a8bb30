/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface BucketPlan {
    /** This is the name of the bucket. */
    name: string;
    /**
     * This is the region of the bucket.
     *
     * Usage:
     * - If `credential.type` is `aws`, then this is required.
     * - If `credential.type` is `gcp`, then this is optional since GCP allows buckets to be accessed without a region but region is required for data residency requirements. Read here: https://cloud.google.com/storage/docs/request-endpoints
     */
    region?: string;
    /**
     * This is the path where call artifacts will be stored.
     *
     * Usage:
     * - To store call artifacts in a specific folder, set this to the full path. Eg. "/folder-name1/folder-name2".
     * - To store call artifacts in the root of the bucket, leave this blank.
     *
     * @default "/"
     */
    path?: string;
    /**
     * This is the HMAC access key offered by GCP for interoperability with S3 clients. Here is the guide on how to create: https://cloud.google.com/storage/docs/authentication/managing-hmackeys#console
     *
     * Usage:
     * - If `credential.type` is `gcp`, then this is required.
     * - If `credential.type` is `aws`, then this is not required since credential.awsAccessKeyId is used instead.
     */
    hmacAccessKey?: string;
    /**
     * This is the secret for the HMAC access key. Here is the guide on how to create: https://cloud.google.com/storage/docs/authentication/managing-hmackeys#console
     *
     * Usage:
     * - If `credential.type` is `gcp`, then this is required.
     * - If `credential.type` is `aws`, then this is not required since credential.awsSecretAccessKey is used instead.
     *
     * Note: This is not returned in the API.
     */
    hmacSecret?: string;
}
