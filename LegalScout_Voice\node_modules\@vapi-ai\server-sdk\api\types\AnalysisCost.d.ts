/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface AnalysisCost {
    /** This is the type of cost, always 'analysis' for this class. */
    type: "analysis";
    /** This is the type of analysis performed. */
    analysisType: Vapi.AnalysisCostAnalysisType;
    /** This is the model that was used to perform the analysis. */
    model: Record<string, unknown>;
    /** This is the number of prompt tokens used in the analysis. */
    promptTokens: number;
    /** This is the number of completion tokens generated in the analysis. */
    completionTokens: number;
    /** This is the cost of the component in USD. */
    cost: number;
}
