/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface AnalysisCostBreakdown {
    /** This is the cost to summarize the call. */
    summary?: number;
    /** This is the number of prompt tokens used to summarize the call. */
    summaryPromptTokens?: number;
    /** This is the number of completion tokens used to summarize the call. */
    summaryCompletionTokens?: number;
    /** This is the cost to extract structured data from the call. */
    structuredData?: number;
    /** This is the number of prompt tokens used to extract structured data from the call. */
    structuredDataPromptTokens?: number;
    /** This is the number of completion tokens used to extract structured data from the call. */
    structuredDataCompletionTokens?: number;
    /** This is the cost to evaluate if the call was successful. */
    successEvaluation?: number;
    /** This is the number of prompt tokens used to evaluate if the call was successful. */
    successEvaluationPromptTokens?: number;
    /** This is the number of completion tokens used to evaluate if the call was successful. */
    successEvaluationCompletionTokens?: number;
}
