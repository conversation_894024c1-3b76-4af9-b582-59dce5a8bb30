/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the language that will be set for the transcription. The list of languages Azure supports can be found here: https://learn.microsoft.com/en-us/azure/ai-services/speech-service/language-support?tabs=stt
 */
export type AzureSpeechTranscriberLanguage = "af-ZA" | "am-ET" | "ar-AE" | "ar-BH" | "ar-DZ" | "ar-EG" | "ar-IL" | "ar-IQ" | "ar-JO" | "ar-KW" | "ar-LB" | "ar-LY" | "ar-MA" | "ar-OM" | "ar-PS" | "ar-QA" | "ar-SA" | "ar-SY" | "ar-TN" | "ar-YE" | "az-AZ" | "bg-BG" | "bn-IN" | "bs-BA" | "ca-ES" | "cs-CZ" | "cy-GB" | "da-DK" | "de-AT" | "de-CH" | "de-DE" | "el-GR" | "en-AU" | "en-CA" | "en-GB" | "en-GH" | "en-HK" | "en-IE" | "en-IN" | "en-KE" | "en-NG" | "en-NZ" | "en-PH" | "en-SG" | "en-TZ" | "en-US" | "en-ZA" | "es-AR" | "es-BO" | "es-CL" | "es-CO" | "es-CR" | "es-CU" | "es-DO" | "es-EC" | "es-ES" | "es-GQ" | "es-GT" | "es-HN" | "es-MX" | "es-NI" | "es-PA" | "es-PE" | "es-PR" | "es-PY" | "es-SV" | "es-US" | "es-UY" | "es-VE" | "et-EE" | "eu-ES" | "fa-IR" | "fi-FI" | "fil-PH" | "fr-BE" | "fr-CA" | "fr-CH" | "fr-FR" | "ga-IE" | "gl-ES" | "gu-IN" | "he-IL" | "hi-IN" | "hr-HR" | "hu-HU" | "hy-AM" | "id-ID" | "is-IS" | "it-CH" | "it-IT" | "ja-JP" | "jv-ID" | "ka-GE" | "kk-KZ" | "km-KH" | "kn-IN" | "ko-KR" | "lo-LA" | "lt-LT" | "lv-LV" | "mk-MK" | "ml-IN" | "mn-MN" | "mr-IN" | "ms-MY" | "mt-MT" | "my-MM" | "nb-NO" | "ne-NP" | "nl-BE" | "nl-NL" | "pa-IN" | "pl-PL" | "ps-AF" | "pt-BR" | "pt-PT" | "ro-RO" | "ru-RU" | "si-LK" | "sk-SK" | "sl-SI" | "so-SO" | "sq-AL" | "sr-RS" | "sv-SE" | "sw-KE" | "sw-TZ" | "ta-IN" | "te-IN" | "th-TH" | "tr-TR" | "uk-UA" | "ur-IN" | "uz-UZ" | "vi-VN" | "wuu-CN" | "yue-CN" | "zh-CN" | "zh-CN-shandong" | "zh-CN-sichuan" | "zh-HK" | "zh-TW" | "zu-ZA";
export declare const AzureSpeechTranscriberLanguage: {
    readonly AfZa: "af-ZA";
    readonly AmEt: "am-ET";
    readonly ArAe: "ar-AE";
    readonly ArBh: "ar-BH";
    readonly ArDz: "ar-DZ";
    readonly ArEg: "ar-EG";
    readonly ArIl: "ar-IL";
    readonly ArIq: "ar-IQ";
    readonly ArJo: "ar-JO";
    readonly ArKw: "ar-KW";
    readonly ArLb: "ar-LB";
    readonly ArLy: "ar-LY";
    readonly ArMa: "ar-MA";
    readonly ArOm: "ar-OM";
    readonly ArPs: "ar-PS";
    readonly ArQa: "ar-QA";
    readonly ArSa: "ar-SA";
    readonly ArSy: "ar-SY";
    readonly ArTn: "ar-TN";
    readonly ArYe: "ar-YE";
    readonly AzAz: "az-AZ";
    readonly BgBg: "bg-BG";
    readonly BnIn: "bn-IN";
    readonly BsBa: "bs-BA";
    readonly CaEs: "ca-ES";
    readonly CsCz: "cs-CZ";
    readonly CyGb: "cy-GB";
    readonly DaDk: "da-DK";
    readonly DeAt: "de-AT";
    readonly DeCh: "de-CH";
    readonly DeDe: "de-DE";
    readonly ElGr: "el-GR";
    readonly EnAu: "en-AU";
    readonly EnCa: "en-CA";
    readonly EnGb: "en-GB";
    readonly EnGh: "en-GH";
    readonly EnHk: "en-HK";
    readonly EnIe: "en-IE";
    readonly EnIn: "en-IN";
    readonly EnKe: "en-KE";
    readonly EnNg: "en-NG";
    readonly EnNz: "en-NZ";
    readonly EnPh: "en-PH";
    readonly EnSg: "en-SG";
    readonly EnTz: "en-TZ";
    readonly EnUs: "en-US";
    readonly EnZa: "en-ZA";
    readonly EsAr: "es-AR";
    readonly EsBo: "es-BO";
    readonly EsCl: "es-CL";
    readonly EsCo: "es-CO";
    readonly EsCr: "es-CR";
    readonly EsCu: "es-CU";
    readonly EsDo: "es-DO";
    readonly EsEc: "es-EC";
    readonly EsEs: "es-ES";
    readonly EsGq: "es-GQ";
    readonly EsGt: "es-GT";
    readonly EsHn: "es-HN";
    readonly EsMx: "es-MX";
    readonly EsNi: "es-NI";
    readonly EsPa: "es-PA";
    readonly EsPe: "es-PE";
    readonly EsPr: "es-PR";
    readonly EsPy: "es-PY";
    readonly EsSv: "es-SV";
    readonly EsUs: "es-US";
    readonly EsUy: "es-UY";
    readonly EsVe: "es-VE";
    readonly EtEe: "et-EE";
    readonly EuEs: "eu-ES";
    readonly FaIr: "fa-IR";
    readonly FiFi: "fi-FI";
    readonly FilPh: "fil-PH";
    readonly FrBe: "fr-BE";
    readonly FrCa: "fr-CA";
    readonly FrCh: "fr-CH";
    readonly FrFr: "fr-FR";
    readonly GaIe: "ga-IE";
    readonly GlEs: "gl-ES";
    readonly GuIn: "gu-IN";
    readonly HeIl: "he-IL";
    readonly HiIn: "hi-IN";
    readonly HrHr: "hr-HR";
    readonly HuHu: "hu-HU";
    readonly HyAm: "hy-AM";
    readonly IdId: "id-ID";
    readonly IsIs: "is-IS";
    readonly ItCh: "it-CH";
    readonly ItIt: "it-IT";
    readonly JaJp: "ja-JP";
    readonly JvId: "jv-ID";
    readonly KaGe: "ka-GE";
    readonly KkKz: "kk-KZ";
    readonly KmKh: "km-KH";
    readonly KnIn: "kn-IN";
    readonly KoKr: "ko-KR";
    readonly LoLa: "lo-LA";
    readonly LtLt: "lt-LT";
    readonly LvLv: "lv-LV";
    readonly MkMk: "mk-MK";
    readonly MlIn: "ml-IN";
    readonly MnMn: "mn-MN";
    readonly MrIn: "mr-IN";
    readonly MsMy: "ms-MY";
    readonly MtMt: "mt-MT";
    readonly MyMm: "my-MM";
    readonly NbNo: "nb-NO";
    readonly NeNp: "ne-NP";
    readonly NlBe: "nl-BE";
    readonly NlNl: "nl-NL";
    readonly PaIn: "pa-IN";
    readonly PlPl: "pl-PL";
    readonly PsAf: "ps-AF";
    readonly PtBr: "pt-BR";
    readonly PtPt: "pt-PT";
    readonly RoRo: "ro-RO";
    readonly RuRu: "ru-RU";
    readonly SiLk: "si-LK";
    readonly SkSk: "sk-SK";
    readonly SlSi: "sl-SI";
    readonly SoSo: "so-SO";
    readonly SqAl: "sq-AL";
    readonly SrRs: "sr-RS";
    readonly SvSe: "sv-SE";
    readonly SwKe: "sw-KE";
    readonly SwTz: "sw-TZ";
    readonly TaIn: "ta-IN";
    readonly TeIn: "te-IN";
    readonly ThTh: "th-TH";
    readonly TrTr: "tr-TR";
    readonly UkUa: "uk-UA";
    readonly UrIn: "ur-IN";
    readonly UzUz: "uz-UZ";
    readonly ViVn: "vi-VN";
    readonly WuuCn: "wuu-CN";
    readonly YueCn: "yue-CN";
    readonly ZhCn: "zh-CN";
    readonly ZhCnShandong: "zh-CN-shandong";
    readonly ZhCnSichuan: "zh-CN-sichuan";
    readonly ZhHk: "zh-HK";
    readonly ZhTw: "zh-TW";
    readonly ZuZa: "zu-ZA";
};
