{"version": 3, "sources": ["../../hast-util-sanitize/lib/schema.js", "../../hast-util-sanitize/lib/index.js", "../../rehype-sanitize/lib/index.js"], "sourcesContent": ["/**\n * @import {Schema} from 'hast-util-sanitize'\n */\n\n// Couple of ARIA attributes allowed in several, but not all, places.\nconst aria = ['ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy']\n\n/**\n * Default schema.\n *\n * Follows GitHub style sanitation.\n *\n * @type {Schema}\n */\nexport const defaultSchema = {\n  ancestors: {\n    tbody: ['table'],\n    td: ['table'],\n    th: ['table'],\n    thead: ['table'],\n    tfoot: ['table'],\n    tr: ['table']\n  },\n  attributes: {\n    a: [\n      ...aria,\n      // Note: these 3 are used by GFM footnotes, they do work on all links.\n      'dataFootnoteBackref',\n      'dataFootnoteRef',\n      ['className', 'data-footnote-backref'],\n      'href'\n    ],\n    blockquote: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `code` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    code: [['className', /^language-./]],\n    del: ['cite'],\n    div: ['itemScope', 'itemType'],\n    dl: [...aria],\n    // Note: this is used by GFM footnotes.\n    h2: [['className', 'sr-only']],\n    img: [...aria, 'longDesc', 'src'],\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    input: [\n      ['disabled', true],\n      ['type', 'checkbox']\n    ],\n    ins: ['cite'],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `li` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    li: [['className', 'task-list-item']],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ol: [...aria, ['className', 'contains-task-list']],\n    q: ['cite'],\n    section: ['dataFootnotes', ['className', 'footnotes']],\n    source: ['srcSet'],\n    summary: [...aria],\n    table: [...aria],\n    // Note: this class is not normally allowed by GH, when manually writing\n    // `ol` as HTML in markdown, they adds it some other way.\n    // We can’t do that, so we have to allow it.\n    ul: [...aria, ['className', 'contains-task-list']],\n    '*': [\n      'abbr',\n      'accept',\n      'acceptCharset',\n      'accessKey',\n      'action',\n      'align',\n      'alt',\n      'axis',\n      'border',\n      'cellPadding',\n      'cellSpacing',\n      'char',\n      'charOff',\n      'charSet',\n      'checked',\n      'clear',\n      'colSpan',\n      'color',\n      'cols',\n      'compact',\n      'coords',\n      'dateTime',\n      'dir',\n      // Note: `disabled` is technically allowed on all elements by GH.\n      // But it is useless on everything except `input`.\n      // Because `input`s are normally not allowed, but we allow them for\n      // checkboxes due to tasklists, we allow `disabled` only there.\n      'encType',\n      'frame',\n      'hSpace',\n      'headers',\n      'height',\n      'hrefLang',\n      'htmlFor',\n      'id',\n      'isMap',\n      'itemProp',\n      'label',\n      'lang',\n      'maxLength',\n      'media',\n      'method',\n      'multiple',\n      'name',\n      'noHref',\n      'noShade',\n      'noWrap',\n      'open',\n      'prompt',\n      'readOnly',\n      'rev',\n      'rowSpan',\n      'rows',\n      'rules',\n      'scope',\n      'selected',\n      'shape',\n      'size',\n      'span',\n      'start',\n      'summary',\n      'tabIndex',\n      'title',\n      'useMap',\n      'vAlign',\n      'value',\n      'width'\n    ]\n  },\n  clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name'],\n  clobberPrefix: 'user-content-',\n  protocols: {\n    cite: ['http', 'https'],\n    href: ['http', 'https', 'irc', 'ircs', 'mailto', 'xmpp'],\n    longDesc: ['http', 'https'],\n    src: ['http', 'https']\n  },\n  required: {\n    input: {disabled: true, type: 'checkbox'}\n  },\n  strip: ['script'],\n  tagNames: [\n    'a',\n    'b',\n    'blockquote',\n    'br',\n    'code',\n    'dd',\n    'del',\n    'details',\n    'div',\n    'dl',\n    'dt',\n    'em',\n    'h1',\n    'h2',\n    'h3',\n    'h4',\n    'h5',\n    'h6',\n    'hr',\n    'i',\n    'img',\n    // Note: `input` is not normally allowed by GH, when manually writing\n    // it in markdown, they add it from tasklists some other way.\n    // We can’t do that, so we have to allow it.\n    'input',\n    'ins',\n    'kbd',\n    'li',\n    'ol',\n    'p',\n    'picture',\n    'pre',\n    'q',\n    'rp',\n    'rt',\n    'ruby',\n    's',\n    'samp',\n    'section',\n    'source',\n    'span',\n    'strike',\n    'strong',\n    'sub',\n    'summary',\n    'sup',\n    'table',\n    'tbody',\n    'td',\n    'tfoot',\n    'th',\n    'thead',\n    'tr',\n    'tt',\n    'ul',\n    'var'\n  ]\n}\n", "/**\n * @import {\n *   Comment,\n *   Doctype,\n *   ElementContent,\n *   Element,\n *   Nodes,\n *   Properties,\n *   RootContent,\n *   Root,\n *   Text\n * } from 'hast'\n */\n\n/**\n * @typedef {[string, ...Array<Exclude<Properties[keyof Properties], Array<any>> | RegExp>] | string} PropertyDefinition\n *   Definition for a property.\n *\n * @typedef Schema\n *   Schema that defines what nodes and properties are allowed.\n *\n *   The default schema is `defaultSchema`, which follows how GitHub cleans.\n *   If any top-level key is missing in the given schema, the corresponding\n *   value of the default schema is used.\n *\n *   To extend the standard schema with a few changes, clone `defaultSchema`\n *   like so:\n *\n *   ```js\n *   import deepmerge from 'deepmerge'\n *   import {h} from 'hastscript'\n *   import {defaultSchema, sanitize} from 'hast-util-sanitize'\n *\n *   // This allows `className` on all elements.\n *   const schema = deepmerge(defaultSchema, {attributes: {'*': ['className']}})\n *\n *   const tree = sanitize(h('div', {className: ['foo']}), schema)\n *\n *   // `tree` still has `className`.\n *   console.log(tree)\n *   // {\n *   //   type: 'element',\n *   //   tagName: 'div',\n *   //   properties: {className: ['foo']},\n *   //   children: []\n *   // }\n *   ```\n * @property {boolean | null | undefined} [allowComments=false]\n *   Whether to allow comment nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowComments: true\n *   ```\n * @property {boolean | null | undefined} [allowDoctypes=false]\n *   Whether to allow doctype nodes (default: `false`).\n *\n *   For example:\n *\n *   ```js\n *   allowDoctypes: true\n *   ```\n * @property {Record<string, Array<string>> | null | undefined} [ancestors]\n *   Map of tag names to a list of tag names which are required ancestors\n *   (default: `defaultSchema.ancestors`).\n *\n *   Elements with these tag names will be ignored if they occur outside of one\n *   of their allowed parents.\n *\n *   For example:\n *\n *   ```js\n *   ancestors: {\n *     tbody: ['table'],\n *     // …\n *     tr: ['table']\n *   }\n *   ```\n * @property {Record<string, Array<PropertyDefinition>> | null | undefined} [attributes]\n *   Map of tag names to allowed property names (default:\n *   `defaultSchema.attributes`).\n *\n *   The special key `'*'` as a tag name defines property names allowed on all\n *   elements.\n *\n *   The special value `'data*'` as a property name can be used to allow all\n *   `data` properties.\n *\n *   For example:\n *\n *   ```js\n *   attributes: {\n *     'ariaDescribedBy', 'ariaLabel', 'ariaLabelledBy', …, 'href'\n *     // …\n *     '*': [\n *       'abbr',\n *       'accept',\n *       'acceptCharset',\n *       // …\n *       'vAlign',\n *       'value',\n *       'width'\n *     ]\n *   }\n *   ```\n *\n *   Instead of a single string in the array, which allows any property value\n *   for the field, you can use an array to allow several values.\n *   For example, `input: ['type']` allows `type` set to any value on `input`s.\n *   But `input: [['type', 'checkbox', 'radio']]` allows `type` when set to\n *   `'checkbox'` or `'radio'`.\n *\n *   You can use regexes, so for example `span: [['className', /^hljs-/]]`\n *   allows any class that starts with `hljs-` on `span`s.\n *\n *   When comma- or space-separated values are used (such as `className`), each\n *   value in is checked individually.\n *   For example, to allow certain classes on `span`s for syntax highlighting,\n *   use `span: [['className', 'number', 'operator', 'token']]`.\n *   This will allow `'number'`, `'operator'`, and `'token'` classes, but drop\n *   others.\n * @property {Array<string> | null | undefined} [clobber]\n *   List of property names that clobber (default: `defaultSchema.clobber`).\n *\n *   For example:\n *\n *   ```js\n *   clobber: ['ariaDescribedBy', 'ariaLabelledBy', 'id', 'name']\n *   ```\n * @property {string | null | undefined} [clobberPrefix]\n *   Prefix to use before clobbering properties (default:\n *   `defaultSchema.clobberPrefix`).\n *\n *   For example:\n *\n *   ```js\n *   clobberPrefix: 'user-content-'\n *   ```\n * @property {Record<string, Array<string> | null | undefined> | null | undefined} [protocols]\n *   Map of *property names* to allowed protocols (default:\n *   `defaultSchema.protocols`).\n *\n *   This defines URLs that are always allowed to have local URLs (relative to\n *   the current website, such as `this`, `#this`, `/this`, or `?this`), and\n *   only allowed to have remote URLs (such as `https://example.com`) if they\n *   use a known protocol.\n *\n *   For example:\n *\n *   ```js\n *   protocols: {\n *     cite: ['http', 'https'],\n *     // …\n *     src: ['http', 'https']\n *   }\n *   ```\n * @property {Record<string, Record<string, Properties[keyof Properties]>> | null | undefined} [required]\n *   Map of tag names to required property names with a default value\n *   (default: `defaultSchema.required`).\n *\n *   This defines properties that must be set.\n *   If a field does not exist (after the element was made safe), these will be\n *   added with the given value.\n *\n *   For example:\n *\n *   ```js\n *   required: {\n *     input: {disabled: true, type: 'checkbox'}\n *   }\n *   ```\n *\n *   > 👉 **Note**: properties are first checked based on `schema.attributes`,\n *   > then on `schema.required`.\n *   > That means properties could be removed by `attributes` and then added\n *   > again with `required`.\n * @property {Array<string> | null | undefined} [strip]\n *   List of tag names to strip from the tree (default: `defaultSchema.strip`).\n *\n *   By default, unsafe elements (those not in `schema.tagNames`) are replaced\n *   by what they contain.\n *   This option can drop their contents.\n *\n *   For example:\n *\n *   ```js\n *   strip: ['script']\n *   ```\n * @property {Array<string> | null | undefined} [tagNames]\n *   List of allowed tag names (default: `defaultSchema.tagNames`).\n *\n *   For example:\n *\n *   ```js\n *   tagNames: [\n *     'a',\n *     'b',\n *     // …\n *     'ul',\n *     'var'\n *   ]\n *   ```\n *\n * @typedef State\n *   Info passed around.\n * @property {Readonly<Schema>} schema\n *   Schema.\n * @property {Array<string>} stack\n *   Tag names of ancestors.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {position} from 'unist-util-position'\nimport {defaultSchema} from './schema.js'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Sanitize a tree.\n *\n * @param {Readonly<Nodes>} node\n *   Unsafe tree.\n * @param {Readonly<Schema> | null | undefined} [options]\n *   Configuration (default: `defaultSchema`).\n * @returns {Nodes}\n *   New, safe tree.\n */\nexport function sanitize(node, options) {\n  /** @type {Nodes} */\n  let result = {type: 'root', children: []}\n\n  /** @type {State} */\n  const state = {\n    schema: options ? {...defaultSchema, ...options} : defaultSchema,\n    stack: []\n  }\n  const replace = transform(state, node)\n\n  if (replace) {\n    if (Array.isArray(replace)) {\n      if (replace.length === 1) {\n        result = replace[0]\n      } else {\n        result.children = replace\n      }\n    } else {\n      result = replace\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize `node`.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} node\n *   Unsafe node.\n * @returns {Array<ElementContent> | Nodes | undefined}\n *   Safe result.\n */\nfunction transform(state, node) {\n  if (node && typeof node === 'object') {\n    const unsafe = /** @type {Record<string, Readonly<unknown>>} */ (node)\n    const type = typeof unsafe.type === 'string' ? unsafe.type : ''\n\n    switch (type) {\n      case 'comment': {\n        return comment(state, unsafe)\n      }\n\n      case 'doctype': {\n        return doctype(state, unsafe)\n      }\n\n      case 'element': {\n        return element(state, unsafe)\n      }\n\n      case 'root': {\n        return root(state, unsafe)\n      }\n\n      case 'text': {\n        return text(state, unsafe)\n      }\n\n      default:\n    }\n  }\n}\n\n/**\n * Make a safe comment.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe comment-like value.\n * @returns {Comment | undefined}\n *   Safe comment (if with `allowComments`).\n */\nfunction comment(state, unsafe) {\n  if (state.schema.allowComments) {\n    // See <https://html.spec.whatwg.org/multipage/parsing.html#serialising-html-fragments>\n    const result = typeof unsafe.value === 'string' ? unsafe.value : ''\n    const index = result.indexOf('-->')\n    const value = index < 0 ? result : result.slice(0, index)\n\n    /** @type {Comment} */\n    const node = {type: 'comment', value}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe doctype.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe doctype-like value.\n * @returns {Doctype | undefined}\n *   Safe doctype (if with `allowDoctypes`).\n */\nfunction doctype(state, unsafe) {\n  if (state.schema.allowDoctypes) {\n    /** @type {Doctype} */\n    const node = {type: 'doctype'}\n\n    patch(node, unsafe)\n\n    return node\n  }\n}\n\n/**\n * Make a safe element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe element-like value.\n * @returns {Array<ElementContent> | Element | undefined}\n *   Safe element.\n */\nfunction element(state, unsafe) {\n  const name = typeof unsafe.tagName === 'string' ? unsafe.tagName : ''\n\n  state.stack.push(name)\n\n  const content = /** @type {Array<ElementContent>} */ (\n    children(state, unsafe.children)\n  )\n  const properties_ = properties(state, unsafe.properties)\n\n  state.stack.pop()\n\n  let safeElement = false\n\n  if (\n    name &&\n    name !== '*' &&\n    (!state.schema.tagNames || state.schema.tagNames.includes(name))\n  ) {\n    safeElement = true\n\n    // Some nodes can break out of their context if they don’t have a certain\n    // ancestor.\n    if (state.schema.ancestors && own.call(state.schema.ancestors, name)) {\n      const ancestors = state.schema.ancestors[name]\n      let index = -1\n\n      safeElement = false\n\n      while (++index < ancestors.length) {\n        if (state.stack.includes(ancestors[index])) {\n          safeElement = true\n        }\n      }\n    }\n  }\n\n  if (!safeElement) {\n    return state.schema.strip && !state.schema.strip.includes(name)\n      ? content\n      : undefined\n  }\n\n  /** @type {Element} */\n  const node = {\n    type: 'element',\n    tagName: name,\n    properties: properties_,\n    children: content\n  }\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe root-like value.\n * @returns {Root}\n *   Safe root.\n */\nfunction root(state, unsafe) {\n  const content = /** @type {Array<RootContent>} */ (\n    children(state, unsafe.children)\n  )\n\n  /** @type {Root} */\n  const node = {type: 'root', children: content}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make a safe text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe text-like value.\n * @returns {Text}\n *   Safe text.\n */\nfunction text(_, unsafe) {\n  const value = typeof unsafe.value === 'string' ? unsafe.value : ''\n  /** @type {Text} */\n  const node = {type: 'text', value}\n\n  patch(node, unsafe)\n\n  return node\n}\n\n/**\n * Make children safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} children\n *   Unsafe value.\n * @returns {Array<Nodes>}\n *   Safe children.\n */\nfunction children(state, children) {\n  /** @type {Array<Nodes>} */\n  const results = []\n\n  if (Array.isArray(children)) {\n    const childrenUnknown = /** @type {Array<Readonly<unknown>>} */ (children)\n    let index = -1\n\n    while (++index < childrenUnknown.length) {\n      const value = transform(state, childrenUnknown[index])\n\n      if (value) {\n        if (Array.isArray(value)) {\n          results.push(...value)\n        } else {\n          results.push(value)\n        }\n      }\n    }\n  }\n\n  return results\n}\n\n/**\n * Make element properties safe.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<unknown>} properties\n *   Unsafe value.\n * @returns {Properties}\n *   Safe value.\n */\nfunction properties(state, properties) {\n  const tagName = state.stack[state.stack.length - 1]\n  const attributes = state.schema.attributes\n  const required = state.schema.required\n  const specific =\n    attributes && own.call(attributes, tagName)\n      ? attributes[tagName]\n      : undefined\n  const defaults =\n    attributes && own.call(attributes, '*') ? attributes['*'] : undefined\n  const properties_ =\n    /** @type {Readonly<Record<string, Readonly<unknown>>>} */ (\n      properties && typeof properties === 'object' ? properties : {}\n    )\n  /** @type {Properties} */\n  const result = {}\n  /** @type {string} */\n  let key\n\n  for (key in properties_) {\n    if (own.call(properties_, key)) {\n      const unsafe = properties_[key]\n      let safe = propertyValue(\n        state,\n        findDefinition(specific, key),\n        key,\n        unsafe\n      )\n\n      if (safe === null || safe === undefined) {\n        safe = propertyValue(state, findDefinition(defaults, key), key, unsafe)\n      }\n\n      if (safe !== null && safe !== undefined) {\n        result[key] = safe\n      }\n    }\n  }\n\n  if (required && own.call(required, tagName)) {\n    const properties = required[tagName]\n\n    for (key in properties) {\n      if (own.call(properties, key) && !own.call(result, key)) {\n        result[key] = properties[key]\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition> | undefined} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but an array).\n * @returns {Array<number | string> | boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValue(state, definition, key, value) {\n  return definition\n    ? Array.isArray(value)\n      ? propertyValueMany(state, definition, key, value)\n      : propertyValuePrimitive(state, definition, key, value)\n    : undefined\n}\n\n/**\n * Sanitize a property value which is a list.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<Array<Readonly<unknown>>>} values\n *   Unsafe value (but an array).\n * @returns {Array<number | string>}\n *   Safe value.\n */\nfunction propertyValueMany(state, definition, key, values) {\n  let index = -1\n  /** @type {Array<number | string>} */\n  const result = []\n\n  while (++index < values.length) {\n    const value = propertyValuePrimitive(state, definition, key, values[index])\n\n    if (typeof value === 'number' || typeof value === 'string') {\n      result.push(value)\n    }\n  }\n\n  return result\n}\n\n/**\n * Sanitize a property value which is a primitive.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Readonly<PropertyDefinition>} definition\n *   Definition.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value (but not an array).\n * @returns {boolean | number | string | undefined}\n *   Safe value.\n */\nfunction propertyValuePrimitive(state, definition, key, value) {\n  if (\n    typeof value !== 'boolean' &&\n    typeof value !== 'number' &&\n    typeof value !== 'string'\n  ) {\n    return\n  }\n\n  if (!safeProtocol(state, key, value)) {\n    return\n  }\n\n  // Just a string, or only one item in an array, means all values are OK.\n  // More than one item means an allow list.\n  if (typeof definition === 'object' && definition.length > 1) {\n    let ok = false\n    let index = 0 // Ignore `key`, which is the first item.\n\n    while (++index < definition.length) {\n      const allowed = definition[index]\n\n      // Expression.\n      if (allowed && typeof allowed === 'object' && 'flags' in allowed) {\n        if (allowed.test(String(value))) {\n          ok = true\n          break\n        }\n      }\n      // Primitive.\n      else if (allowed === value) {\n        ok = true\n        break\n      }\n    }\n\n    if (!ok) return\n  }\n\n  return state.schema.clobber &&\n    state.schema.clobberPrefix &&\n    state.schema.clobber.includes(key)\n    ? state.schema.clobberPrefix + value\n    : value\n}\n\n/**\n * Check whether `value` is a safe URL.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} key\n *   Field name.\n * @param {Readonly<unknown>} value\n *   Unsafe value.\n * @returns {boolean}\n *   Whether it’s a safe value.\n */\nfunction safeProtocol(state, key, value) {\n  const protocols =\n    state.schema.protocols && own.call(state.schema.protocols, key)\n      ? state.schema.protocols[key]\n      : undefined\n\n  // No protocols defined? Then everything is fine.\n  if (!protocols || protocols.length === 0) {\n    return true\n  }\n\n  const url = String(value)\n  const colon = url.indexOf(':')\n  const questionMark = url.indexOf('?')\n  const numberSign = url.indexOf('#')\n  const slash = url.indexOf('/')\n\n  if (\n    colon < 0 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash > -1 && colon > slash) ||\n    (questionMark > -1 && colon > questionMark) ||\n    (numberSign > -1 && colon > numberSign)\n  ) {\n    return true\n  }\n\n  let index = -1\n\n  while (++index < protocols.length) {\n    const protocol = protocols[index]\n\n    if (\n      colon === protocol.length &&\n      url.slice(0, protocol.length) === protocol\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Add data and position.\n *\n * @param {Nodes} node\n *   Node to patch safe data and position on.\n * @param {Readonly<Record<string, Readonly<unknown>>>} unsafe\n *   Unsafe node-like value.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(node, unsafe) {\n  const cleanPosition = position(\n    // @ts-expect-error: looks like a node.\n    unsafe\n  )\n\n  if (unsafe.data) {\n    node.data = structuredClone(unsafe.data)\n  }\n\n  if (cleanPosition) node.position = cleanPosition\n}\n\n/**\n *\n * @param {Readonly<Array<PropertyDefinition>> | undefined} definitions\n * @param {string} key\n * @returns {Readonly<PropertyDefinition> | undefined}\n */\nfunction findDefinition(definitions, key) {\n  /** @type {PropertyDefinition | undefined} */\n  let dataDefault\n  let index = -1\n\n  if (definitions) {\n    while (++index < definitions.length) {\n      const entry = definitions[index]\n      const name = typeof entry === 'string' ? entry : entry[0]\n\n      if (name === key) {\n        return entry\n      }\n\n      if (name === 'data*') dataDefault = entry\n    }\n  }\n\n  if (key.length > 4 && key.slice(0, 4).toLowerCase() === 'data') {\n    return dataDefault\n  }\n}\n", "/**\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast-util-sanitize').Schema} Schema\n */\n\nimport {sanitize} from 'hast-util-sanitize'\n\n/**\n * Sanitize HTML.\n *\n * @param {Schema | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nexport default function rehypeSanitize(options) {\n  /**\n   * @param {Root} tree\n   *   Tree.\n   * @returns {Root}\n   *   New tree.\n   */\n  return function (tree) {\n    // Assume root in -> root out.\n    const result = /** @type {Root} */ (sanitize(tree, options))\n    return result\n  }\n}\n"], "mappings": ";;;;;;;AAKA,IAAM,OAAO,CAAC,mBAAmB,aAAa,gBAAgB;AASvD,IAAM,gBAAgB;AAAA,EAC3B,WAAW;AAAA,IACT,OAAO,CAAC,OAAO;AAAA,IACf,IAAI,CAAC,OAAO;AAAA,IACZ,IAAI,CAAC,OAAO;AAAA,IACZ,OAAO,CAAC,OAAO;AAAA,IACf,OAAO,CAAC,OAAO;AAAA,IACf,IAAI,CAAC,OAAO;AAAA,EACd;AAAA,EACA,YAAY;AAAA,IACV,GAAG;AAAA,MACD,GAAG;AAAA;AAAA,MAEH;AAAA,MACA;AAAA,MACA,CAAC,aAAa,uBAAuB;AAAA,MACrC;AAAA,IACF;AAAA,IACA,YAAY,CAAC,MAAM;AAAA;AAAA;AAAA;AAAA,IAInB,MAAM,CAAC,CAAC,aAAa,aAAa,CAAC;AAAA,IACnC,KAAK,CAAC,MAAM;AAAA,IACZ,KAAK,CAAC,aAAa,UAAU;AAAA,IAC7B,IAAI,CAAC,GAAG,IAAI;AAAA;AAAA,IAEZ,IAAI,CAAC,CAAC,aAAa,SAAS,CAAC;AAAA,IAC7B,KAAK,CAAC,GAAG,MAAM,YAAY,KAAK;AAAA;AAAA;AAAA;AAAA,IAIhC,OAAO;AAAA,MACL,CAAC,YAAY,IAAI;AAAA,MACjB,CAAC,QAAQ,UAAU;AAAA,IACrB;AAAA,IACA,KAAK,CAAC,MAAM;AAAA;AAAA;AAAA;AAAA,IAIZ,IAAI,CAAC,CAAC,aAAa,gBAAgB,CAAC;AAAA;AAAA;AAAA;AAAA,IAIpC,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,oBAAoB,CAAC;AAAA,IACjD,GAAG,CAAC,MAAM;AAAA,IACV,SAAS,CAAC,iBAAiB,CAAC,aAAa,WAAW,CAAC;AAAA,IACrD,QAAQ,CAAC,QAAQ;AAAA,IACjB,SAAS,CAAC,GAAG,IAAI;AAAA,IACjB,OAAO,CAAC,GAAG,IAAI;AAAA;AAAA;AAAA;AAAA,IAIf,IAAI,CAAC,GAAG,MAAM,CAAC,aAAa,oBAAoB,CAAC;AAAA,IACjD,KAAK;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA;AAAA;AAAA;AAAA,MAKA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,SAAS,CAAC,mBAAmB,kBAAkB,MAAM,MAAM;AAAA,EAC3D,eAAe;AAAA,EACf,WAAW;AAAA,IACT,MAAM,CAAC,QAAQ,OAAO;AAAA,IACtB,MAAM,CAAC,QAAQ,SAAS,OAAO,QAAQ,UAAU,MAAM;AAAA,IACvD,UAAU,CAAC,QAAQ,OAAO;AAAA,IAC1B,KAAK,CAAC,QAAQ,OAAO;AAAA,EACvB;AAAA,EACA,UAAU;AAAA,IACR,OAAO,EAAC,UAAU,MAAM,MAAM,WAAU;AAAA,EAC1C;AAAA,EACA,OAAO,CAAC,QAAQ;AAAA,EAChB,UAAU;AAAA,IACR;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAIA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;ACQA,IAAM,MAAM,CAAC,EAAE;AAYR,SAAS,SAAS,MAAM,SAAS;AAEtC,MAAI,SAAS,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAGxC,QAAM,QAAQ;AAAA,IACZ,QAAQ,UAAU,EAAC,GAAG,eAAe,GAAG,QAAO,IAAI;AAAA,IACnD,OAAO,CAAC;AAAA,EACV;AACA,QAAM,UAAU,UAAU,OAAO,IAAI;AAErC,MAAI,SAAS;AACX,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,UAAI,QAAQ,WAAW,GAAG;AACxB,iBAAS,QAAQ,CAAC;AAAA,MACpB,OAAO;AACL,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,OAAO;AACL,eAAS;AAAA,IACX;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,UAAU,OAAO,MAAM;AAC9B,MAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,UAAM;AAAA;AAAA,MAA2D;AAAA;AACjE,UAAM,OAAO,OAAO,OAAO,SAAS,WAAW,OAAO,OAAO;AAE7D,YAAQ,MAAM;AAAA,MACZ,KAAK,WAAW;AACd,eAAO,QAAQ,OAAO,MAAM;AAAA,MAC9B;AAAA,MAEA,KAAK,WAAW;AACd,eAAO,QAAQ,OAAO,MAAM;AAAA,MAC9B;AAAA,MAEA,KAAK,WAAW;AACd,eAAO,QAAQ,OAAO,MAAM;AAAA,MAC9B;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO,KAAK,OAAO,MAAM;AAAA,MAC3B;AAAA,MAEA,KAAK,QAAQ;AACX,eAAO,KAAK,OAAO,MAAM;AAAA,MAC3B;AAAA,MAEA;AAAA,IACF;AAAA,EACF;AACF;AAYA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,MAAI,MAAM,OAAO,eAAe;AAE9B,UAAM,SAAS,OAAO,OAAO,UAAU,WAAW,OAAO,QAAQ;AACjE,UAAM,QAAQ,OAAO,QAAQ,KAAK;AAClC,UAAM,QAAQ,QAAQ,IAAI,SAAS,OAAO,MAAM,GAAG,KAAK;AAGxD,UAAM,OAAO,EAAC,MAAM,WAAW,MAAK;AAEpC,UAAM,MAAM,MAAM;AAElB,WAAO;AAAA,EACT;AACF;AAYA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,MAAI,MAAM,OAAO,eAAe;AAE9B,UAAM,OAAO,EAAC,MAAM,UAAS;AAE7B,UAAM,MAAM,MAAM;AAElB,WAAO;AAAA,EACT;AACF;AAYA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,QAAM,OAAO,OAAO,OAAO,YAAY,WAAW,OAAO,UAAU;AAEnE,QAAM,MAAM,KAAK,IAAI;AAErB,QAAM;AAAA;AAAA,IACJ,SAAS,OAAO,OAAO,QAAQ;AAAA;AAEjC,QAAM,cAAc,WAAW,OAAO,OAAO,UAAU;AAEvD,QAAM,MAAM,IAAI;AAEhB,MAAI,cAAc;AAElB,MACE,QACA,SAAS,QACR,CAAC,MAAM,OAAO,YAAY,MAAM,OAAO,SAAS,SAAS,IAAI,IAC9D;AACA,kBAAc;AAId,QAAI,MAAM,OAAO,aAAa,IAAI,KAAK,MAAM,OAAO,WAAW,IAAI,GAAG;AACpE,YAAM,YAAY,MAAM,OAAO,UAAU,IAAI;AAC7C,UAAI,QAAQ;AAEZ,oBAAc;AAEd,aAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,YAAI,MAAM,MAAM,SAAS,UAAU,KAAK,CAAC,GAAG;AAC1C,wBAAc;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,CAAC,aAAa;AAChB,WAAO,MAAM,OAAO,SAAS,CAAC,MAAM,OAAO,MAAM,SAAS,IAAI,IAC1D,UACA;AAAA,EACN;AAGA,QAAM,OAAO;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ;AAEA,QAAM,MAAM,MAAM;AAElB,SAAO;AACT;AAYA,SAAS,KAAK,OAAO,QAAQ;AAC3B,QAAM;AAAA;AAAA,IACJ,SAAS,OAAO,OAAO,QAAQ;AAAA;AAIjC,QAAM,OAAO,EAAC,MAAM,QAAQ,UAAU,QAAO;AAE7C,QAAM,MAAM,MAAM;AAElB,SAAO;AACT;AAYA,SAAS,KAAK,GAAG,QAAQ;AACvB,QAAM,QAAQ,OAAO,OAAO,UAAU,WAAW,OAAO,QAAQ;AAEhE,QAAM,OAAO,EAAC,MAAM,QAAQ,MAAK;AAEjC,QAAM,MAAM,MAAM;AAElB,SAAO;AACT;AAYA,SAAS,SAAS,OAAOA,WAAU;AAEjC,QAAM,UAAU,CAAC;AAEjB,MAAI,MAAM,QAAQA,SAAQ,GAAG;AAC3B,UAAM;AAAA;AAAA,MAA2DA;AAAA;AACjE,QAAI,QAAQ;AAEZ,WAAO,EAAE,QAAQ,gBAAgB,QAAQ;AACvC,YAAM,QAAQ,UAAU,OAAO,gBAAgB,KAAK,CAAC;AAErD,UAAI,OAAO;AACT,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,kBAAQ,KAAK,GAAG,KAAK;AAAA,QACvB,OAAO;AACL,kBAAQ,KAAK,KAAK;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,WAAW,OAAOC,aAAY;AACrC,QAAM,UAAU,MAAM,MAAM,MAAM,MAAM,SAAS,CAAC;AAClD,QAAM,aAAa,MAAM,OAAO;AAChC,QAAM,WAAW,MAAM,OAAO;AAC9B,QAAM,WACJ,cAAc,IAAI,KAAK,YAAY,OAAO,IACtC,WAAW,OAAO,IAClB;AACN,QAAM,WACJ,cAAc,IAAI,KAAK,YAAY,GAAG,IAAI,WAAW,GAAG,IAAI;AAC9D,QAAM;AAAA;AAAA,IAEFA,eAAc,OAAOA,gBAAe,WAAWA,cAAa,CAAC;AAAA;AAGjE,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,OAAK,OAAO,aAAa;AACvB,QAAI,IAAI,KAAK,aAAa,GAAG,GAAG;AAC9B,YAAM,SAAS,YAAY,GAAG;AAC9B,UAAI,OAAO;AAAA,QACT;AAAA,QACA,eAAe,UAAU,GAAG;AAAA,QAC5B;AAAA,QACA;AAAA,MACF;AAEA,UAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,eAAO,cAAc,OAAO,eAAe,UAAU,GAAG,GAAG,KAAK,MAAM;AAAA,MACxE;AAEA,UAAI,SAAS,QAAQ,SAAS,QAAW;AACvC,eAAO,GAAG,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,YAAY,IAAI,KAAK,UAAU,OAAO,GAAG;AAC3C,UAAMA,cAAa,SAAS,OAAO;AAEnC,SAAK,OAAOA,aAAY;AACtB,UAAI,IAAI,KAAKA,aAAY,GAAG,KAAK,CAAC,IAAI,KAAK,QAAQ,GAAG,GAAG;AACvD,eAAO,GAAG,IAAIA,YAAW,GAAG;AAAA,MAC9B;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAgBA,SAAS,cAAc,OAAO,YAAY,KAAK,OAAO;AACpD,SAAO,aACH,MAAM,QAAQ,KAAK,IACjB,kBAAkB,OAAO,YAAY,KAAK,KAAK,IAC/C,uBAAuB,OAAO,YAAY,KAAK,KAAK,IACtD;AACN;AAgBA,SAAS,kBAAkB,OAAO,YAAY,KAAK,QAAQ;AACzD,MAAI,QAAQ;AAEZ,QAAM,SAAS,CAAC;AAEhB,SAAO,EAAE,QAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,uBAAuB,OAAO,YAAY,KAAK,OAAO,KAAK,CAAC;AAE1E,QAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAC1D,aAAO,KAAK,KAAK;AAAA,IACnB;AAAA,EACF;AAEA,SAAO;AACT;AAgBA,SAAS,uBAAuB,OAAO,YAAY,KAAK,OAAO;AAC7D,MACE,OAAO,UAAU,aACjB,OAAO,UAAU,YACjB,OAAO,UAAU,UACjB;AACA;AAAA,EACF;AAEA,MAAI,CAAC,aAAa,OAAO,KAAK,KAAK,GAAG;AACpC;AAAA,EACF;AAIA,MAAI,OAAO,eAAe,YAAY,WAAW,SAAS,GAAG;AAC3D,QAAI,KAAK;AACT,QAAI,QAAQ;AAEZ,WAAO,EAAE,QAAQ,WAAW,QAAQ;AAClC,YAAM,UAAU,WAAW,KAAK;AAGhC,UAAI,WAAW,OAAO,YAAY,YAAY,WAAW,SAAS;AAChE,YAAI,QAAQ,KAAK,OAAO,KAAK,CAAC,GAAG;AAC/B,eAAK;AACL;AAAA,QACF;AAAA,MACF,WAES,YAAY,OAAO;AAC1B,aAAK;AACL;AAAA,MACF;AAAA,IACF;AAEA,QAAI,CAAC;AAAI;AAAA,EACX;AAEA,SAAO,MAAM,OAAO,WAClB,MAAM,OAAO,iBACb,MAAM,OAAO,QAAQ,SAAS,GAAG,IAC/B,MAAM,OAAO,gBAAgB,QAC7B;AACN;AAcA,SAAS,aAAa,OAAO,KAAK,OAAO;AACvC,QAAM,YACJ,MAAM,OAAO,aAAa,IAAI,KAAK,MAAM,OAAO,WAAW,GAAG,IAC1D,MAAM,OAAO,UAAU,GAAG,IAC1B;AAGN,MAAI,CAAC,aAAa,UAAU,WAAW,GAAG;AACxC,WAAO;AAAA,EACT;AAEA,QAAM,MAAM,OAAO,KAAK;AACxB,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,QAAM,eAAe,IAAI,QAAQ,GAAG;AACpC,QAAM,aAAa,IAAI,QAAQ,GAAG;AAClC,QAAM,QAAQ,IAAI,QAAQ,GAAG;AAE7B,MACE,QAAQ;AAAA,EAEP,QAAQ,MAAM,QAAQ,SACtB,eAAe,MAAM,QAAQ,gBAC7B,aAAa,MAAM,QAAQ,YAC5B;AACA,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,UAAU,QAAQ;AACjC,UAAM,WAAW,UAAU,KAAK;AAEhC,QACE,UAAU,SAAS,UACnB,IAAI,MAAM,GAAG,SAAS,MAAM,MAAM,UAClC;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,MAAM,MAAM,QAAQ;AAC3B,QAAM,gBAAgB;AAAA;AAAA,IAEpB;AAAA,EACF;AAEA,MAAI,OAAO,MAAM;AACf,SAAK,OAAO,YAAgB,OAAO,IAAI;AAAA,EACzC;AAEA,MAAI;AAAe,SAAK,WAAW;AACrC;AAQA,SAAS,eAAe,aAAa,KAAK;AAExC,MAAI;AACJ,MAAI,QAAQ;AAEZ,MAAI,aAAa;AACf,WAAO,EAAE,QAAQ,YAAY,QAAQ;AACnC,YAAM,QAAQ,YAAY,KAAK;AAC/B,YAAM,OAAO,OAAO,UAAU,WAAW,QAAQ,MAAM,CAAC;AAExD,UAAI,SAAS,KAAK;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,SAAS;AAAS,sBAAc;AAAA,IACtC;AAAA,EACF;AAEA,MAAI,IAAI,SAAS,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,YAAY,MAAM,QAAQ;AAC9D,WAAO;AAAA,EACT;AACF;;;AC7uBe,SAAR,eAAgC,SAAS;AAO9C,SAAO,SAAU,MAAM;AAErB,UAAM;AAAA;AAAA,MAA8B,SAAS,MAAM,OAAO;AAAA;AAC1D,WAAO;AAAA,EACT;AACF;", "names": ["children", "properties"]}