import {
  __commonJS,
  __esm,
  __export,
  __publicField,
  __toCommonJS
} from "./chunk-C3M7BXFS.js";

// node_modules/@daily-co/daily-js/dist/daily-esm.js
var daily_esm_exports = {};
__export(daily_esm_exports, {
  DAILY_ACCESS_LEVEL_FULL: () => Pi,
  DAILY_ACCESS_LEVEL_LOBBY: () => Ai,
  DAILY_ACCESS_LEVEL_NONE: () => ji,
  DAILY_ACCESS_UNKNOWN: () => Oi,
  DAILY_CAMERA_ERROR_CAM_AND_MIC_IN_USE: () => Wi,
  DAILY_CAMERA_ERROR_CAM_IN_USE: () => qi,
  DAILY_CAMERA_ERROR_CONSTRAINTS: () => Ki,
  DAILY_CAMERA_ERROR_MIC_IN_USE: () => zi,
  DAILY_CAMERA_ERROR_NOT_FOUND: () => Qi,
  DA<PERSON>Y_CAMERA_ERROR_PERMISSIONS: () => Hi,
  DAILY_CAMERA_ERROR_UNDEF_MEDIADEVICES: () => Gi,
  DAILY_CAMERA_ERROR_UNKNOWN: () => Yi,
  DAILY_EVENT_ACCESS_STATE_UPDATED: () => fo,
  DAILY_EVENT_ACTIVE_SPEAKER_CHANGE: () => Uo,
  DAILY_EVENT_ACTIVE_SPEAKER_MODE_CHANGE: () => Vo,
  DAILY_EVENT_APP_MSG: () => Io,
  DAILY_EVENT_CAMERA_ERROR: () => oo,
  DAILY_EVENT_CPU_LOAD_CHANGE: () => qo,
  DAILY_EVENT_ERROR: () => ns,
  DAILY_EVENT_EXIT_FULLSCREEN: () => Ho,
  DAILY_EVENT_FACE_COUNTS_UPDATED: () => zo,
  DAILY_EVENT_FULLSCREEN: () => Wo,
  DAILY_EVENT_IFRAME_LAUNCH_CONFIG: () => Zi,
  DAILY_EVENT_IFRAME_READY_FOR_LAUNCH_CONFIG: () => Xi,
  DAILY_EVENT_INPUT_SETTINGS_UPDATED: () => es,
  DAILY_EVENT_JOINED_MEETING: () => ao,
  DAILY_EVENT_JOINING_MEETING: () => so,
  DAILY_EVENT_LANG_UPDATED: () => Xo,
  DAILY_EVENT_LEFT_MEETING: () => co,
  DAILY_EVENT_LIVE_STREAMING_ERROR: () => Yo,
  DAILY_EVENT_LIVE_STREAMING_STARTED: () => Go,
  DAILY_EVENT_LIVE_STREAMING_STOPPED: () => Ko,
  DAILY_EVENT_LIVE_STREAMING_UPDATED: () => Qo,
  DAILY_EVENT_LOADED: () => ro,
  DAILY_EVENT_LOADING: () => to,
  DAILY_EVENT_LOAD_ATTEMPT_FAILED: () => no,
  DAILY_EVENT_LOCAL_SCREEN_SHARE_CANCELED: () => Bo,
  DAILY_EVENT_LOCAL_SCREEN_SHARE_STARTED: () => Ro,
  DAILY_EVENT_LOCAL_SCREEN_SHARE_STOPPED: () => Fo,
  DAILY_EVENT_MEETING_SESSION_DATA_ERROR: () => mo,
  DAILY_EVENT_MEETING_SESSION_STATE_UPDATED: () => go,
  DAILY_EVENT_MEETING_SESSION_SUMMARY_UPDATED: () => vo,
  DAILY_EVENT_NETWORK_CONNECTION: () => $o,
  DAILY_EVENT_NETWORK_QUALITY_CHANGE: () => Jo,
  DAILY_EVENT_NONFATAL_ERROR: () => ts,
  DAILY_EVENT_PARTICIPANT_COUNTS_UPDATED: () => po,
  DAILY_EVENT_PARTICIPANT_JOINED: () => lo,
  DAILY_EVENT_PARTICIPANT_LEFT: () => ho,
  DAILY_EVENT_PARTICIPANT_UPDATED: () => uo,
  DAILY_EVENT_RECEIVE_SETTINGS_UPDATED: () => Zo,
  DAILY_EVENT_RECORDING_DATA: () => jo,
  DAILY_EVENT_RECORDING_ERROR: () => Po,
  DAILY_EVENT_RECORDING_STARTED: () => Eo,
  DAILY_EVENT_RECORDING_STATS: () => Oo,
  DAILY_EVENT_RECORDING_STOPPED: () => To,
  DAILY_EVENT_RECORDING_UPLOAD_COMPLETED: () => Ao,
  DAILY_EVENT_REMOTE_MEDIA_PLAYER_STARTED: () => Lo,
  DAILY_EVENT_REMOTE_MEDIA_PLAYER_STOPPED: () => No,
  DAILY_EVENT_REMOTE_MEDIA_PLAYER_UPDATED: () => Do,
  DAILY_EVENT_STARTED_CAMERA: () => io,
  DAILY_EVENT_THEME_UPDATED: () => eo,
  DAILY_EVENT_TRACK_STARTED: () => wo,
  DAILY_EVENT_TRACK_STOPPED: () => So,
  DAILY_EVENT_TRANSCRIPTION_ERROR: () => Co,
  DAILY_EVENT_TRANSCRIPTION_MSG: () => xo,
  DAILY_EVENT_TRANSCRIPTION_STARTED: () => ko,
  DAILY_EVENT_TRANSCRIPTION_STOPPED: () => Mo,
  DAILY_EVENT_WAITING_PARTICIPANT_ADDED: () => yo,
  DAILY_EVENT_WAITING_PARTICIPANT_REMOVED: () => _o,
  DAILY_EVENT_WAITING_PARTICIPANT_UPDATED: () => bo,
  DAILY_FATAL_ERROR_CONNECTION: () => $i,
  DAILY_FATAL_ERROR_EJECTED: () => Li,
  DAILY_FATAL_ERROR_EOL: () => Vi,
  DAILY_FATAL_ERROR_EXP_ROOM: () => Ri,
  DAILY_FATAL_ERROR_EXP_TOKEN: () => Fi,
  DAILY_FATAL_ERROR_MEETING_FULL: () => Ui,
  DAILY_FATAL_ERROR_NBF_ROOM: () => Di,
  DAILY_FATAL_ERROR_NBF_TOKEN: () => Ni,
  DAILY_FATAL_ERROR_NOT_ALLOWED: () => Ji,
  DAILY_FATAL_ERROR_NO_ROOM: () => Bi,
  DAILY_RECEIVE_SETTINGS_ALL_PARTICIPANTS_KEY: () => xi,
  DAILY_RECEIVE_SETTINGS_BASE_KEY: () => Ii,
  DAILY_STATE_ERROR: () => wi,
  DAILY_STATE_JOINED: () => bi,
  DAILY_STATE_JOINING: () => yi,
  DAILY_STATE_LEFT: () => _i,
  DAILY_STATE_NEW: () => vi,
  DAILY_TRACK_STATE_BLOCKED: () => Si,
  DAILY_TRACK_STATE_INTERRUPTED: () => Ei,
  DAILY_TRACK_STATE_LOADING: () => Ci,
  DAILY_TRACK_STATE_OFF: () => ki,
  DAILY_TRACK_STATE_PLAYABLE: () => Ti,
  DAILY_TRACK_STATE_SENDABLE: () => Mi,
  default: () => ac
});
function e(e2, t2) {
  if (null == e2)
    return {};
  var n2, r2, i2 = function(e3, t3) {
    if (null == e3)
      return {};
    var n3 = {};
    for (var r3 in e3)
      if ({}.hasOwnProperty.call(e3, r3)) {
        if (-1 !== t3.indexOf(r3))
          continue;
        n3[r3] = e3[r3];
      }
    return n3;
  }(e2, t2);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e2);
    for (r2 = 0; r2 < o2.length; r2++)
      n2 = o2[r2], -1 === t2.indexOf(n2) && {}.propertyIsEnumerable.call(e2, n2) && (i2[n2] = e2[n2]);
  }
  return i2;
}
function t(e2, t2) {
  if (!(e2 instanceof t2))
    throw new TypeError("Cannot call a class as a function");
}
function n(e2) {
  return n = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(e3) {
    return typeof e3;
  } : function(e3) {
    return e3 && "function" == typeof Symbol && e3.constructor === Symbol && e3 !== Symbol.prototype ? "symbol" : typeof e3;
  }, n(e2);
}
function r(e2) {
  var t2 = function(e3, t3) {
    if ("object" != n(e3) || !e3)
      return e3;
    var r2 = e3[Symbol.toPrimitive];
    if (void 0 !== r2) {
      var i2 = r2.call(e3, t3 || "default");
      if ("object" != n(i2))
        return i2;
      throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === t3 ? String : Number)(e3);
  }(e2, "string");
  return "symbol" == n(t2) ? t2 : t2 + "";
}
function i(e2, t2) {
  for (var n2 = 0; n2 < t2.length; n2++) {
    var i2 = t2[n2];
    i2.enumerable = i2.enumerable || false, i2.configurable = true, "value" in i2 && (i2.writable = true), Object.defineProperty(e2, r(i2.key), i2);
  }
}
function o(e2, t2, n2) {
  return t2 && i(e2.prototype, t2), n2 && i(e2, n2), Object.defineProperty(e2, "prototype", { writable: false }), e2;
}
function s(e2, t2) {
  if (t2 && ("object" == n(t2) || "function" == typeof t2))
    return t2;
  if (void 0 !== t2)
    throw new TypeError("Derived constructors may only return object or undefined");
  return function(e3) {
    if (void 0 === e3)
      throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
    return e3;
  }(e2);
}
function a(e2) {
  return a = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function(e3) {
    return e3.__proto__ || Object.getPrototypeOf(e3);
  }, a(e2);
}
function c(e2, t2) {
  return c = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function(e3, t3) {
    return e3.__proto__ = t3, e3;
  }, c(e2, t2);
}
function l(e2, t2) {
  if ("function" != typeof t2 && null !== t2)
    throw new TypeError("Super expression must either be null or a function");
  e2.prototype = Object.create(t2 && t2.prototype, { constructor: { value: e2, writable: true, configurable: true } }), Object.defineProperty(e2, "prototype", { writable: false }), t2 && c(e2, t2);
}
function u(e2, t2, n2) {
  return (t2 = r(t2)) in e2 ? Object.defineProperty(e2, t2, { value: n2, enumerable: true, configurable: true, writable: true }) : e2[t2] = n2, e2;
}
function d(e2, t2, n2, r2, i2, o2, s2) {
  try {
    var a2 = e2[o2](s2), c2 = a2.value;
  } catch (e3) {
    return void n2(e3);
  }
  a2.done ? t2(c2) : Promise.resolve(c2).then(r2, i2);
}
function h(e2) {
  return function() {
    var t2 = this, n2 = arguments;
    return new Promise(function(r2, i2) {
      var o2 = e2.apply(t2, n2);
      function s2(e3) {
        d(o2, r2, i2, s2, a2, "next", e3);
      }
      function a2(e3) {
        d(o2, r2, i2, s2, a2, "throw", e3);
      }
      s2(void 0);
    });
  };
}
function p(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++)
    r2[n2] = e2[n2];
  return r2;
}
function f(e2, t2) {
  return function(e3) {
    if (Array.isArray(e3))
      return e3;
  }(e2) || function(e3, t3) {
    var n2 = null == e3 ? null : "undefined" != typeof Symbol && e3[Symbol.iterator] || e3["@@iterator"];
    if (null != n2) {
      var r2, i2, o2, s2, a2 = [], c2 = true, l2 = false;
      try {
        if (o2 = (n2 = n2.call(e3)).next, 0 === t3) {
          if (Object(n2) !== n2)
            return;
          c2 = false;
        } else
          for (; !(c2 = (r2 = o2.call(n2)).done) && (a2.push(r2.value), a2.length !== t3); c2 = true)
            ;
      } catch (e4) {
        l2 = true, i2 = e4;
      } finally {
        try {
          if (!c2 && null != n2.return && (s2 = n2.return(), Object(s2) !== s2))
            return;
        } finally {
          if (l2)
            throw i2;
        }
      }
      return a2;
    }
  }(e2, t2) || function(e3, t3) {
    if (e3) {
      if ("string" == typeof e3)
        return p(e3, t3);
      var n2 = {}.toString.call(e3).slice(8, -1);
      return "Object" === n2 && e3.constructor && (n2 = e3.constructor.name), "Map" === n2 || "Set" === n2 ? Array.from(e3) : "Arguments" === n2 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n2) ? p(e3, t3) : void 0;
    }
  }(e2, t2) || function() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }();
}
function v(e2) {
  return e2 && e2.__esModule && Object.prototype.hasOwnProperty.call(e2, "default") ? e2.default : e2;
}
function w() {
  w.init.call(this);
}
function k(e2) {
  if ("function" != typeof e2)
    throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof e2);
}
function M(e2) {
  return void 0 === e2._maxListeners ? w.defaultMaxListeners : e2._maxListeners;
}
function C(e2, t2, n2, r2) {
  var i2, o2, s2, a2;
  if (k(n2), void 0 === (o2 = e2._events) ? (o2 = e2._events = /* @__PURE__ */ Object.create(null), e2._eventsCount = 0) : (void 0 !== o2.newListener && (e2.emit("newListener", t2, n2.listener ? n2.listener : n2), o2 = e2._events), s2 = o2[t2]), void 0 === s2)
    s2 = o2[t2] = n2, ++e2._eventsCount;
  else if ("function" == typeof s2 ? s2 = o2[t2] = r2 ? [n2, s2] : [s2, n2] : r2 ? s2.unshift(n2) : s2.push(n2), (i2 = M(e2)) > 0 && s2.length > i2 && !s2.warned) {
    s2.warned = true;
    var c2 = new Error("Possible EventEmitter memory leak detected. " + s2.length + " " + String(t2) + " listeners added. Use emitter.setMaxListeners() to increase limit");
    c2.name = "MaxListenersExceededWarning", c2.emitter = e2, c2.type = t2, c2.count = s2.length, a2 = c2, console && console.warn && console.warn(a2);
  }
  return e2;
}
function E() {
  if (!this.fired)
    return this.target.removeListener(this.type, this.wrapFn), this.fired = true, 0 === arguments.length ? this.listener.call(this.target) : this.listener.apply(this.target, arguments);
}
function T(e2, t2, n2) {
  var r2 = { fired: false, wrapFn: void 0, target: e2, type: t2, listener: n2 }, i2 = E.bind(r2);
  return i2.listener = n2, r2.wrapFn = i2, i2;
}
function O(e2, t2, n2) {
  var r2 = e2._events;
  if (void 0 === r2)
    return [];
  var i2 = r2[t2];
  return void 0 === i2 ? [] : "function" == typeof i2 ? n2 ? [i2.listener || i2] : [i2] : n2 ? function(e3) {
    for (var t3 = new Array(e3.length), n3 = 0; n3 < t3.length; ++n3)
      t3[n3] = e3[n3].listener || e3[n3];
    return t3;
  }(i2) : A(i2, i2.length);
}
function P(e2) {
  var t2 = this._events;
  if (void 0 !== t2) {
    var n2 = t2[e2];
    if ("function" == typeof n2)
      return 1;
    if (void 0 !== n2)
      return n2.length;
  }
  return 0;
}
function A(e2, t2) {
  for (var n2 = new Array(t2), r2 = 0; r2 < t2; ++r2)
    n2[r2] = e2[r2];
  return n2;
}
function j(e2, t2, n2, r2) {
  if ("function" == typeof e2.on)
    r2.once ? e2.once(t2, n2) : e2.on(t2, n2);
  else {
    if ("function" != typeof e2.addEventListener)
      throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof e2);
    e2.addEventListener(t2, function i2(o2) {
      r2.once && e2.removeEventListener(t2, i2), n2(o2);
    });
  }
}
function D(e2, t2, n2) {
  for (n2 of e2.keys())
    if (N(n2, t2))
      return n2;
}
function N(e2, t2) {
  var n2, r2, i2;
  if (e2 === t2)
    return true;
  if (e2 && t2 && (n2 = e2.constructor) === t2.constructor) {
    if (n2 === Date)
      return e2.getTime() === t2.getTime();
    if (n2 === RegExp)
      return e2.toString() === t2.toString();
    if (n2 === Array) {
      if ((r2 = e2.length) === t2.length)
        for (; r2-- && N(e2[r2], t2[r2]); )
          ;
      return -1 === r2;
    }
    if (n2 === Set) {
      if (e2.size !== t2.size)
        return false;
      for (r2 of e2) {
        if ((i2 = r2) && "object" == typeof i2 && !(i2 = D(t2, i2)))
          return false;
        if (!t2.has(i2))
          return false;
      }
      return true;
    }
    if (n2 === Map) {
      if (e2.size !== t2.size)
        return false;
      for (r2 of e2) {
        if ((i2 = r2[0]) && "object" == typeof i2 && !(i2 = D(t2, i2)))
          return false;
        if (!N(r2[1], t2.get(i2)))
          return false;
      }
      return true;
    }
    if (n2 === ArrayBuffer)
      e2 = new Uint8Array(e2), t2 = new Uint8Array(t2);
    else if (n2 === DataView) {
      if ((r2 = e2.byteLength) === t2.byteLength)
        for (; r2-- && e2.getInt8(r2) === t2.getInt8(r2); )
          ;
      return -1 === r2;
    }
    if (ArrayBuffer.isView(e2)) {
      if ((r2 = e2.byteLength) === t2.byteLength)
        for (; r2-- && e2[r2] === t2[r2]; )
          ;
      return -1 === r2;
    }
    if (!n2 || "object" == typeof e2) {
      for (n2 in r2 = 0, e2) {
        if (L.call(e2, n2) && ++r2 && !L.call(t2, n2))
          return false;
        if (!(n2 in t2) || !N(e2[n2], t2[n2]))
          return false;
      }
      return Object.keys(t2).length === r2;
    }
  }
  return e2 != e2 && t2 != t2;
}
function K() {
  return Date.now() + Math.random().toString();
}
function Y() {
  throw new Error("Method must be implemented in subclass");
}
function X(e2, t2) {
  return null != t2 && t2.proxyUrl ? t2.proxyUrl + ("/" === t2.proxyUrl.slice(-1) ? "" : "/") + e2.substring(8) : e2;
}
function Z(e2) {
  return null != e2 && e2.callObjectBundleUrlOverride ? e2.callObjectBundleUrlOverride : X("https://c.daily.co/call-machine/versioned/".concat("0.79.0", "/static/call-machine-object-bundle.js"), e2);
}
function ee(e2) {
  try {
    new URL(e2);
  } catch (e3) {
    return false;
  }
  return true;
}
function ie(e2, t2, n2) {
  const r2 = n2 || re, i2 = r2.__SENTRY__ = r2.__SENTRY__ || {}, o2 = i2[ne] = i2[ne] || {};
  return o2[e2] || (o2[e2] = t2());
}
function ce(e2) {
  if (!("console" in re))
    return e2();
  const t2 = re.console, n2 = {}, r2 = Object.keys(ae);
  r2.forEach((e3) => {
    const r3 = ae[e3];
    n2[e3] = t2[e3], t2[e3] = r3;
  });
  try {
    return e2();
  } finally {
    r2.forEach((e3) => {
      t2[e3] = n2[e3];
    });
  }
}
function pe(e2) {
  return e2[e2.length - 1] || {};
}
function ve(e2) {
  try {
    return e2 && "function" == typeof e2 && e2.name || fe;
  } catch (e3) {
    return fe;
  }
}
function ge(e2) {
  const t2 = e2.exception;
  if (t2) {
    const e3 = [];
    try {
      return t2.values.forEach((t3) => {
        t3.stacktrace.frames && e3.push(...t3.stacktrace.frames);
      }), e3;
    } catch (e4) {
      return;
    }
  }
}
function be(e2, t2) {
  me[e2] = me[e2] || [], me[e2].push(t2);
}
function _e(e2, t2) {
  if (!ye[e2]) {
    ye[e2] = true;
    try {
      t2();
    } catch (t3) {
      oe && le.error(`Error while instrumenting ${e2}`, t3);
    }
  }
}
function we(e2, t2) {
  const n2 = e2 && me[e2];
  if (n2)
    for (const r2 of n2)
      try {
        r2(t2);
      } catch (t3) {
        oe && le.error(`Error while triggering instrumentation handler.
Type: ${e2}
Name: ${ve(r2)}
Error:`, t3);
      }
}
function ke() {
  Se = re.onerror, re.onerror = function(e2, t2, n2, r2, i2) {
    return we("error", { column: r2, error: i2, line: n2, msg: e2, url: t2 }), !!Se && Se.apply(this, arguments);
  }, re.onerror.__SENTRY_INSTRUMENTED__ = true;
}
function Ce() {
  Me = re.onunhandledrejection, re.onunhandledrejection = function(e2) {
    return we("unhandledrejection", e2), !Me || Me.apply(this, arguments);
  }, re.onunhandledrejection.__SENTRY_INSTRUMENTED__ = true;
}
function Ee() {
  return Te(re), re;
}
function Te(e2) {
  const t2 = e2.__SENTRY__ = e2.__SENTRY__ || {};
  return t2.version = t2.version || ne, t2[ne] = t2[ne] || {};
}
function Pe(e2) {
  switch (Oe.call(e2)) {
    case "[object Error]":
    case "[object Exception]":
    case "[object DOMException]":
    case "[object WebAssembly.Exception]":
      return true;
    default:
      return Be(e2, Error);
  }
}
function Ae(e2, t2) {
  return Oe.call(e2) === `[object ${t2}]`;
}
function je(e2) {
  return Ae(e2, "ErrorEvent");
}
function Ie(e2) {
  return Ae(e2, "DOMError");
}
function xe(e2) {
  return Ae(e2, "String");
}
function Le(e2) {
  return "object" == typeof e2 && null !== e2 && "__sentry_template_string__" in e2 && "__sentry_template_values__" in e2;
}
function De(e2) {
  return null === e2 || Le(e2) || "object" != typeof e2 && "function" != typeof e2;
}
function Ne(e2) {
  return Ae(e2, "Object");
}
function Re(e2) {
  return "undefined" != typeof Event && Be(e2, Event);
}
function Fe(e2) {
  return Boolean(e2 && e2.then && "function" == typeof e2.then);
}
function Be(e2, t2) {
  try {
    return e2 instanceof t2;
  } catch (e3) {
    return false;
  }
}
function Ue(e2) {
  return !("object" != typeof e2 || null === e2 || !e2.__isVue && !e2._isVue);
}
function Je(e2, t2 = {}) {
  if (!e2)
    return "<unknown>";
  try {
    let n2 = e2;
    const r2 = 5, i2 = [];
    let o2 = 0, s2 = 0;
    const a2 = " > ", c2 = a2.length;
    let l2;
    const u2 = Array.isArray(t2) ? t2 : t2.keyAttrs, d2 = !Array.isArray(t2) && t2.maxStringLength || 80;
    for (; n2 && o2++ < r2 && (l2 = $e(n2, u2), !("html" === l2 || o2 > 1 && s2 + i2.length * c2 + l2.length >= d2)); )
      i2.push(l2), s2 += l2.length, n2 = n2.parentNode;
    return i2.reverse().join(a2);
  } catch (e3) {
    return "<unknown>";
  }
}
function $e(e2, t2) {
  const n2 = e2, r2 = [];
  if (!n2 || !n2.tagName)
    return "";
  if (Ve.HTMLElement && n2 instanceof HTMLElement && n2.dataset) {
    if (n2.dataset.sentryComponent)
      return n2.dataset.sentryComponent;
    if (n2.dataset.sentryElement)
      return n2.dataset.sentryElement;
  }
  r2.push(n2.tagName.toLowerCase());
  const i2 = t2 && t2.length ? t2.filter((e3) => n2.getAttribute(e3)).map((e3) => [e3, n2.getAttribute(e3)]) : null;
  if (i2 && i2.length)
    i2.forEach((e3) => {
      r2.push(`[${e3[0]}="${e3[1]}"]`);
    });
  else {
    n2.id && r2.push(`#${n2.id}`);
    const e3 = n2.className;
    if (e3 && xe(e3)) {
      const t3 = e3.split(/\s+/);
      for (const e4 of t3)
        r2.push(`.${e4}`);
    }
  }
  const o2 = ["aria-label", "type", "name", "title", "alt"];
  for (const e3 of o2) {
    const t3 = n2.getAttribute(e3);
    t3 && r2.push(`[${e3}="${t3}"]`);
  }
  return r2.join("");
}
function qe(e2, t2 = 0) {
  return "string" != typeof e2 || 0 === t2 || e2.length <= t2 ? e2 : `${e2.slice(0, t2)}...`;
}
function ze(e2, t2) {
  if (!Array.isArray(e2))
    return "";
  const n2 = [];
  for (let t3 = 0; t3 < e2.length; t3++) {
    const r2 = e2[t3];
    try {
      Ue(r2) ? n2.push("[VueViewModel]") : n2.push(String(r2));
    } catch (e3) {
      n2.push("[value cannot be serialized]");
    }
  }
  return n2.join(t2);
}
function We(e2, t2, n2 = false) {
  return !!xe(e2) && (Ae(t2, "RegExp") ? t2.test(e2) : !!xe(t2) && (n2 ? e2 === t2 : e2.includes(t2)));
}
function He(e2, t2 = [], n2 = false) {
  return t2.some((t3) => We(e2, t3, n2));
}
function Ge(e2, t2, n2) {
  if (!(t2 in e2))
    return;
  const r2 = e2[t2], i2 = n2(r2);
  "function" == typeof i2 && Ke(i2, r2);
  try {
    e2[t2] = i2;
  } catch (n3) {
    oe && le.log(`Failed to replace method "${t2}" in object`, e2);
  }
}
function Qe(e2, t2, n2) {
  try {
    Object.defineProperty(e2, t2, { value: n2, writable: true, configurable: true });
  } catch (n3) {
    oe && le.log(`Failed to add non-enumerable property "${t2}" to object`, e2);
  }
}
function Ke(e2, t2) {
  try {
    const n2 = t2.prototype || {};
    e2.prototype = t2.prototype = n2, Qe(e2, "__sentry_original__", t2);
  } catch (e3) {
  }
}
function Ye(e2) {
  return e2.__sentry_original__;
}
function Xe(e2) {
  if (Pe(e2))
    return { message: e2.message, name: e2.name, stack: e2.stack, ...et(e2) };
  if (Re(e2)) {
    const t2 = { type: e2.type, target: Ze(e2.target), currentTarget: Ze(e2.currentTarget), ...et(e2) };
    return "undefined" != typeof CustomEvent && Be(e2, CustomEvent) && (t2.detail = e2.detail), t2;
  }
  return e2;
}
function Ze(e2) {
  try {
    return t2 = e2, "undefined" != typeof Element && Be(t2, Element) ? Je(e2) : Object.prototype.toString.call(e2);
  } catch (e3) {
    return "<unknown>";
  }
  var t2;
}
function et(e2) {
  if ("object" == typeof e2 && null !== e2) {
    const t2 = {};
    for (const n2 in e2)
      Object.prototype.hasOwnProperty.call(e2, n2) && (t2[n2] = e2[n2]);
    return t2;
  }
  return {};
}
function tt(e2) {
  return nt(e2, /* @__PURE__ */ new Map());
}
function nt(e2, t2) {
  if (function(e3) {
    if (!Ne(e3))
      return false;
    try {
      const t3 = Object.getPrototypeOf(e3).constructor.name;
      return !t3 || "Object" === t3;
    } catch (e4) {
      return true;
    }
  }(e2)) {
    const n2 = t2.get(e2);
    if (void 0 !== n2)
      return n2;
    const r2 = {};
    t2.set(e2, r2);
    for (const n3 of Object.getOwnPropertyNames(e2))
      void 0 !== e2[n3] && (r2[n3] = nt(e2[n3], t2));
    return r2;
  }
  if (Array.isArray(e2)) {
    const n2 = t2.get(e2);
    if (void 0 !== n2)
      return n2;
    const r2 = [];
    return t2.set(e2, r2), e2.forEach((e3) => {
      r2.push(nt(e3, t2));
    }), r2;
  }
  return e2;
}
function rt() {
  return Date.now() / 1e3;
}
function ot() {
  const e2 = re, t2 = e2.crypto || e2.msCrypto;
  let n2 = () => 16 * Math.random();
  try {
    if (t2 && t2.randomUUID)
      return t2.randomUUID().replace(/-/g, "");
    t2 && t2.getRandomValues && (n2 = () => {
      const e3 = new Uint8Array(1);
      return t2.getRandomValues(e3), e3[0];
    });
  } catch (e3) {
  }
  return ([1e7] + 1e3 + 4e3 + 8e3 + 1e11).replace(/[018]/g, (e3) => (e3 ^ (15 & n2()) >> e3 / 4).toString(16));
}
function st(e2) {
  return e2.exception && e2.exception.values ? e2.exception.values[0] : void 0;
}
function at(e2) {
  const { message: t2, event_id: n2 } = e2;
  if (t2)
    return t2;
  const r2 = st(e2);
  return r2 ? r2.type && r2.value ? `${r2.type}: ${r2.value}` : r2.type || r2.value || n2 || "<unknown>" : n2 || "<unknown>";
}
function ct(e2, t2, n2) {
  const r2 = e2.exception = e2.exception || {}, i2 = r2.values = r2.values || [], o2 = i2[0] = i2[0] || {};
  o2.value || (o2.value = t2 || ""), o2.type || (o2.type = n2 || "Error");
}
function lt(e2, t2) {
  const n2 = st(e2);
  if (!n2)
    return;
  const r2 = n2.mechanism;
  if (n2.mechanism = { type: "generic", handled: true, ...r2, ...t2 }, t2 && "data" in t2) {
    const e3 = { ...r2 && r2.data, ...t2.data };
    n2.mechanism.data = e3;
  }
}
function ut(e2) {
  if (function(e3) {
    try {
      return e3.__sentry_captured__;
    } catch (e4) {
    }
  }(e2))
    return true;
  try {
    Qe(e2, "__sentry_captured__", true);
  } catch (e3) {
  }
  return false;
}
function ht(e2) {
  return new ft((t2) => {
    t2(e2);
  });
}
function pt(e2) {
  return new ft((t2, n2) => {
    n2(e2);
  });
}
function vt(e2) {
  const t2 = it(), n2 = { sid: ot(), init: true, timestamp: t2, started: t2, duration: 0, status: "ok", errors: 0, ignoreDuration: false, toJSON: () => function(e3) {
    return tt({ sid: `${e3.sid}`, init: e3.init, started: new Date(1e3 * e3.started).toISOString(), timestamp: new Date(1e3 * e3.timestamp).toISOString(), status: e3.status, errors: e3.errors, did: "number" == typeof e3.did || "string" == typeof e3.did ? `${e3.did}` : void 0, duration: e3.duration, abnormal_mechanism: e3.abnormal_mechanism, attrs: { release: e3.release, environment: e3.environment, ip_address: e3.ipAddress, user_agent: e3.userAgent } });
  }(n2) };
  return e2 && gt(n2, e2), n2;
}
function gt(e2, t2 = {}) {
  if (t2.user && (!e2.ipAddress && t2.user.ip_address && (e2.ipAddress = t2.user.ip_address), e2.did || t2.did || (e2.did = t2.user.id || t2.user.email || t2.user.username)), e2.timestamp = t2.timestamp || it(), t2.abnormal_mechanism && (e2.abnormal_mechanism = t2.abnormal_mechanism), t2.ignoreDuration && (e2.ignoreDuration = t2.ignoreDuration), t2.sid && (e2.sid = 32 === t2.sid.length ? t2.sid : ot()), void 0 !== t2.init && (e2.init = t2.init), !e2.did && t2.did && (e2.did = `${t2.did}`), "number" == typeof t2.started && (e2.started = t2.started), e2.ignoreDuration)
    e2.duration = void 0;
  else if ("number" == typeof t2.duration)
    e2.duration = t2.duration;
  else {
    const t3 = e2.timestamp - e2.started;
    e2.duration = t3 >= 0 ? t3 : 0;
  }
  t2.release && (e2.release = t2.release), t2.environment && (e2.environment = t2.environment), !e2.ipAddress && t2.ipAddress && (e2.ipAddress = t2.ipAddress), !e2.userAgent && t2.userAgent && (e2.userAgent = t2.userAgent), "number" == typeof t2.errors && (e2.errors = t2.errors), t2.status && (e2.status = t2.status);
}
function mt() {
  return ot();
}
function yt() {
  return ot().substring(16);
}
function bt(e2, t2, n2 = 2) {
  if (!t2 || "object" != typeof t2 || n2 <= 0)
    return t2;
  if (e2 && t2 && 0 === Object.keys(t2).length)
    return e2;
  const r2 = { ...e2 };
  for (const e3 in t2)
    Object.prototype.hasOwnProperty.call(t2, e3) && (r2[e3] = bt(r2[e3], t2[e3], n2 - 1));
  return r2;
}
function wt(e2, t2) {
  t2 ? Qe(e2, _t, t2) : delete e2[_t];
}
function St(e2) {
  return e2[_t];
}
function Et() {
  const e2 = Te(Ee());
  return e2.stack = e2.stack || new Ct(ie("defaultCurrentScope", () => new Mt()), ie("defaultIsolationScope", () => new Mt()));
}
function Tt(e2) {
  return Et().withScope(e2);
}
function Ot(e2, t2) {
  const n2 = Et();
  return n2.withScope(() => (n2.getStackTop().scope = e2, t2(e2)));
}
function Pt(e2) {
  return Et().withScope(() => e2(Et().getIsolationScope()));
}
function At(e2) {
  const t2 = Te(e2);
  return t2.acs ? t2.acs : { withIsolationScope: Pt, withScope: Tt, withSetScope: Ot, withSetIsolationScope: (e3, t3) => Pt(t3), getCurrentScope: () => Et().getScope(), getIsolationScope: () => Et().getIsolationScope() };
}
function jt() {
  return At(Ee()).getCurrentScope();
}
function It() {
  return At(Ee()).getIsolationScope();
}
function xt() {
  return jt().getClient();
}
function Lt(e2) {
  const t2 = e2.getPropagationContext(), { traceId: n2, spanId: r2, parentSpanId: i2 } = t2;
  return tt({ trace_id: n2, span_id: r2, parent_span_id: i2 });
}
function Dt(e2) {
  const t2 = e2._sentryMetrics;
  if (!t2)
    return;
  const n2 = {};
  for (const [, [e3, r2]] of t2) {
    (n2[e3] || (n2[e3] = [])).push(tt(r2));
  }
  return n2;
}
function Rt(e2) {
  const t2 = function(e3) {
    if (!e3 || !xe(e3) && !Array.isArray(e3))
      return;
    if (Array.isArray(e3))
      return e3.reduce((e4, t3) => {
        const n3 = Ft(t3);
        return Object.entries(n3).forEach(([t4, n4]) => {
          e4[t4] = n4;
        }), e4;
      }, {});
    return Ft(e3);
  }(e2);
  if (!t2)
    return;
  const n2 = Object.entries(t2).reduce((e3, [t3, n3]) => {
    if (t3.match(Nt)) {
      e3[t3.slice(7)] = n3;
    }
    return e3;
  }, {});
  return Object.keys(n2).length > 0 ? n2 : void 0;
}
function Ft(e2) {
  return e2.split(",").map((e3) => e3.split("=").map((e4) => decodeURIComponent(e4.trim()))).reduce((e3, [t2, n2]) => (t2 && n2 && (e3[t2] = n2), e3), {});
}
function Ut(e2) {
  const { spanId: t2, traceId: n2, isRemote: r2 } = e2.spanContext();
  return tt({ parent_span_id: r2 ? t2 : $t(e2).parent_span_id, span_id: r2 ? yt() : t2, trace_id: n2 });
}
function Vt(e2) {
  return "number" == typeof e2 ? Jt(e2) : Array.isArray(e2) ? e2[0] + e2[1] / 1e9 : e2 instanceof Date ? Jt(e2.getTime()) : it();
}
function Jt(e2) {
  return e2 > 9999999999 ? e2 / 1e3 : e2;
}
function $t(e2) {
  if (function(e3) {
    return "function" == typeof e3.getSpanJSON;
  }(e2))
    return e2.getSpanJSON();
  try {
    const { spanId: t2, traceId: n2 } = e2.spanContext();
    if (function(e3) {
      const t3 = e3;
      return !!(t3.attributes && t3.startTime && t3.name && t3.endTime && t3.status);
    }(e2)) {
      const { attributes: r2, startTime: i2, name: o2, endTime: s2, parentSpanId: a2, status: c2 } = e2;
      return tt({ span_id: t2, trace_id: n2, data: r2, description: o2, parent_span_id: a2, start_timestamp: Vt(i2), timestamp: Vt(s2) || void 0, status: qt(c2), op: r2["sentry.op"], origin: r2["sentry.origin"], _metrics_summary: Dt(e2) });
    }
    return { span_id: t2, trace_id: n2 };
  } catch (e3) {
    return {};
  }
}
function qt(e2) {
  if (e2 && 0 !== e2.code)
    return 1 === e2.code ? "ok" : e2.message || "unknown_error";
}
function zt(e2) {
  return e2._sentryRootSpan || e2;
}
function Wt() {
  Bt || (ce(() => {
    console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.");
  }), Bt = true);
}
function Gt(e2, t2) {
  const n2 = t2.getOptions(), { publicKey: r2 } = t2.getDsn() || {}, i2 = tt({ environment: n2.environment || Ht, release: n2.release, public_key: r2, trace_id: e2 });
  return t2.emit("createDsc", i2), i2;
}
function Qt(e2) {
  const t2 = xt();
  if (!t2)
    return {};
  const n2 = zt(e2), r2 = n2._frozenDsc;
  if (r2)
    return r2;
  const i2 = n2.spanContext().traceState, o2 = i2 && i2.get("sentry.dsc"), s2 = o2 && Rt(o2);
  if (s2)
    return s2;
  const a2 = Gt(e2.spanContext().traceId, t2), c2 = $t(n2), l2 = c2.data || {}, u2 = l2["sentry.sample_rate"];
  null != u2 && (a2.sample_rate = `${u2}`);
  const d2 = l2["sentry.source"], h2 = c2.description;
  return "url" !== d2 && h2 && (a2.transaction = h2), function(e3) {
    if ("boolean" == typeof __SENTRY_TRACING__ && !__SENTRY_TRACING__)
      return false;
    const t3 = xt(), n3 = e3 || t3 && t3.getOptions();
    return !!n3 && (n3.enableTracing || "tracesSampleRate" in n3 || "tracesSampler" in n3);
  }() && (a2.sampled = String(function(e3) {
    const { traceFlags: t3 } = e3.spanContext();
    return 1 === t3;
  }(n2))), t2.emit("createDsc", a2, n2), a2;
}
function Yt(e2, t2 = false) {
  const { host: n2, path: r2, pass: i2, port: o2, projectId: s2, protocol: a2, publicKey: c2 } = e2;
  return `${a2}://${c2}${t2 && i2 ? `:${i2}` : ""}@${n2}${o2 ? `:${o2}` : ""}/${r2 ? `${r2}/` : r2}${s2}`;
}
function Xt(e2) {
  return { protocol: e2.protocol, publicKey: e2.publicKey || "", pass: e2.pass || "", host: e2.host, port: e2.port || "", path: e2.path || "", projectId: e2.projectId };
}
function Zt(e2) {
  const t2 = "string" == typeof e2 ? function(e3) {
    const t3 = Kt.exec(e3);
    if (!t3)
      return void ce(() => {
        console.error(`Invalid Sentry Dsn: ${e3}`);
      });
    const [n2, r2, i2 = "", o2 = "", s2 = "", a2 = ""] = t3.slice(1);
    let c2 = "", l2 = a2;
    const u2 = l2.split("/");
    if (u2.length > 1 && (c2 = u2.slice(0, -1).join("/"), l2 = u2.pop()), l2) {
      const e4 = l2.match(/^\d+/);
      e4 && (l2 = e4[0]);
    }
    return Xt({ host: o2, pass: i2, path: c2, projectId: l2, port: s2, protocol: n2, publicKey: r2 });
  }(e2) : Xt(e2);
  if (t2 && function(e3) {
    if (!oe)
      return true;
    const { port: t3, projectId: n2, protocol: r2 } = e3;
    return !(["protocol", "publicKey", "host", "projectId"].find((t4) => !e3[t4] && (le.error(`Invalid Sentry Dsn: ${t4} missing`), true)) || (n2.match(/^\d+$/) ? function(e4) {
      return "http" === e4 || "https" === e4;
    }(r2) ? t3 && isNaN(parseInt(t3, 10)) && (le.error(`Invalid Sentry Dsn: Invalid port ${t3}`), 1) : (le.error(`Invalid Sentry Dsn: Invalid protocol ${r2}`), 1) : (le.error(`Invalid Sentry Dsn: Invalid projectId ${n2}`), 1)));
  }(t2))
    return t2;
}
function en(e2, t2 = 100, n2 = 1 / 0) {
  try {
    return nn("", e2, t2, n2);
  } catch (e3) {
    return { ERROR: `**non-serializable** (${e3})` };
  }
}
function tn(e2, t2 = 3, n2 = 102400) {
  const r2 = en(e2, t2);
  return i2 = r2, function(e3) {
    return ~-encodeURI(e3).split(/%..|./).length;
  }(JSON.stringify(i2)) > n2 ? tn(e2, t2 - 1, n2) : r2;
  var i2;
}
function nn(e2, t2, n2 = 1 / 0, r2 = 1 / 0, i2 = function() {
  const e3 = "function" == typeof WeakSet, t3 = e3 ? /* @__PURE__ */ new WeakSet() : [];
  return [function(n3) {
    if (e3)
      return !!t3.has(n3) || (t3.add(n3), false);
    for (let e4 = 0; e4 < t3.length; e4++)
      if (t3[e4] === n3)
        return true;
    return t3.push(n3), false;
  }, function(n3) {
    if (e3)
      t3.delete(n3);
    else
      for (let e4 = 0; e4 < t3.length; e4++)
        if (t3[e4] === n3) {
          t3.splice(e4, 1);
          break;
        }
  }];
}()) {
  const [o2, s2] = i2;
  if (null == t2 || ["boolean", "string"].includes(typeof t2) || "number" == typeof t2 && Number.isFinite(t2))
    return t2;
  const a2 = function(e3, t3) {
    try {
      if ("domain" === e3 && t3 && "object" == typeof t3 && t3._events)
        return "[Domain]";
      if ("domainEmitter" === e3)
        return "[DomainEmitter]";
      if ("undefined" != typeof global && t3 === global)
        return "[Global]";
      if ("undefined" != typeof window && t3 === window)
        return "[Window]";
      if ("undefined" != typeof document && t3 === document)
        return "[Document]";
      if (Ue(t3))
        return "[VueViewModel]";
      if (Ne(n3 = t3) && "nativeEvent" in n3 && "preventDefault" in n3 && "stopPropagation" in n3)
        return "[SyntheticEvent]";
      if ("number" == typeof t3 && !Number.isFinite(t3))
        return `[${t3}]`;
      if ("function" == typeof t3)
        return `[Function: ${ve(t3)}]`;
      if ("symbol" == typeof t3)
        return `[${String(t3)}]`;
      if ("bigint" == typeof t3)
        return `[BigInt: ${String(t3)}]`;
      const r3 = function(e4) {
        const t4 = Object.getPrototypeOf(e4);
        return t4 ? t4.constructor.name : "null prototype";
      }(t3);
      return /^HTML(\w*)Element$/.test(r3) ? `[HTMLElement: ${r3}]` : `[object ${r3}]`;
    } catch (e4) {
      return `**non-serializable** (${e4})`;
    }
    var n3;
  }(e2, t2);
  if (!a2.startsWith("[object "))
    return a2;
  if (t2.__sentry_skip_normalization__)
    return t2;
  const c2 = "number" == typeof t2.__sentry_override_normalization_depth__ ? t2.__sentry_override_normalization_depth__ : n2;
  if (0 === c2)
    return a2.replace("object ", "");
  if (o2(t2))
    return "[Circular ~]";
  const l2 = t2;
  if (l2 && "function" == typeof l2.toJSON)
    try {
      return nn("", l2.toJSON(), c2 - 1, r2, i2);
    } catch (e3) {
    }
  const u2 = Array.isArray(t2) ? [] : {};
  let d2 = 0;
  const h2 = Xe(t2);
  for (const e3 in h2) {
    if (!Object.prototype.hasOwnProperty.call(h2, e3))
      continue;
    if (d2 >= r2) {
      u2[e3] = "[MaxProperties ~]";
      break;
    }
    const t3 = h2[e3];
    u2[e3] = nn(e3, t3, c2 - 1, r2, i2), d2++;
  }
  return s2(t2), u2;
}
function rn(e2, t2 = []) {
  return [e2, t2];
}
function on(e2, t2) {
  const [n2, r2] = e2;
  return [n2, [...r2, t2]];
}
function sn(e2, t2) {
  const n2 = e2[1];
  for (const e3 of n2) {
    if (t2(e3, e3[0].type))
      return true;
  }
  return false;
}
function an(e2) {
  return re.__SENTRY__ && re.__SENTRY__.encodePolyfill ? re.__SENTRY__.encodePolyfill(e2) : new TextEncoder().encode(e2);
}
function cn(e2) {
  const [t2, n2] = e2;
  let r2 = JSON.stringify(t2);
  function i2(e3) {
    "string" == typeof r2 ? r2 = "string" == typeof e3 ? r2 + e3 : [an(r2), e3] : r2.push("string" == typeof e3 ? an(e3) : e3);
  }
  for (const e3 of n2) {
    const [t3, n3] = e3;
    if (i2(`
${JSON.stringify(t3)}
`), "string" == typeof n3 || n3 instanceof Uint8Array)
      i2(n3);
    else {
      let e4;
      try {
        e4 = JSON.stringify(n3);
      } catch (t4) {
        e4 = JSON.stringify(en(n3));
      }
      i2(e4);
    }
  }
  return "string" == typeof r2 ? r2 : function(e3) {
    const t3 = e3.reduce((e4, t4) => e4 + t4.length, 0), n3 = new Uint8Array(t3);
    let r3 = 0;
    for (const t4 of e3)
      n3.set(t4, r3), r3 += t4.length;
    return n3;
  }(r2);
}
function ln(e2) {
  const t2 = "string" == typeof e2.data ? an(e2.data) : e2.data;
  return [tt({ type: "attachment", length: t2.length, filename: e2.filename, content_type: e2.contentType, attachment_type: e2.attachmentType }), t2];
}
function dn(e2) {
  return un[e2];
}
function hn(e2) {
  if (!e2 || !e2.sdk)
    return;
  const { name: t2, version: n2 } = e2.sdk;
  return { name: t2, version: n2 };
}
function pn(e2, t2, n2, r2) {
  const i2 = hn(n2), o2 = e2.type && "replay_event" !== e2.type ? e2.type : "event";
  !function(e3, t3) {
    t3 && (e3.sdk = e3.sdk || {}, e3.sdk.name = e3.sdk.name || t3.name, e3.sdk.version = e3.sdk.version || t3.version, e3.sdk.integrations = [...e3.sdk.integrations || [], ...t3.integrations || []], e3.sdk.packages = [...e3.sdk.packages || [], ...t3.packages || []]);
  }(e2, n2 && n2.sdk);
  const s2 = function(e3, t3, n3, r3) {
    const i3 = e3.sdkProcessingMetadata && e3.sdkProcessingMetadata.dynamicSamplingContext;
    return { event_id: e3.event_id, sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...t3 && { sdk: t3 }, ...!!n3 && r3 && { dsn: Yt(r3) }, ...i3 && { trace: tt({ ...i3 }) } };
  }(e2, i2, r2, t2);
  delete e2.sdkProcessingMetadata;
  return rn(s2, [[{ type: o2 }, e2]]);
}
function fn(e2, t2, n2, r2 = 0) {
  return new ft((i2, o2) => {
    const s2 = e2[r2];
    if (null === t2 || "function" != typeof s2)
      i2(t2);
    else {
      const a2 = s2({ ...t2 }, n2);
      te && s2.id && null === a2 && le.log(`Event processor "${s2.id}" dropped event`), Fe(a2) ? a2.then((t3) => fn(e2, t3, n2, r2 + 1).then(i2)).then(null, o2) : fn(e2, a2, n2, r2 + 1).then(i2).then(null, o2);
    }
  });
}
function yn(e2, t2) {
  const { fingerprint: n2, span: r2, breadcrumbs: i2, sdkProcessingMetadata: o2 } = t2;
  !function(e3, t3) {
    const { extra: n3, tags: r3, user: i3, contexts: o3, level: s2, transactionName: a2 } = t3, c2 = tt(n3);
    c2 && Object.keys(c2).length && (e3.extra = { ...c2, ...e3.extra });
    const l2 = tt(r3);
    l2 && Object.keys(l2).length && (e3.tags = { ...l2, ...e3.tags });
    const u2 = tt(i3);
    u2 && Object.keys(u2).length && (e3.user = { ...u2, ...e3.user });
    const d2 = tt(o3);
    d2 && Object.keys(d2).length && (e3.contexts = { ...d2, ...e3.contexts });
    s2 && (e3.level = s2);
    a2 && "transaction" !== e3.type && (e3.transaction = a2);
  }(e2, t2), r2 && function(e3, t3) {
    e3.contexts = { trace: Ut(t3), ...e3.contexts }, e3.sdkProcessingMetadata = { dynamicSamplingContext: Qt(t3), ...e3.sdkProcessingMetadata };
    const n3 = zt(t3), r3 = $t(n3).description;
    r3 && !e3.transaction && "transaction" === e3.type && (e3.transaction = r3);
  }(e2, r2), function(e3, t3) {
    e3.fingerprint = e3.fingerprint ? Array.isArray(e3.fingerprint) ? e3.fingerprint : [e3.fingerprint] : [], t3 && (e3.fingerprint = e3.fingerprint.concat(t3));
    e3.fingerprint && !e3.fingerprint.length && delete e3.fingerprint;
  }(e2, n2), function(e3, t3) {
    const n3 = [...e3.breadcrumbs || [], ...t3];
    e3.breadcrumbs = n3.length ? n3 : void 0;
  }(e2, i2), function(e3, t3) {
    e3.sdkProcessingMetadata = { ...e3.sdkProcessingMetadata, ...t3 };
  }(e2, o2);
}
function bn(e2, t2) {
  const { extra: n2, tags: r2, user: i2, contexts: o2, level: s2, sdkProcessingMetadata: a2, breadcrumbs: c2, fingerprint: l2, eventProcessors: u2, attachments: d2, propagationContext: h2, transactionName: p2, span: f2 } = t2;
  _n(e2, "extra", n2), _n(e2, "tags", r2), _n(e2, "user", i2), _n(e2, "contexts", o2), e2.sdkProcessingMetadata = bt(e2.sdkProcessingMetadata, a2, 2), s2 && (e2.level = s2), p2 && (e2.transactionName = p2), f2 && (e2.span = f2), c2.length && (e2.breadcrumbs = [...e2.breadcrumbs, ...c2]), l2.length && (e2.fingerprint = [...e2.fingerprint, ...l2]), u2.length && (e2.eventProcessors = [...e2.eventProcessors, ...u2]), d2.length && (e2.attachments = [...e2.attachments, ...d2]), e2.propagationContext = { ...e2.propagationContext, ...h2 };
}
function _n(e2, t2, n2) {
  e2[t2] = bt(e2[t2], n2, 1);
}
function wn(e2, t2, n2, r2, i2, o2) {
  const { normalizeDepth: s2 = 3, normalizeMaxBreadth: a2 = 1e3 } = e2, c2 = { ...t2, event_id: t2.event_id || n2.event_id || ot(), timestamp: t2.timestamp || rt() }, l2 = n2.integrations || e2.integrations.map((e3) => e3.name);
  !function(e3, t3) {
    const { environment: n3, release: r3, dist: i3, maxValueLength: o3 = 250 } = t3;
    e3.environment = e3.environment || n3 || Ht, !e3.release && r3 && (e3.release = r3);
    !e3.dist && i3 && (e3.dist = i3);
    e3.message && (e3.message = qe(e3.message, o3));
    const s3 = e3.exception && e3.exception.values && e3.exception.values[0];
    s3 && s3.value && (s3.value = qe(s3.value, o3));
    const a3 = e3.request;
    a3 && a3.url && (a3.url = qe(a3.url, o3));
  }(c2, e2), function(e3, t3) {
    t3.length > 0 && (e3.sdk = e3.sdk || {}, e3.sdk.integrations = [...e3.sdk.integrations || [], ...t3]);
  }(c2, l2), i2 && i2.emit("applyFrameMetadata", t2), void 0 === t2.type && function(e3, t3) {
    const n3 = function(e4) {
      const t4 = re._sentryDebugIds;
      if (!t4)
        return {};
      const n4 = Object.keys(t4);
      return mn && n4.length === gn || (gn = n4.length, mn = n4.reduce((n5, r3) => {
        vn || (vn = {});
        const i3 = vn[r3];
        if (i3)
          n5[i3[0]] = i3[1];
        else {
          const i4 = e4(r3);
          for (let e5 = i4.length - 1; e5 >= 0; e5--) {
            const o3 = i4[e5], s3 = o3 && o3.filename, a3 = t4[r3];
            if (s3 && a3) {
              n5[s3] = a3, vn[r3] = [s3, a3];
              break;
            }
          }
        }
        return n5;
      }, {})), mn;
    }(t3);
    try {
      e3.exception.values.forEach((e4) => {
        e4.stacktrace.frames.forEach((e5) => {
          n3 && e5.filename && (e5.debug_id = n3[e5.filename]);
        });
      });
    } catch (e4) {
    }
  }(c2, e2.stackParser);
  const u2 = function(e3, t3) {
    if (!t3)
      return e3;
    const n3 = e3 ? e3.clone() : new Mt();
    return n3.update(t3), n3;
  }(r2, n2.captureContext);
  n2.mechanism && lt(c2, n2.mechanism);
  const d2 = i2 ? i2.getEventProcessors() : [], h2 = ie("globalScope", () => new Mt()).getScopeData();
  if (o2) {
    bn(h2, o2.getScopeData());
  }
  if (u2) {
    bn(h2, u2.getScopeData());
  }
  const p2 = [...n2.attachments || [], ...h2.attachments];
  p2.length && (n2.attachments = p2), yn(c2, h2);
  return fn([...d2, ...h2.eventProcessors], c2, n2).then((e3) => (e3 && function(e4) {
    const t3 = {};
    try {
      e4.exception.values.forEach((e5) => {
        e5.stacktrace.frames.forEach((e6) => {
          e6.debug_id && (e6.abs_path ? t3[e6.abs_path] = e6.debug_id : e6.filename && (t3[e6.filename] = e6.debug_id), delete e6.debug_id);
        });
      });
    } catch (e5) {
    }
    if (0 === Object.keys(t3).length)
      return;
    e4.debug_meta = e4.debug_meta || {}, e4.debug_meta.images = e4.debug_meta.images || [];
    const n3 = e4.debug_meta.images;
    Object.entries(t3).forEach(([e5, t4]) => {
      n3.push({ type: "sourcemap", code_file: e5, debug_id: t4 });
    });
  }(e3), "number" == typeof s2 && s2 > 0 ? function(e4, t3, n3) {
    if (!e4)
      return null;
    const r3 = { ...e4, ...e4.breadcrumbs && { breadcrumbs: e4.breadcrumbs.map((e5) => ({ ...e5, ...e5.data && { data: en(e5.data, t3, n3) } })) }, ...e4.user && { user: en(e4.user, t3, n3) }, ...e4.contexts && { contexts: en(e4.contexts, t3, n3) }, ...e4.extra && { extra: en(e4.extra, t3, n3) } };
    e4.contexts && e4.contexts.trace && r3.contexts && (r3.contexts.trace = e4.contexts.trace, e4.contexts.trace.data && (r3.contexts.trace.data = en(e4.contexts.trace.data, t3, n3)));
    e4.spans && (r3.spans = e4.spans.map((e5) => ({ ...e5, ...e5.data && { data: en(e5.data, t3, n3) } })));
    e4.contexts && e4.contexts.flags && r3.contexts && (r3.contexts.flags = en(e4.contexts.flags, 3, n3));
    return r3;
  }(e3, s2, a2) : e3));
}
function Sn(e2) {
  if (e2)
    return function(e3) {
      return e3 instanceof Mt || "function" == typeof e3;
    }(e2) || function(e3) {
      return Object.keys(e3).some((e4) => kn.includes(e4));
    }(e2) ? { captureContext: e2 } : e2;
}
function Mn(e2, t2) {
  return jt().captureEvent(e2, t2);
}
function Cn(e2) {
  const t2 = xt(), n2 = It(), r2 = jt(), { release: i2, environment: o2 = Ht } = t2 && t2.getOptions() || {}, { userAgent: s2 } = re.navigator || {}, a2 = vt({ release: i2, environment: o2, user: r2.getUser() || n2.getUser(), ...s2 && { userAgent: s2 }, ...e2 }), c2 = n2.getSession();
  return c2 && "ok" === c2.status && gt(c2, { status: "exited" }), En(), n2.setSession(a2), r2.setSession(a2), a2;
}
function En() {
  const e2 = It(), t2 = jt(), n2 = t2.getSession() || e2.getSession();
  n2 && function(e3, t3) {
    let n3 = {};
    t3 ? n3 = { status: t3 } : "ok" === e3.status && (n3 = { status: "exited" }), gt(e3, n3);
  }(n2), Tn(), e2.setSession(), t2.setSession();
}
function Tn() {
  const e2 = It(), t2 = jt(), n2 = xt(), r2 = t2.getSession() || e2.getSession();
  r2 && n2 && n2.captureSession(r2);
}
function On(e2 = false) {
  e2 ? En() : Tn();
}
function Pn(e2, t2, n2) {
  return t2 || `${function(e3) {
    return `${function(e4) {
      const t3 = e4.protocol ? `${e4.protocol}:` : "", n3 = e4.port ? `:${e4.port}` : "";
      return `${t3}//${e4.host}${n3}${e4.path ? `/${e4.path}` : ""}/api/`;
    }(e3)}${e3.projectId}/envelope/`;
  }(e2)}?${function(e3, t3) {
    const n3 = { sentry_version: "7" };
    return e3.publicKey && (n3.sentry_key = e3.publicKey), t3 && (n3.sentry_client = `${t3.name}/${t3.version}`), new URLSearchParams(n3).toString();
  }(e2, n2)}`;
}
function jn(e2, t2) {
  for (const n2 of t2)
    n2 && n2.afterAllSetup && n2.afterAllSetup(e2);
}
function In(e2, t2, n2) {
  if (n2[t2.name])
    te && le.log(`Integration skipped because it was already installed: ${t2.name}`);
  else {
    if (n2[t2.name] = t2, -1 === An.indexOf(t2.name) && "function" == typeof t2.setupOnce && (t2.setupOnce(), An.push(t2.name)), t2.setup && "function" == typeof t2.setup && t2.setup(e2), "function" == typeof t2.preprocessEvent) {
      const n3 = t2.preprocessEvent.bind(t2);
      e2.on("preprocessEvent", (t3, r2) => n3(t3, r2, e2));
    }
    if ("function" == typeof t2.processEvent) {
      const n3 = t2.processEvent.bind(t2), r2 = Object.assign((t3, r3) => n3(t3, r3, e2), { id: t2.name });
      e2.addEventProcessor(r2);
    }
    te && le.log(`Integration installed: ${t2.name}`);
  }
}
function Nn(e2) {
  return void 0 === e2.type;
}
function Rn(e2) {
  return "transaction" === e2.type;
}
function Fn(e2) {
  const t2 = [];
  function n2(e3) {
    return t2.splice(t2.indexOf(e3), 1)[0] || Promise.resolve(void 0);
  }
  return { $: t2, add: function(r2) {
    if (!(void 0 === e2 || t2.length < e2))
      return pt(new xn("Not adding Promise because buffer limit was reached."));
    const i2 = r2();
    return -1 === t2.indexOf(i2) && t2.push(i2), i2.then(() => n2(i2)).then(null, () => n2(i2).then(null, () => {
    })), i2;
  }, drain: function(e3) {
    return new ft((n3, r2) => {
      let i2 = t2.length;
      if (!i2)
        return n3(true);
      const o2 = setTimeout(() => {
        e3 && e3 > 0 && n3(false);
      }, e3);
      t2.forEach((e4) => {
        ht(e4).then(() => {
          --i2 || (clearTimeout(o2), n3(true));
        }, r2);
      });
    });
  } };
}
function Bn(e2, { statusCode: t2, headers: n2 }, r2 = Date.now()) {
  const i2 = { ...e2 }, o2 = n2 && n2["x-sentry-rate-limits"], s2 = n2 && n2["retry-after"];
  if (o2)
    for (const e3 of o2.trim().split(",")) {
      const [t3, n3, , , o3] = e3.split(":", 5), s3 = parseInt(t3, 10), a2 = 1e3 * (isNaN(s3) ? 60 : s3);
      if (n3)
        for (const e4 of n3.split(";"))
          "metric_bucket" === e4 && o3 && !o3.split(";").includes("custom") || (i2[e4] = r2 + a2);
      else
        i2.all = r2 + a2;
    }
  else
    s2 ? i2.all = r2 + function(e3, t3 = Date.now()) {
      const n3 = parseInt(`${e3}`, 10);
      if (!isNaN(n3))
        return 1e3 * n3;
      const r3 = Date.parse(`${e3}`);
      return isNaN(r3) ? 6e4 : r3 - t3;
    }(s2, r2) : 429 === t2 && (i2.all = r2 + 6e4);
  return i2;
}
function Un(e2, t2, n2 = Fn(e2.bufferSize || 64)) {
  let r2 = {};
  return { send: function(i2) {
    const o2 = [];
    if (sn(i2, (t3, n3) => {
      const i3 = dn(n3);
      if (function(e3, t4, n4 = Date.now()) {
        return function(e4, t5) {
          return e4[t5] || e4.all || 0;
        }(e3, t4) > n4;
      }(r2, i3)) {
        const r3 = Vn(t3, n3);
        e2.recordDroppedEvent("ratelimit_backoff", i3, r3);
      } else
        o2.push(t3);
    }), 0 === o2.length)
      return ht({});
    const s2 = rn(i2[0], o2), a2 = (t3) => {
      sn(s2, (n3, r3) => {
        const i3 = Vn(n3, r3);
        e2.recordDroppedEvent(t3, dn(r3), i3);
      });
    };
    return n2.add(() => t2({ body: cn(s2) }).then((e3) => (void 0 !== e3.statusCode && (e3.statusCode < 200 || e3.statusCode >= 300) && te && le.warn(`Sentry responded with status code ${e3.statusCode} to sent event.`), r2 = Bn(r2, e3), e3), (e3) => {
      throw a2("network_error"), e3;
    })).then((e3) => e3, (e3) => {
      if (e3 instanceof xn)
        return te && le.error("Skipped sending event because buffer is full."), a2("queue_overflow"), ht({});
      throw e3;
    });
  }, flush: (e3) => n2.drain(e3) };
}
function Vn(e2, t2) {
  if ("event" === t2 || "transaction" === t2)
    return Array.isArray(e2) ? e2[1] : void 0;
}
function $n(e2, t2) {
  const n2 = xt(), r2 = It();
  if (!n2)
    return;
  const { beforeBreadcrumb: i2 = null, maxBreadcrumbs: o2 = Jn } = n2.getOptions();
  if (o2 <= 0)
    return;
  const s2 = { timestamp: rt(), ...e2 }, a2 = i2 ? ce(() => i2(s2, t2)) : s2;
  null !== a2 && (n2.emit && n2.emit("beforeAddBreadcrumb", a2, t2), r2.addBreadcrumb(a2, o2));
}
function Qn(e2) {
  try {
    let t2;
    try {
      t2 = e2.exception.values[0].stacktrace.frames;
    } catch (e3) {
    }
    return t2 ? function(e3 = []) {
      for (let t3 = e3.length - 1; t3 >= 0; t3--) {
        const n2 = e3[t3];
        if (n2 && "<anonymous>" !== n2.filename && "[native code]" !== n2.filename)
          return n2.filename || null;
      }
      return null;
    }(t2) : null;
  } catch (t2) {
    return te && le.error(`Cannot extract url for event ${at(e2)}`), null;
  }
}
function Kn(e2, t2, n2 = 250, r2, i2, o2, s2) {
  if (!(o2.exception && o2.exception.values && s2 && Be(s2.originalException, Error)))
    return;
  const a2 = o2.exception.values.length > 0 ? o2.exception.values[o2.exception.values.length - 1] : void 0;
  var c2, l2;
  a2 && (o2.exception.values = (c2 = Yn(e2, t2, i2, s2.originalException, r2, o2.exception.values, a2, 0), l2 = n2, c2.map((e3) => (e3.value && (e3.value = qe(e3.value, l2)), e3))));
}
function Yn(e2, t2, n2, r2, i2, o2, s2, a2) {
  if (o2.length >= n2 + 1)
    return o2;
  let c2 = [...o2];
  if (Be(r2[i2], Error)) {
    Xn(s2, a2);
    const o3 = e2(t2, r2[i2]), l2 = c2.length;
    Zn(o3, i2, l2, a2), c2 = Yn(e2, t2, n2, r2[i2], i2, [o3, ...c2], o3, l2);
  }
  return Array.isArray(r2.errors) && r2.errors.forEach((r3, o3) => {
    if (Be(r3, Error)) {
      Xn(s2, a2);
      const l2 = e2(t2, r3), u2 = c2.length;
      Zn(l2, `errors[${o3}]`, u2, a2), c2 = Yn(e2, t2, n2, r3, i2, [l2, ...c2], l2, u2);
    }
  }), c2;
}
function Xn(e2, t2) {
  e2.mechanism = e2.mechanism || { type: "generic", handled: true }, e2.mechanism = { ...e2.mechanism, ..."AggregateError" === e2.type && { is_exception_group: true }, exception_id: t2 };
}
function Zn(e2, t2, n2, r2) {
  e2.mechanism = e2.mechanism || { type: "generic", handled: true }, e2.mechanism = { ...e2.mechanism, type: "chained", source: t2, exception_id: n2, parent_id: r2 };
}
function er(e2) {
  if (!e2)
    return {};
  const t2 = e2.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);
  if (!t2)
    return {};
  const n2 = t2[6] || "", r2 = t2[8] || "";
  return { host: t2[4], path: t2[5], protocol: t2[2], search: n2, hash: r2, relative: t2[5] + n2 + r2 };
}
function tr() {
  "console" in re && se.forEach(function(e2) {
    e2 in re.console && Ge(re.console, e2, function(t2) {
      return ae[e2] = t2, function(...t3) {
        we("console", { args: t3, level: e2 });
        const n2 = ae[e2];
        n2 && n2.apply(re.console, t3);
      };
    });
  });
}
function nr(e2) {
  return "warn" === e2 ? "warning" : ["fatal", "error", "warning", "log", "info", "debug"].includes(e2) ? e2 : "log";
}
function ir(e2, t2) {
  let n2 = ge(e2), r2 = ge(t2);
  if (!n2 && !r2)
    return true;
  if (n2 && !r2 || !n2 && r2)
    return false;
  if (r2.length !== n2.length)
    return false;
  for (let e3 = 0; e3 < r2.length; e3++) {
    const t3 = r2[e3], i2 = n2[e3];
    if (t3.filename !== i2.filename || t3.lineno !== i2.lineno || t3.colno !== i2.colno || t3.function !== i2.function)
      return false;
  }
  return true;
}
function or(e2, t2) {
  let n2 = e2.fingerprint, r2 = t2.fingerprint;
  if (!n2 && !r2)
    return true;
  if (n2 && !r2 || !n2 && r2)
    return false;
  try {
    return !(n2.join("") !== r2.join(""));
  } catch (e3) {
    return false;
  }
}
function sr(e2) {
  return e2.exception && e2.exception.values && e2.exception.values[0];
}
function ar(e2) {
  return void 0 === e2 ? void 0 : e2 >= 400 && e2 < 500 ? "warning" : e2 >= 500 ? "error" : void 0;
}
function lr(e2) {
  return e2 && /^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e2.toString());
}
function ur() {
  if ("string" == typeof EdgeRuntime)
    return true;
  if (!function() {
    if (!("fetch" in cr))
      return false;
    try {
      return new Headers(), new Request("http://www.example.com"), new Response(), true;
    } catch (e3) {
      return false;
    }
  }())
    return false;
  if (lr(cr.fetch))
    return true;
  let e2 = false;
  const t2 = cr.document;
  if (t2 && "function" == typeof t2.createElement)
    try {
      const n2 = t2.createElement("iframe");
      n2.hidden = true, t2.head.appendChild(n2), n2.contentWindow && n2.contentWindow.fetch && (e2 = lr(n2.contentWindow.fetch)), t2.head.removeChild(n2);
    } catch (e3) {
      oe && le.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ", e3);
    }
  return e2;
}
function dr(e2, t2) {
  const n2 = "fetch";
  be(n2, e2), _e(n2, () => function(e3, t3 = false) {
    if (t3 && !ur())
      return;
    Ge(re, "fetch", function(t4) {
      return function(...n3) {
        const r2 = new Error(), { method: i2, url: o2 } = function(e4) {
          if (0 === e4.length)
            return { method: "GET", url: "" };
          if (2 === e4.length) {
            const [t6, n4] = e4;
            return { url: pr(t6), method: hr(n4, "method") ? String(n4.method).toUpperCase() : "GET" };
          }
          const t5 = e4[0];
          return { url: pr(t5), method: hr(t5, "method") ? String(t5.method).toUpperCase() : "GET" };
        }(n3), s2 = { args: n3, fetchData: { method: i2, url: o2 }, startTimestamp: 1e3 * it(), virtualError: r2 };
        return e3 || we("fetch", { ...s2 }), t4.apply(re, n3).then(async (t5) => (e3 ? e3(t5) : we("fetch", { ...s2, endTimestamp: 1e3 * it(), response: t5 }), t5), (e4) => {
          throw we("fetch", { ...s2, endTimestamp: 1e3 * it(), error: e4 }), Pe(e4) && void 0 === e4.stack && (e4.stack = r2.stack, Qe(e4, "framesToPop", 1)), e4;
        });
      };
    });
  }(void 0, t2));
}
function hr(e2, t2) {
  return !!e2 && "object" == typeof e2 && !!e2[t2];
}
function pr(e2) {
  return "string" == typeof e2 ? e2 : e2 ? hr(e2, "url") ? e2.url : e2.toString ? e2.toString() : "" : "";
}
function mr() {
  return gr > 0;
}
function yr(e2, t2 = {}) {
  if (!function(e3) {
    return "function" == typeof e3;
  }(e2))
    return e2;
  try {
    const t3 = e2.__sentry_wrapped__;
    if (t3)
      return "function" == typeof t3 ? t3 : e2;
    if (Ye(e2))
      return e2;
  } catch (t3) {
    return e2;
  }
  const n2 = function(...n3) {
    try {
      const r2 = n3.map((e3) => yr(e3, t2));
      return e2.apply(this, r2);
    } catch (e3) {
      throw gr++, setTimeout(() => {
        gr--;
      }), function(...e4) {
        const t3 = At(Ee());
        if (2 === e4.length) {
          const [n4, r2] = e4;
          return n4 ? t3.withSetScope(n4, r2) : t3.withScope(r2);
        }
        t3.withScope(e4[0]);
      }((r2) => {
        var i2, o2;
        r2.addEventProcessor((e4) => (t2.mechanism && (ct(e4, void 0, void 0), lt(e4, t2.mechanism)), e4.extra = { ...e4.extra, arguments: n3 }, e4)), i2 = e3, jt().captureException(i2, Sn(o2));
      }), e3;
    }
  };
  try {
    for (const t3 in e2)
      Object.prototype.hasOwnProperty.call(e2, t3) && (n2[t3] = e2[t3]);
  } catch (e3) {
  }
  Ke(n2, e2), Qe(e2, "__sentry_wrapped__", n2);
  try {
    Object.getOwnPropertyDescriptor(n2, "name").configurable && Object.defineProperty(n2, "name", { get: () => e2.name });
  } catch (e3) {
  }
  return n2;
}
function _r(e2, t2) {
  const n2 = kr(e2, t2), r2 = { type: Er(t2), value: Tr(t2) };
  return n2.length && (r2.stacktrace = { frames: n2 }), void 0 === r2.type && "" === r2.value && (r2.value = "Unrecoverable error caught"), r2;
}
function wr(e2, t2, n2, r2) {
  const i2 = xt(), o2 = i2 && i2.getOptions().normalizeDepth, s2 = function(e3) {
    for (const t3 in e3)
      if (Object.prototype.hasOwnProperty.call(e3, t3)) {
        const n3 = e3[t3];
        if (n3 instanceof Error)
          return n3;
      }
    return;
  }(t2), a2 = { __serialized__: tn(t2, o2) };
  if (s2)
    return { exception: { values: [_r(e2, s2)] }, extra: a2 };
  const c2 = { exception: { values: [{ type: Re(t2) ? t2.constructor.name : r2 ? "UnhandledRejection" : "Error", value: Ar(t2, { isUnhandledRejection: r2 }) }] }, extra: a2 };
  if (n2) {
    const t3 = kr(e2, n2);
    t3.length && (c2.exception.values[0].stacktrace = { frames: t3 });
  }
  return c2;
}
function Sr(e2, t2) {
  return { exception: { values: [_r(e2, t2)] } };
}
function kr(e2, t2) {
  const n2 = t2.stacktrace || t2.stack || "", r2 = function(e3) {
    if (e3 && Mr.test(e3.message))
      return 1;
    return 0;
  }(t2), i2 = function(e3) {
    if ("number" == typeof e3.framesToPop)
      return e3.framesToPop;
    return 0;
  }(t2);
  try {
    return e2(n2, r2, i2);
  } catch (e3) {
  }
  return [];
}
function Cr(e2) {
  return "undefined" != typeof WebAssembly && void 0 !== WebAssembly.Exception && e2 instanceof WebAssembly.Exception;
}
function Er(e2) {
  const t2 = e2 && e2.name;
  if (!t2 && Cr(e2)) {
    return e2.message && Array.isArray(e2.message) && 2 == e2.message.length ? e2.message[0] : "WebAssembly.Exception";
  }
  return t2;
}
function Tr(e2) {
  const t2 = e2 && e2.message;
  return t2 ? t2.error && "string" == typeof t2.error.message ? t2.error.message : Cr(e2) && Array.isArray(e2.message) && 2 == e2.message.length ? e2.message[1] : t2 : "No error message";
}
function Or(e2, t2, n2, r2, i2) {
  let o2;
  if (je(t2) && t2.error) {
    return Sr(e2, t2.error);
  }
  if (Ie(t2) || Ae(t2, "DOMException")) {
    const i3 = t2;
    if ("stack" in t2)
      o2 = Sr(e2, t2);
    else {
      const t3 = i3.name || (Ie(i3) ? "DOMError" : "DOMException"), s2 = i3.message ? `${t3}: ${i3.message}` : t3;
      o2 = Pr(e2, s2, n2, r2), ct(o2, s2);
    }
    return "code" in i3 && (o2.tags = { ...o2.tags, "DOMException.code": `${i3.code}` }), o2;
  }
  if (Pe(t2))
    return Sr(e2, t2);
  if (Ne(t2) || Re(t2)) {
    return o2 = wr(e2, t2, n2, i2), lt(o2, { synthetic: true }), o2;
  }
  return o2 = Pr(e2, t2, n2, r2), ct(o2, `${t2}`, void 0), lt(o2, { synthetic: true }), o2;
}
function Pr(e2, t2, n2, r2) {
  const i2 = {};
  if (r2 && n2) {
    const r3 = kr(e2, n2);
    r3.length && (i2.exception = { values: [{ value: t2, stacktrace: { frames: r3 } }] }), lt(i2, { synthetic: true });
  }
  if (Le(t2)) {
    const { __sentry_template_string__: e3, __sentry_template_values__: n3 } = t2;
    return i2.logentry = { message: e3, params: n3 }, i2;
  }
  return i2.message = t2, i2;
}
function Ar(e2, { isUnhandledRejection: t2 }) {
  const n2 = function(e3, t3 = 40) {
    const n3 = Object.keys(Xe(e3));
    n3.sort();
    const r3 = n3[0];
    if (!r3)
      return "[object has no keys]";
    if (r3.length >= t3)
      return qe(r3, t3);
    for (let e4 = n3.length; e4 > 0; e4--) {
      const r4 = n3.slice(0, e4).join(", ");
      if (!(r4.length > t3))
        return e4 === n3.length ? r4 : qe(r4, t3);
    }
    return "";
  }(e2), r2 = t2 ? "promise rejection" : "exception";
  if (je(e2))
    return `Event \`ErrorEvent\` captured as ${r2} with message \`${e2.message}\``;
  if (Re(e2)) {
    return `Event \`${function(e3) {
      try {
        const t3 = Object.getPrototypeOf(e3);
        return t3 ? t3.constructor.name : void 0;
      } catch (e4) {
      }
    }(e2)}\` (type=${e2.type}) captured as ${r2}`;
  }
  return `Object captured as ${r2} with keys: ${n2}`;
}
function Fr() {
  if (!xr.document)
    return;
  const e2 = we.bind(null, "dom"), t2 = Br(e2, true);
  xr.document.addEventListener("click", t2, false), xr.document.addEventListener("keypress", t2, false), ["EventTarget", "Node"].forEach((t3) => {
    const n2 = xr[t3], r2 = n2 && n2.prototype;
    r2 && r2.hasOwnProperty && r2.hasOwnProperty("addEventListener") && (Ge(r2, "addEventListener", function(t4) {
      return function(n3, r3, i2) {
        if ("click" === n3 || "keypress" == n3)
          try {
            const r4 = this.__sentry_instrumentation_handlers__ = this.__sentry_instrumentation_handlers__ || {}, o2 = r4[n3] = r4[n3] || { refCount: 0 };
            if (!o2.handler) {
              const r5 = Br(e2);
              o2.handler = r5, t4.call(this, n3, r5, i2);
            }
            o2.refCount++;
          } catch (e3) {
          }
        return t4.call(this, n3, r3, i2);
      };
    }), Ge(r2, "removeEventListener", function(e3) {
      return function(t4, n3, r3) {
        if ("click" === t4 || "keypress" == t4)
          try {
            const n4 = this.__sentry_instrumentation_handlers__ || {}, i2 = n4[t4];
            i2 && (i2.refCount--, i2.refCount <= 0 && (e3.call(this, t4, i2.handler, r3), i2.handler = void 0, delete n4[t4]), 0 === Object.keys(n4).length && delete this.__sentry_instrumentation_handlers__);
          } catch (e4) {
          }
        return e3.call(this, t4, n3, r3);
      };
    }));
  });
}
function Br(e2, t2 = false) {
  return (n2) => {
    if (!n2 || n2._sentryCaptured)
      return;
    const r2 = function(e3) {
      try {
        return e3.target;
      } catch (e4) {
        return null;
      }
    }(n2);
    if (function(e3, t3) {
      return "keypress" === e3 && (!t3 || !t3.tagName || "INPUT" !== t3.tagName && "TEXTAREA" !== t3.tagName && !t3.isContentEditable);
    }(n2.type, r2))
      return;
    Qe(n2, "_sentryCaptured", true), r2 && !r2._sentryId && Qe(r2, "_sentryId", ot());
    const i2 = "keypress" === n2.type ? "input" : n2.type;
    if (!function(e3) {
      if (e3.type !== Dr)
        return false;
      try {
        if (!e3.target || e3.target._sentryId !== Nr)
          return false;
      } catch (e4) {
      }
      return true;
    }(n2)) {
      e2({ event: n2, name: i2, global: t2 }), Dr = n2.type, Nr = r2 ? r2._sentryId : void 0;
    }
    clearTimeout(Lr), Lr = xr.setTimeout(() => {
      Nr = void 0, Dr = void 0;
    }, 1e3);
  };
}
function Ur(e2) {
  const t2 = "history";
  be(t2, e2), _e(t2, Vr);
}
function Vr() {
  if (!function() {
    const e3 = fr.chrome, t3 = e3 && e3.app && e3.app.runtime, n2 = "history" in fr && !!fr.history.pushState && !!fr.history.replaceState;
    return !t3 && n2;
  }())
    return;
  const e2 = xr.onpopstate;
  function t2(e3) {
    return function(...t3) {
      const n2 = t3.length > 2 ? t3[2] : void 0;
      if (n2) {
        const e4 = Rr, t4 = String(n2);
        Rr = t4;
        we("history", { from: e4, to: t4 });
      }
      return e3.apply(this, t3);
    };
  }
  xr.onpopstate = function(...t3) {
    const n2 = xr.location.href, r2 = Rr;
    Rr = n2;
    if (we("history", { from: r2, to: n2 }), e2)
      try {
        return e2.apply(this, t3);
      } catch (e3) {
      }
  }, Ge(xr.history, "pushState", t2), Ge(xr.history, "replaceState", t2);
}
function $r(e2) {
  Jr[e2] = void 0;
}
function zr() {
  if (!xr.XMLHttpRequest)
    return;
  const e2 = XMLHttpRequest.prototype;
  e2.open = new Proxy(e2.open, { apply(e3, t2, n2) {
    const r2 = new Error(), i2 = 1e3 * it(), o2 = xe(n2[0]) ? n2[0].toUpperCase() : void 0, s2 = function(e4) {
      if (xe(e4))
        return e4;
      try {
        return e4.toString();
      } catch (e5) {
      }
      return;
    }(n2[1]);
    if (!o2 || !s2)
      return e3.apply(t2, n2);
    t2[qr] = { method: o2, url: s2, request_headers: {} }, "POST" === o2 && s2.match(/sentry_key/) && (t2.__sentry_own_request__ = true);
    const a2 = () => {
      const e4 = t2[qr];
      if (e4 && 4 === t2.readyState) {
        try {
          e4.status_code = t2.status;
        } catch (e5) {
        }
        we("xhr", { endTimestamp: 1e3 * it(), startTimestamp: i2, xhr: t2, virtualError: r2 });
      }
    };
    return "onreadystatechange" in t2 && "function" == typeof t2.onreadystatechange ? t2.onreadystatechange = new Proxy(t2.onreadystatechange, { apply: (e4, t3, n3) => (a2(), e4.apply(t3, n3)) }) : t2.addEventListener("readystatechange", a2), t2.setRequestHeader = new Proxy(t2.setRequestHeader, { apply(e4, t3, n3) {
      const [r3, i3] = n3, o3 = t3[qr];
      return o3 && xe(r3) && xe(i3) && (o3.request_headers[r3.toLowerCase()] = i3), e4.apply(t3, n3);
    } }), e3.apply(t2, n2);
  } }), e2.send = new Proxy(e2.send, { apply(e3, t2, n2) {
    const r2 = t2[qr];
    if (!r2)
      return e3.apply(t2, n2);
    void 0 !== n2[0] && (r2.body = n2[0]);
    return we("xhr", { startTimestamp: 1e3 * it(), xhr: t2 }), e3.apply(t2, n2);
  } });
}
function Wr(e2, t2 = function(e3) {
  const t3 = Jr[e3];
  if (t3)
    return t3;
  let n2 = xr[e3];
  if (lr(n2))
    return Jr[e3] = n2.bind(xr);
  const r2 = xr.document;
  if (r2 && "function" == typeof r2.createElement)
    try {
      const t4 = r2.createElement("iframe");
      t4.hidden = true, r2.head.appendChild(t4);
      const i2 = t4.contentWindow;
      i2 && i2[e3] && (n2 = i2[e3]), r2.head.removeChild(t4);
    } catch (t4) {
      Ir && le.warn(`Could not create sandbox iframe for ${e3} check, bailing to window.${e3}: `, t4);
    }
  return n2 ? Jr[e3] = n2.bind(xr) : n2;
}("fetch")) {
  let n2 = 0, r2 = 0;
  return Un(e2, function(i2) {
    const o2 = i2.body.length;
    n2 += o2, r2++;
    const s2 = { body: i2.body, method: "POST", referrerPolicy: "origin", headers: e2.headers, keepalive: n2 <= 6e4 && r2 < 15, ...e2.fetchOptions };
    if (!t2)
      return $r("fetch"), pt("No fetch implementation available");
    try {
      return t2(e2.url, s2).then((e3) => (n2 -= o2, r2--, { statusCode: e3.status, headers: { "x-sentry-rate-limits": e3.headers.get("X-Sentry-Rate-Limits"), "retry-after": e3.headers.get("Retry-After") } }));
    } catch (e3) {
      return $r("fetch"), n2 -= o2, r2--, pt(e3);
    }
  });
}
function Hr(e2, t2, n2, r2) {
  const i2 = { filename: e2, function: "<anonymous>" === t2 ? ue : t2, in_app: true };
  return void 0 !== n2 && (i2.lineno = n2), void 0 !== r2 && (i2.colno = r2), i2;
}
function oi(e2) {
  return function(...t2) {
    const n2 = t2[0];
    return t2[0] = yr(n2, { mechanism: { data: { function: ve(e2) }, handled: false, type: "instrument" } }), e2.apply(this, t2);
  };
}
function si(e2) {
  return function(t2) {
    return e2.apply(this, [yr(t2, { mechanism: { data: { function: "requestAnimationFrame", handler: ve(e2) }, handled: false, type: "instrument" } })]);
  };
}
function ai(e2) {
  return function(...t2) {
    const n2 = this;
    return ["onload", "onerror", "onprogress", "onreadystatechange"].forEach((e3) => {
      e3 in n2 && "function" == typeof n2[e3] && Ge(n2, e3, function(t3) {
        const n3 = { mechanism: { data: { function: e3, handler: ve(t3) }, handled: false, type: "instrument" } }, r2 = Ye(t3);
        return r2 && (n3.mechanism.data.handler = ve(r2)), yr(t3, n3);
      });
    }), e2.apply(this, t2);
  };
}
function ci(e2) {
  const t2 = vr[e2], n2 = t2 && t2.prototype;
  n2 && n2.hasOwnProperty && n2.hasOwnProperty("addEventListener") && (Ge(n2, "addEventListener", function(t3) {
    return function(n3, r2, i2) {
      try {
        "function" == typeof r2.handleEvent && (r2.handleEvent = yr(r2.handleEvent, { mechanism: { data: { function: "handleEvent", handler: ve(r2), target: e2 }, handled: false, type: "instrument" } }));
      } catch (e3) {
      }
      return t3.apply(this, [n3, yr(r2, { mechanism: { data: { function: "addEventListener", handler: ve(r2), target: e2 }, handled: false, type: "instrument" } }), i2]);
    };
  }), Ge(n2, "removeEventListener", function(e3) {
    return function(t3, n3, r2) {
      try {
        const i2 = n3.__sentry_wrapped__;
        i2 && e3.call(this, t3, i2, r2);
      } catch (e4) {
      }
      return e3.call(this, t3, n3, r2);
    };
  }));
}
function di(e2) {
  br && le.log(`Global Handler attached: ${e2}`);
}
function hi() {
  const e2 = xt();
  return e2 && e2.getOptions() || { stackParser: () => [], attachStacktrace: false };
}
function ms() {
  return !ys() && "undefined" != typeof window && window.navigator && window.navigator.userAgent ? window.navigator.userAgent : "";
}
function ys() {
  return "undefined" != typeof navigator && navigator.product && "ReactNative" === navigator.product;
}
function bs() {
  return navigator && navigator.mediaDevices && navigator.mediaDevices.getUserMedia;
}
function _s() {
  return !!(navigator && navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) && (function(e2, t2) {
    if (!e2 || !t2)
      return true;
    switch (e2) {
      case "Chrome":
        return t2.major >= 75;
      case "Safari":
        return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection") && !(13 === t2.major && 0 === t2.minor && 0 === t2.point);
      case "Firefox":
        return t2.major >= 67;
    }
    return true;
  }(js(), Is()) || ys());
}
function ws() {
  if (ys())
    return false;
  if (!document)
    return false;
  var e2 = document.createElement("iframe");
  return !!e2.requestFullscreen || !!e2.webkitRequestFullscreen;
}
function Es() {
  var e2 = arguments.length > 0 && void 0 !== arguments[0] && arguments[0];
  return !ys() && (Cs !== Ss && (e2 ? function() {
    if (As())
      return false;
    return ["Chrome", "Firefox"].includes(js());
  }() : function() {
    if (As())
      return false;
    var e3 = js();
    if ("Safari" === e3) {
      var t2 = Ns();
      if (t2.major < 15 || 15 === t2.major && t2.minor < 4)
        return false;
    }
    if ("Chrome" === e3) {
      return xs().major >= 77;
    }
    if ("Firefox" === e3) {
      return Rs().major >= 97;
    }
    return ["Chrome", "Firefox", "Safari"].includes(e3);
  }()));
}
function Ts() {
  if (ys())
    return false;
  if (Ps())
    return false;
  if ("undefined" == typeof AudioWorkletNode)
    return false;
  switch (js()) {
    case "Chrome":
    case "Firefox":
      return true;
    case "Safari":
      var e2 = Is();
      return e2.major > 17 || 17 === e2.major && e2.minor >= 4;
  }
  return false;
}
function Os() {
  return bs() && !function() {
    var e2, t2 = js();
    if (!ms())
      return true;
    switch (t2) {
      case "Chrome":
        return (e2 = xs()).major && e2.major > 0 && e2.major < 75;
      case "Firefox":
        return (e2 = Rs()).major < 91;
      case "Safari":
        return (e2 = Ns()).major < 13 || 13 === e2.major && e2.minor < 1;
      default:
        return true;
    }
  }();
}
function Ps() {
  return ms().match(/Linux; Android/);
}
function As() {
  var e2, t2 = ms(), n2 = t2.match(/Mac/) && (!ys() && "undefined" != typeof window && null !== (e2 = window) && void 0 !== e2 && null !== (e2 = e2.navigator) && void 0 !== e2 && e2.maxTouchPoints ? window.navigator.maxTouchPoints : 0) >= 5;
  return !!(t2.match(/Mobi/) || t2.match(/Android/) || n2) || (!!ms().match(/DailyAnd\//) || void 0);
}
function js() {
  if ("undefined" != typeof window) {
    var e2 = ms();
    return Ls() ? "Safari" : e2.indexOf("Edge") > -1 ? "Edge" : e2.match(/Chrome\//) ? "Chrome" : e2.indexOf("Safari") > -1 || Ds() ? "Safari" : e2.indexOf("Firefox") > -1 ? "Firefox" : e2.indexOf("MSIE") > -1 || e2.indexOf(".NET") > -1 ? "IE" : "Unknown Browser";
  }
}
function Is() {
  switch (js()) {
    case "Chrome":
      return xs();
    case "Safari":
      return Ns();
    case "Firefox":
      return Rs();
    case "Edge":
      return function() {
        var e2 = 0, t2 = 0;
        if ("undefined" != typeof window) {
          var n2 = ms().match(/Edge\/(\d+).(\d+)/);
          if (n2)
            try {
              e2 = parseInt(n2[1]), t2 = parseInt(n2[2]);
            } catch (e3) {
            }
        }
        return { major: e2, minor: t2 };
      }();
  }
}
function xs() {
  var e2 = 0, t2 = 0, n2 = 0, r2 = 0, i2 = false;
  if ("undefined" != typeof window) {
    var o2 = ms(), s2 = o2.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);
    if (s2)
      try {
        e2 = parseInt(s2[1]), t2 = parseInt(s2[2]), n2 = parseInt(s2[3]), r2 = parseInt(s2[4]), i2 = o2.indexOf("OPR/") > -1;
      } catch (e3) {
      }
  }
  return { major: e2, minor: t2, build: n2, patch: r2, opera: i2 };
}
function Ls() {
  return !!ms().match(/iPad|iPhone|iPod/i) && bs();
}
function Ds() {
  return ms().indexOf("AppleWebKit/605.1.15") > -1;
}
function Ns() {
  var e2 = 0, t2 = 0, n2 = 0;
  if ("undefined" != typeof window) {
    var r2 = ms().match(/Version\/(\d+).(\d+)(.(\d+))?/);
    if (r2)
      try {
        e2 = parseInt(r2[1]), t2 = parseInt(r2[2]), n2 = parseInt(r2[4]);
      } catch (e3) {
      }
    else
      (Ls() || Ds()) && (e2 = 14, t2 = 0, n2 = 3);
  }
  return { major: e2, minor: t2, point: n2 };
}
function Rs() {
  var e2 = 0, t2 = 0;
  if ("undefined" != typeof window) {
    var n2 = ms().match(/Firefox\/(\d+).(\d+)/);
    if (n2)
      try {
        e2 = parseInt(n2[1]), t2 = parseInt(n2[2]);
      } catch (e3) {
      }
  }
  return { major: e2, minor: t2 };
}
function Bs(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function Us(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? Bs(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : Bs(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
function Vs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Vs = function() {
    return !!e2;
  })();
}
function $s(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function qs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (qs = function() {
    return !!e2;
  })();
}
function Ks() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Ks = function() {
    return !!e2;
  })();
}
function Ys(e2) {
  var t2 = "function" == typeof Map ? /* @__PURE__ */ new Map() : void 0;
  return Ys = function(e3) {
    if (null === e3 || !function(e4) {
      try {
        return -1 !== Function.toString.call(e4).indexOf("[native code]");
      } catch (t3) {
        return "function" == typeof e4;
      }
    }(e3))
      return e3;
    if ("function" != typeof e3)
      throw new TypeError("Super expression must either be null or a function");
    if (void 0 !== t2) {
      if (t2.has(e3))
        return t2.get(e3);
      t2.set(e3, n2);
    }
    function n2() {
      return function(e4, t3, n3) {
        if (Ks())
          return Reflect.construct.apply(null, arguments);
        var r2 = [null];
        r2.push.apply(r2, t3);
        var i2 = new (e4.bind.apply(e4, r2))();
        return n3 && c(i2, n3.prototype), i2;
      }(e3, arguments, a(this).constructor);
    }
    return n2.prototype = Object.create(e3.prototype, { constructor: { value: n2, enumerable: false, writable: true, configurable: true } }), c(n2, e3);
  }, Ys(e2);
}
function Xs() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (Xs = function() {
    return !!e2;
  })();
}
function Zs(e2) {
  var t2, n2 = null === (t2 = window._daily) || void 0 === t2 ? void 0 : t2.pendings;
  if (n2) {
    var r2 = n2.indexOf(e2);
    -1 !== r2 && n2.splice(r2, 1);
  }
}
function pa(e2, t2) {
  for (var n2 = t2.getState(), r2 = 0, i2 = ["cam", "screen"]; r2 < i2.length; r2++)
    for (var o2 = i2[r2], s2 = 0, a2 = ["video", "audio"]; s2 < a2.length; s2++) {
      var c2 = a2[s2], l2 = "cam" === o2 ? c2 : "screen".concat(c2.charAt(0).toUpperCase() + c2.slice(1)), u2 = e2.tracks[l2];
      if (u2) {
        var d2 = e2.local ? ca(n2, o2, c2) : la(n2, e2.session_id, o2, c2);
        "playable" === u2.state && (u2.track = d2), u2.persistentTrack = d2;
      }
    }
}
function fa(e2, t2) {
  try {
    var n2 = t2.getState();
    for (var r2 in e2.tracks)
      if (!va(r2)) {
        var i2 = e2.tracks[r2].kind;
        if (i2) {
          var o2 = e2.tracks[r2];
          if (o2) {
            var s2 = e2.local ? ha(n2, r2) : la(n2, e2.session_id, r2, i2);
            "playable" === o2.state && (e2.tracks[r2].track = s2), o2.persistentTrack = s2;
          }
        } else
          console.error("unknown type for custom track");
      }
  } catch (e3) {
    console.error(e3);
  }
}
function va(e2) {
  return ["video", "audio", "screenVideo", "screenAudio"].includes(e2);
}
function ga(e2, t2, n2) {
  var r2 = n2.getState();
  if (e2.local) {
    if (e2.audio)
      try {
        e2.audioTrack = r2.local.streams.cam.stream.getAudioTracks()[0], e2.audioTrack || (e2.audio = false);
      } catch (e3) {
      }
    if (e2.video)
      try {
        e2.videoTrack = r2.local.streams.cam.stream.getVideoTracks()[0], e2.videoTrack || (e2.video = false);
      } catch (e3) {
      }
    if (e2.screen)
      try {
        e2.screenVideoTrack = r2.local.streams.screen.stream.getVideoTracks()[0], e2.screenAudioTrack = r2.local.streams.screen.stream.getAudioTracks()[0], e2.screenVideoTrack || e2.screenAudioTrack || (e2.screen = false);
      } catch (e3) {
      }
  } else {
    var i2 = true;
    try {
      var o2 = r2.participants[e2.session_id];
      o2 && o2.public && o2.public.rtcType && "peer-to-peer" === o2.public.rtcType.impl && o2.private && !["connected", "completed"].includes(o2.private.peeringState) && (i2 = false);
    } catch (e3) {
      console.error(e3);
    }
    if (!i2)
      return e2.audio = false, e2.audioTrack = false, e2.video = false, e2.videoTrack = false, e2.screen = false, void (e2.screenTrack = false);
    try {
      r2.streams;
      if (e2.audio && aa(r2, e2.session_id, "cam-audio")) {
        var s2 = la(r2, e2.session_id, "cam", "audio");
        s2 && (t2 && t2.audioTrack && t2.audioTrack.id === s2.id ? e2.audioTrack = s2 : s2.muted || (e2.audioTrack = s2)), e2.audioTrack || (e2.audio = false);
      }
      if (e2.video && aa(r2, e2.session_id, "cam-video")) {
        var a2 = la(r2, e2.session_id, "cam", "video");
        a2 && (t2 && t2.videoTrack && t2.videoTrack.id === a2.id ? e2.videoTrack = a2 : a2.muted || (e2.videoTrack = a2)), e2.videoTrack || (e2.video = false);
      }
      if (e2.screen && aa(r2, e2.session_id, "screen-audio")) {
        var c2 = la(r2, e2.session_id, "screen", "audio");
        c2 && (t2 && t2.screenAudioTrack && t2.screenAudioTrack.id === c2.id ? e2.screenAudioTrack = c2 : c2.muted || (e2.screenAudioTrack = c2));
      }
      if (e2.screen && aa(r2, e2.session_id, "screen-video")) {
        var l2 = la(r2, e2.session_id, "screen", "video");
        l2 && (t2 && t2.screenVideoTrack && t2.screenVideoTrack.id === l2.id ? e2.screenVideoTrack = l2 : l2.muted || (e2.screenVideoTrack = l2));
      }
      e2.screenVideoTrack || e2.screenAudioTrack || (e2.screen = false);
    } catch (e3) {
      console.error("unexpected error matching up tracks", e3);
    }
  }
}
function ma(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3)
          return ya(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? ya(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2)
        throw o2;
    }
  } };
}
function ya(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++)
    r2[n2] = e2[n2];
  return r2;
}
function wa(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3)
          return Sa(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? Sa(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2)
        throw o2;
    }
  } };
}
function Sa(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++)
    r2[n2] = e2[n2];
  return r2;
}
function Ca(e2) {
  Ta() ? function(e3) {
    ba.has(e3) || (ba.set(e3, {}), navigator.mediaDevices.enumerateDevices().then(function(t2) {
      ba.has(e3) && (ba.get(e3).lastDevicesString = JSON.stringify(t2), _a || (_a = function() {
        var e4 = h(function* () {
          var e5, t3 = yield navigator.mediaDevices.enumerateDevices(), n2 = ma(ba.keys());
          try {
            for (n2.s(); !(e5 = n2.n()).done; ) {
              var r2 = e5.value, i2 = JSON.stringify(t3);
              i2 !== ba.get(r2).lastDevicesString && (ba.get(r2).lastDevicesString = i2, r2(t3));
            }
          } catch (e6) {
            n2.e(e6);
          } finally {
            n2.f();
          }
        });
        return function() {
          return e4.apply(this, arguments);
        };
      }(), navigator.mediaDevices.addEventListener("devicechange", _a)));
    }).catch(function() {
    }));
  }(e2) : function(e3) {
    ka.has(e3) || (ka.set(e3, {}), navigator.mediaDevices.enumerateDevices().then(function(t2) {
      ka.has(e3) && (ka.get(e3).lastDevicesString = JSON.stringify(t2), Ma || (Ma = setInterval(h(function* () {
        var e4, t3 = yield navigator.mediaDevices.enumerateDevices(), n2 = wa(ka.keys());
        try {
          for (n2.s(); !(e4 = n2.n()).done; ) {
            var r2 = e4.value, i2 = JSON.stringify(t3);
            i2 !== ka.get(r2).lastDevicesString && (ka.get(r2).lastDevicesString = i2, r2(t3));
          }
        } catch (e5) {
          n2.e(e5);
        } finally {
          n2.f();
        }
      }), 3e3)));
    }));
  }(e2);
}
function Ea(e2) {
  Ta() ? function(e3) {
    ba.has(e3) && (ba.delete(e3), 0 === ba.size && _a && (navigator.mediaDevices.removeEventListener("devicechange", _a), _a = null));
  }(e2) : function(e3) {
    ka.has(e3) && (ka.delete(e3), 0 === ka.size && Ma && (clearInterval(Ma), Ma = null));
  }(e2);
}
function Ta() {
  var e2;
  return ys() || void 0 !== (null === (e2 = navigator.mediaDevices) || void 0 === e2 ? void 0 : e2.ondevicechange);
}
function Pa(e2, t2) {
  var n2 = t2.isLocalScreenVideo;
  return e2 && "live" === e2.readyState && !function(e3, t3) {
    return (!t3.isLocalScreenVideo || "Chrome" !== js()) && e3.muted && !Oa.has(e3.id);
  }(e2, { isLocalScreenVideo: n2 });
}
function Aa(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function ja(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? Aa(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : Aa(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
function $a(e2, t2) {
  var n2 = Object.keys(e2);
  if (Object.getOwnPropertySymbols) {
    var r2 = Object.getOwnPropertySymbols(e2);
    t2 && (r2 = r2.filter(function(t3) {
      return Object.getOwnPropertyDescriptor(e2, t3).enumerable;
    })), n2.push.apply(n2, r2);
  }
  return n2;
}
function qa(e2) {
  for (var t2 = 1; t2 < arguments.length; t2++) {
    var n2 = null != arguments[t2] ? arguments[t2] : {};
    t2 % 2 ? $a(Object(n2), true).forEach(function(t3) {
      u(e2, t3, n2[t3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e2, Object.getOwnPropertyDescriptors(n2)) : $a(Object(n2)).forEach(function(t3) {
      Object.defineProperty(e2, t3, Object.getOwnPropertyDescriptor(n2, t3));
    });
  }
  return e2;
}
function za() {
  try {
    var e2 = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function() {
    }));
  } catch (e3) {
  }
  return (za = function() {
    return !!e2;
  })();
}
function Wa(e2, t2) {
  var n2 = "undefined" != typeof Symbol && e2[Symbol.iterator] || e2["@@iterator"];
  if (!n2) {
    if (Array.isArray(e2) || (n2 = function(e3, t3) {
      if (e3) {
        if ("string" == typeof e3)
          return Ha(e3, t3);
        var n3 = {}.toString.call(e3).slice(8, -1);
        return "Object" === n3 && e3.constructor && (n3 = e3.constructor.name), "Map" === n3 || "Set" === n3 ? Array.from(e3) : "Arguments" === n3 || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n3) ? Ha(e3, t3) : void 0;
      }
    }(e2)) || t2 && e2 && "number" == typeof e2.length) {
      n2 && (e2 = n2);
      var r2 = 0, i2 = function() {
      };
      return { s: i2, n: function() {
        return r2 >= e2.length ? { done: true } : { done: false, value: e2[r2++] };
      }, e: function(e3) {
        throw e3;
      }, f: i2 };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
  }
  var o2, s2 = true, a2 = false;
  return { s: function() {
    n2 = n2.call(e2);
  }, n: function() {
    var e3 = n2.next();
    return s2 = e3.done, e3;
  }, e: function(e3) {
    a2 = true, o2 = e3;
  }, f: function() {
    try {
      s2 || null == n2.return || n2.return();
    } finally {
      if (a2)
        throw o2;
    }
  } };
}
function Ha(e2, t2) {
  (null == t2 || t2 > e2.length) && (t2 = e2.length);
  for (var n2 = 0, r2 = Array(t2); n2 < t2; n2++)
    r2[n2] = e2[n2];
  return r2;
}
function cc(e2, t2) {
  var n2 = {};
  for (var r2 in e2)
    if (e2[r2] instanceof MediaStreamTrack)
      console.warn("MediaStreamTrack found in props or cache.", r2), n2[r2] = ls;
    else if ("dailyConfig" === r2) {
      if (e2[r2].modifyLocalSdpHook) {
        var i2 = window._daily.instances[t2].customCallbacks || {};
        i2.modifyLocalSdpHook = e2[r2].modifyLocalSdpHook, window._daily.instances[t2].customCallbacks = i2, delete e2[r2].modifyLocalSdpHook;
      }
      if (e2[r2].modifyRemoteSdpHook) {
        var o2 = window._daily.instances[t2].customCallbacks || {};
        o2.modifyRemoteSdpHook = e2[r2].modifyRemoteSdpHook, window._daily.instances[t2].customCallbacks = o2, delete e2[r2].modifyRemoteSdpHook;
      }
      n2[r2] = e2[r2];
    } else
      n2[r2] = e2[r2];
  return n2;
}
function lc(e2) {
  var t2 = arguments.length > 2 ? arguments[2] : void 0;
  if (e2 !== bi) {
    var n2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " only supported after join.");
    throw t2 && (n2 += " ".concat(t2)), console.error(n2), new Error(n2);
  }
}
function uc(e2, t2) {
  return [yi, bi].includes(e2) || t2;
}
function dc(e2, t2) {
  var n2 = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "This daily-js method", r2 = arguments.length > 3 ? arguments[3] : void 0;
  if (uc(e2, t2)) {
    var i2 = "".concat(n2, " not supported after joining a meeting.");
    throw r2 && (i2 += " ".concat(r2)), console.error(i2), new Error(i2);
  }
}
function hc(e2) {
  var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", n2 = arguments.length > 2 ? arguments[2] : void 0;
  if (!e2) {
    var r2 = "".concat(t2, arguments.length > 3 && void 0 !== arguments[3] && arguments[3] ? " requires preAuth() or startCamera() to initialize call state." : " requires preAuth(), startCamera(), or join() to initialize call state.");
    throw n2 && (r2 += " ".concat(n2)), console.error(r2), new Error(r2);
  }
}
function pc(e2) {
  if (e2) {
    var t2 = "A pre-call quality test is in progress. Please try ".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " again once testing has completed. Use stopTestCallQuality() to end it early.");
    throw console.error(t2), new Error(t2);
  }
}
function fc(e2) {
  if (!e2) {
    var t2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " is only supported on custom callObject instances");
    throw console.error(t2), new Error(t2);
  }
}
function vc(e2) {
  if (e2) {
    var t2 = "".concat(arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "This daily-js method", " is only supported as part of Daily's Prebuilt");
    throw console.error(t2), new Error(t2);
  }
}
function gc() {
  if (ys())
    throw new Error("This daily-js method is not currently supported in React Native");
}
function mc() {
  if (!ys())
    throw new Error("This daily-js method is only supported in React Native");
}
function yc(e2) {
  if (void 0 === e2)
    return true;
  var t2;
  if ("string" == typeof e2)
    t2 = e2;
  else
    try {
      t2 = JSON.stringify(e2), N(JSON.parse(t2), e2) || console.warn("The userData provided will be modified when serialized.");
    } catch (e3) {
      throw Error("userData must be serializable to JSON: ".concat(e3));
    }
  if (t2.length > 4096)
    throw Error("userData is too large (".concat(t2.length, " characters). Maximum size suppported is ").concat(4096, "."));
  return true;
}
function bc(e2, t2) {
  for (var n2 = t2.allowAllParticipantsKey, r2 = function(e3) {
    var t3 = ["local"];
    return n2 || t3.push("*"), e3 && !t3.includes(e3);
  }, i2 = function(e3) {
    return !!(void 0 === e3.layer || Number.isInteger(e3.layer) && e3.layer >= 0 || "inherit" === e3.layer);
  }, o2 = function(e3) {
    return !!e3 && (!(e3.video && !i2(e3.video)) && !(e3.screenVideo && !i2(e3.screenVideo)));
  }, s2 = 0, a2 = Object.entries(e2); s2 < a2.length; s2++) {
    var c2 = f(a2[s2], 2), l2 = c2[0], u2 = c2[1];
    if (!r2(l2) || !o2(u2))
      return false;
  }
  return true;
}
function _c(e2) {
  if ("object" !== n(e2))
    return false;
  for (var t2 = 0, r2 = Object.entries(e2); t2 < r2.length; t2++) {
    var i2 = f(r2[t2], 2), o2 = i2[0], s2 = i2[1];
    switch (o2) {
      case "video":
        if ("object" !== n(s2))
          return false;
        for (var a2 = 0, c2 = Object.entries(s2); a2 < c2.length; a2++) {
          var l2 = f(c2[a2], 2), u2 = l2[0], d2 = l2[1];
          switch (u2) {
            case "processor":
              if (!kc(d2))
                return false;
              break;
            case "settings":
              if (!Mc(d2))
                return false;
              break;
            default:
              return false;
          }
        }
        break;
      case "audio":
        if ("object" !== n(s2))
          return false;
        for (var h2 = 0, p2 = Object.entries(s2); h2 < p2.length; h2++) {
          var v2 = f(p2[h2], 2), g2 = v2[0], m2 = v2[1];
          switch (g2) {
            case "processor":
              if (!Sc(m2))
                return false;
              break;
            case "settings":
              if (!Mc(m2))
                return false;
              break;
            default:
              return false;
          }
        }
        break;
      default:
        return false;
    }
  }
  return true;
}
function wc(e2, t2, n2) {
  var r2, i2 = [];
  e2.video && e2.video.processor && (Es(null !== (r2 = null == t2 ? void 0 : t2.useLegacyVideoProcessor) && void 0 !== r2 && r2) || (e2.video.settings ? delete e2.video.processor : delete e2.video, i2.push("video")));
  e2.audio && e2.audio.processor && (Ts() || (e2.audio.settings ? delete e2.audio.processor : delete e2.audio, i2.push("audio"))), i2.length > 0 && console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(i2.join(", "))), e2.audio && e2.audio.settings && (e2.audio.settings.customTrack ? (n2.audioTrack = e2.audio.settings.customTrack, e2.audio.settings = { customTrack: ls }) : delete n2.audioTrack), e2.video && e2.video.settings && (e2.video.settings.customTrack ? (n2.videoTrack = e2.video.settings.customTrack, e2.video.settings = { customTrack: ls }) : delete n2.videoTrack);
}
function Sc(e2) {
  if (ys())
    return console.warn("Video processing is not yet supported in React Native"), false;
  var t2 = ["type"];
  return !!e2 && ("object" === n(e2) && (Object.keys(e2).filter(function(e3) {
    return !t2.includes(e3);
  }).forEach(function(t3) {
    console.warn("invalid key inputSettings -> audio -> processor : ".concat(t3)), delete e2[t3];
  }), !!function(e3) {
    if ("string" != typeof e3)
      return false;
    if (!Object.values(ds).includes(e3))
      return console.error("inputSettings audio processor type invalid"), false;
    return true;
  }(e2.type)));
}
function kc(e2) {
  if (ys())
    return console.warn("Video processing is not yet supported in React Native"), false;
  var t2 = ["type", "config"];
  if (!e2)
    return false;
  if ("object" !== n(e2))
    return false;
  if (!function(e3) {
    if ("string" != typeof e3)
      return false;
    if (!Object.values(us).includes(e3))
      return console.error("inputSettings video processor type invalid"), false;
    return true;
  }(e2.type))
    return false;
  if (e2.config) {
    if ("object" !== n(e2.config))
      return false;
    if (!function(e3, t3) {
      var n2 = Object.keys(t3);
      if (0 === n2.length)
        return true;
      var r2 = "invalid object in inputSettings -> video -> processor -> config";
      switch (e3) {
        case us.BGBLUR:
          return n2.length > 1 || "strength" !== n2[0] ? (console.error(r2), false) : !("number" != typeof t3.strength || t3.strength <= 0 || t3.strength > 1 || isNaN(t3.strength)) || (console.error("".concat(r2, "; expected: {0 < strength <= 1}, got: ").concat(t3.strength)), false);
        case us.BGIMAGE:
          return !(void 0 !== t3.source && !function(e4) {
            if ("default" === e4.source)
              return e4.type = "default", true;
            if (e4.source instanceof ArrayBuffer)
              return true;
            if (ee(e4.source))
              return e4.type = "url", !!function(e5) {
                var t5 = new URL(e5), n4 = t5.pathname;
                if ("data:" === t5.protocol)
                  try {
                    var r3 = n4.substring(n4.indexOf(":") + 1, n4.indexOf(";")).split("/")[1];
                    return ps.includes(r3);
                  } catch (e6) {
                    return console.error("failed to deduce blob content type", e6), false;
                  }
                var i2 = n4.split(".").at(-1).toLowerCase().trim();
                return ps.includes(i2);
              }(e4.source) || (console.error("invalid image type; supported types: [".concat(ps.join(", "), "]")), false);
            return t4 = e4.source, n3 = Number(t4), isNaN(n3) || !Number.isInteger(n3) || n3 <= 0 || n3 > 10 ? (console.error("invalid image selection; must be an int, > 0, <= ".concat(10)), false) : (e4.type = "daily-preselect", true);
            var t4, n3;
          }(t3));
        default:
          return true;
      }
    }(e2.type, e2.config))
      return false;
  }
  return Object.keys(e2).filter(function(e3) {
    return !t2.includes(e3);
  }).forEach(function(t3) {
    console.warn("invalid key inputSettings -> video -> processor : ".concat(t3)), delete e2[t3];
  }), true;
}
function Mc(e2) {
  return "object" === n(e2) && (!e2.customTrack || e2.customTrack instanceof MediaStreamTrack);
}
function Cc() {
  var e2 = Object.values(us).join(" | "), t2 = Object.values(ds).join(" | ");
  return "inputSettings must be of the form: { video?: { processor?: { type: [ ".concat(e2, " ], config?: {} } }, audio?: { processor: {type: [ ").concat(t2, " ] } } }");
}
function Ec(e2) {
  var t2 = e2.allowAllParticipantsKey;
  return "receiveSettings must be of the form { [<remote participant id> | ".concat(Ii).concat(t2 ? ' | "'.concat("*", '"') : "", "]: ") + '{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}';
}
function Tc() {
  return "customIntegrations should be an object of type ".concat(JSON.stringify(ic), ".");
}
function Oc(e2) {
  if (e2 && "object" !== n(e2) || Array.isArray(e2))
    return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(rc), ".")), false;
  if (e2)
    for (var t2 = 0, r2 = Object.entries(e2); t2 < r2.length; t2++)
      for (var i2 = f(r2[t2], 1)[0], o2 = 0, s2 = Object.entries(e2[i2]); o2 < s2.length; o2++) {
        var a2 = f(s2[o2], 2), c2 = a2[0], l2 = a2[1], u2 = rc.id[c2];
        if (!u2)
          return console.error("customTrayButton does not support key ".concat(c2)), false;
        switch (c2) {
          case "iconPath":
          case "iconPathDarkMode":
            if (!ee(l2))
              return console.error("customTrayButton ".concat(c2, " should be a url.")), false;
            break;
          case "visualState":
            if (!["default", "sidebar-open", "active"].includes(l2))
              return console.error("customTrayButton ".concat(c2, " should be ").concat(u2, ". Got: ").concat(l2)), false;
            break;
          default:
            if (n(l2) !== u2)
              return console.error("customTrayButton ".concat(c2, " should be a ").concat(u2, ".")), false;
        }
      }
  return true;
}
function Pc(e2) {
  if (!e2 || e2 && "object" !== n(e2) || Array.isArray(e2))
    return console.error(Tc()), false;
  for (var t2 = function(e3) {
    return "".concat(e3, " should be ").concat(ic.id[e3]);
  }, r2 = function(e3, t3) {
    return console.error("customIntegration ".concat(e3, ": ").concat(t3));
  }, i2 = 0, o2 = Object.entries(e2); i2 < o2.length; i2++) {
    var s2 = f(o2[i2], 1)[0];
    if (!("label" in e2[s2]))
      return r2(s2, "label is required"), false;
    if (!("location" in e2[s2]))
      return r2(s2, "location is required"), false;
    if (!("src" in e2[s2]) && !("srcdoc" in e2[s2]))
      return r2(s2, "src or srcdoc is required"), false;
    for (var a2 = 0, c2 = Object.entries(e2[s2]); a2 < c2.length; a2++) {
      var l2 = f(c2[a2], 2), u2 = l2[0], d2 = l2[1];
      switch (u2) {
        case "allow":
        case "csp":
        case "name":
        case "referrerPolicy":
        case "sandbox":
          if ("string" != typeof d2)
            return r2(s2, t2(u2)), false;
          break;
        case "iconURL":
          if (!ee(d2))
            return r2(s2, "".concat(u2, " should be a url")), false;
          break;
        case "src":
          if ("srcdoc" in e2[s2])
            return r2(s2, "cannot have both src and srcdoc"), false;
          if (!ee(d2))
            return r2(s2, 'src "'.concat(d2, '" is not a valid URL')), false;
          break;
        case "srcdoc":
          if ("src" in e2[s2])
            return r2(s2, "cannot have both src and srcdoc"), false;
          if ("string" != typeof d2)
            return r2(s2, t2(u2)), false;
          break;
        case "location":
          if (!["main", "sidebar"].includes(d2))
            return r2(s2, t2(u2)), false;
          break;
        case "controlledBy":
          if ("*" !== d2 && "owners" !== d2 && (!Array.isArray(d2) || d2.some(function(e3) {
            return "string" != typeof e3;
          })))
            return r2(s2, t2(u2)), false;
          break;
        case "shared":
          if ((!Array.isArray(d2) || d2.some(function(e3) {
            return "string" != typeof e3;
          })) && "owners" !== d2 && "boolean" != typeof d2)
            return r2(s2, t2(u2)), false;
          break;
        default:
          if (!ic.id[u2])
            return console.error("customIntegration does not support key ".concat(u2)), false;
      }
    }
  }
  return true;
}
function Ac(e2, t2) {
  if (void 0 === t2)
    return false;
  switch (n(t2)) {
    case "string":
      return n(e2) === t2;
    case "object":
      if ("object" !== n(e2))
        return false;
      for (var r2 in e2)
        if (!Ac(e2[r2], t2[r2]))
          return false;
      return true;
    default:
      return false;
  }
}
function jc(e2, t2) {
  var n2 = e2.sessionId, r2 = e2.toEndPoint, i2 = e2.callerId, o2 = e2.useSipRefer;
  if (!n2 || !r2)
    throw new Error("".concat(t2, "() requires a sessionId and toEndPoint"));
  if ("string" != typeof n2 || "string" != typeof r2)
    throw new Error("Invalid paramater: sessionId and toEndPoint must be of type string");
  if (o2 && !r2.startsWith("sip:"))
    throw new Error('"toEndPoint" must be a "sip" address');
  if (!r2.startsWith("sip:") && !r2.startsWith("+"))
    throw new Error("toEndPoint: ".concat(r2, ' must starts with either "sip:" or "+"'));
  if (i2 && "string" != typeof i2)
    throw new Error("callerId must be of type string");
  if (i2 && !r2.startsWith("+"))
    throw new Error("callerId is only valid when transferring to a PSTN number");
}
function Ic(e2) {
  if ("object" !== n(e2))
    throw new Error('RemoteMediaPlayerSettings: must be "object" type');
  if (e2.state && !Object.values(hs).includes(e2.state))
    throw new Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: " + JSON.stringify(hs));
  if (e2.volume) {
    if ("number" != typeof e2.volume)
      throw new Error('RemoteMediaPlayerSettings.volume: must be "number" type');
    if (e2.volume < 0 || e2.volume > 2)
      throw new Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0");
  }
}
function xc(e2, t2, n2) {
  return !("number" != typeof e2 || e2 < t2 || e2 > n2);
}
function Lc(e2, t2) {
  return e2 && !t2 && delete e2.data, e2;
}
var g, m, y, b, _, S, I, x, L, R, F, B, U, V, J, $, q, z, W, H, G, Q, te, ne, re, oe, se, ae, le, ue, de, he, fe, me, ye, Se, Me, Oe, Ve, it, dt, ft, _t, kt, Mt, Ct, Nt, Bt, Ht, Kt, un, vn, gn, mn, kn, An, xn, Ln, Dn, Jn, qn, zn, Wn, Hn, Gn, rr, cr, fr, vr, gr, br, Mr, jr, Ir, xr, Lr, Dr, Nr, Rr, Jr, qr, Gr, Qr, Kr, Yr, Xr, Zr, ei, ti, ni, ri, ii, li, ui, pi, fi, vi, gi, mi, yi, bi, _i, wi, Si, ki, Mi, Ci, Ei, Ti, Oi, Pi, Ai, ji, Ii, xi, Li, Di, Ni, Ri, Fi, Bi, Ui, Vi, Ji, $i, qi, zi, Wi, Hi, Gi, Qi, Ki, Yi, Xi, Zi, eo, to, no, ro, io, oo, so, ao, co, lo, uo, ho, po, fo, vo, go, mo, yo, bo, _o, wo, So, ko, Mo, Co, Eo, To, Oo, Po, Ao, jo, Io, xo, Lo, Do, No, Ro, Fo, Bo, Uo, Vo, Jo, $o, qo, zo, Wo, Ho, Go, Qo, Ko, Yo, Xo, Zo, es, ts, ns, rs, is, os, ss, as, cs, ls, us, ds, hs, ps, fs, vs, gs, Ss, ks, Ms, Cs, Fs, Js, zs, Ws, Hs, Gs, Qs, ea, ta, na, ra, ia, oa, sa, aa, ca, la, ua, da, ha, ba, _a, ka, Ma, Oa, Ia, xa, La, Da, Na, Ra, Fa, Ba, Ua, Va, Ja, Ga, Qa, Ka, Ya, Xa, Za, ec, tc, nc, rc, ic, oc, sc, ac;
var init_daily_esm = __esm({
  "node_modules/@daily-co/daily-js/dist/daily-esm.js"() {
    m = { exports: {} };
    y = "object" == typeof Reflect ? Reflect : null;
    b = y && "function" == typeof y.apply ? y.apply : function(e2, t2, n2) {
      return Function.prototype.apply.call(e2, t2, n2);
    };
    g = y && "function" == typeof y.ownKeys ? y.ownKeys : Object.getOwnPropertySymbols ? function(e2) {
      return Object.getOwnPropertyNames(e2).concat(Object.getOwnPropertySymbols(e2));
    } : function(e2) {
      return Object.getOwnPropertyNames(e2);
    };
    _ = Number.isNaN || function(e2) {
      return e2 != e2;
    };
    m.exports = w, m.exports.once = function(e2, t2) {
      return new Promise(function(n2, r2) {
        function i2(n3) {
          e2.removeListener(t2, o2), r2(n3);
        }
        function o2() {
          "function" == typeof e2.removeListener && e2.removeListener("error", i2), n2([].slice.call(arguments));
        }
        j(e2, t2, o2, { once: true }), "error" !== t2 && function(e3, t3, n3) {
          "function" == typeof e3.on && j(e3, "error", t3, n3);
        }(e2, i2, { once: true });
      });
    }, w.EventEmitter = w, w.prototype._events = void 0, w.prototype._eventsCount = 0, w.prototype._maxListeners = void 0;
    S = 10;
    Object.defineProperty(w, "defaultMaxListeners", { enumerable: true, get: function() {
      return S;
    }, set: function(e2) {
      if ("number" != typeof e2 || e2 < 0 || _(e2))
        throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + e2 + ".");
      S = e2;
    } }), w.init = function() {
      void 0 !== this._events && this._events !== Object.getPrototypeOf(this)._events || (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0), this._maxListeners = this._maxListeners || void 0;
    }, w.prototype.setMaxListeners = function(e2) {
      if ("number" != typeof e2 || e2 < 0 || _(e2))
        throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + e2 + ".");
      return this._maxListeners = e2, this;
    }, w.prototype.getMaxListeners = function() {
      return M(this);
    }, w.prototype.emit = function(e2) {
      for (var t2 = [], n2 = 1; n2 < arguments.length; n2++)
        t2.push(arguments[n2]);
      var r2 = "error" === e2, i2 = this._events;
      if (void 0 !== i2)
        r2 = r2 && void 0 === i2.error;
      else if (!r2)
        return false;
      if (r2) {
        var o2;
        if (t2.length > 0 && (o2 = t2[0]), o2 instanceof Error)
          throw o2;
        var s2 = new Error("Unhandled error." + (o2 ? " (" + o2.message + ")" : ""));
        throw s2.context = o2, s2;
      }
      var a2 = i2[e2];
      if (void 0 === a2)
        return false;
      if ("function" == typeof a2)
        b(a2, this, t2);
      else {
        var c2 = a2.length, l2 = A(a2, c2);
        for (n2 = 0; n2 < c2; ++n2)
          b(l2[n2], this, t2);
      }
      return true;
    }, w.prototype.addListener = function(e2, t2) {
      return C(this, e2, t2, false);
    }, w.prototype.on = w.prototype.addListener, w.prototype.prependListener = function(e2, t2) {
      return C(this, e2, t2, true);
    }, w.prototype.once = function(e2, t2) {
      return k(t2), this.on(e2, T(this, e2, t2)), this;
    }, w.prototype.prependOnceListener = function(e2, t2) {
      return k(t2), this.prependListener(e2, T(this, e2, t2)), this;
    }, w.prototype.removeListener = function(e2, t2) {
      var n2, r2, i2, o2, s2;
      if (k(t2), void 0 === (r2 = this._events))
        return this;
      if (void 0 === (n2 = r2[e2]))
        return this;
      if (n2 === t2 || n2.listener === t2)
        0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : (delete r2[e2], r2.removeListener && this.emit("removeListener", e2, n2.listener || t2));
      else if ("function" != typeof n2) {
        for (i2 = -1, o2 = n2.length - 1; o2 >= 0; o2--)
          if (n2[o2] === t2 || n2[o2].listener === t2) {
            s2 = n2[o2].listener, i2 = o2;
            break;
          }
        if (i2 < 0)
          return this;
        0 === i2 ? n2.shift() : function(e3, t3) {
          for (; t3 + 1 < e3.length; t3++)
            e3[t3] = e3[t3 + 1];
          e3.pop();
        }(n2, i2), 1 === n2.length && (r2[e2] = n2[0]), void 0 !== r2.removeListener && this.emit("removeListener", e2, s2 || t2);
      }
      return this;
    }, w.prototype.off = w.prototype.removeListener, w.prototype.removeAllListeners = function(e2) {
      var t2, n2, r2;
      if (void 0 === (n2 = this._events))
        return this;
      if (void 0 === n2.removeListener)
        return 0 === arguments.length ? (this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0) : void 0 !== n2[e2] && (0 == --this._eventsCount ? this._events = /* @__PURE__ */ Object.create(null) : delete n2[e2]), this;
      if (0 === arguments.length) {
        var i2, o2 = Object.keys(n2);
        for (r2 = 0; r2 < o2.length; ++r2)
          "removeListener" !== (i2 = o2[r2]) && this.removeAllListeners(i2);
        return this.removeAllListeners("removeListener"), this._events = /* @__PURE__ */ Object.create(null), this._eventsCount = 0, this;
      }
      if ("function" == typeof (t2 = n2[e2]))
        this.removeListener(e2, t2);
      else if (void 0 !== t2)
        for (r2 = t2.length - 1; r2 >= 0; r2--)
          this.removeListener(e2, t2[r2]);
      return this;
    }, w.prototype.listeners = function(e2) {
      return O(this, e2, true);
    }, w.prototype.rawListeners = function(e2) {
      return O(this, e2, false);
    }, w.listenerCount = function(e2, t2) {
      return "function" == typeof e2.listenerCount ? e2.listenerCount(t2) : P.call(e2, t2);
    }, w.prototype.listenerCount = P, w.prototype.eventNames = function() {
      return this._eventsCount > 0 ? g(this._events) : [];
    };
    I = m.exports;
    x = v(I);
    L = Object.prototype.hasOwnProperty;
    R = { "Amazon Silk": "amazon_silk", "Android Browser": "android", Bada: "bada", BlackBerry: "blackberry", Chrome: "chrome", Chromium: "chromium", Electron: "electron", Epiphany: "epiphany", Firefox: "firefox", Focus: "focus", Generic: "generic", "Google Search": "google_search", Googlebot: "googlebot", "Internet Explorer": "ie", "K-Meleon": "k_meleon", Maxthon: "maxthon", "Microsoft Edge": "edge", "MZ Browser": "mz", "NAVER Whale Browser": "naver", Opera: "opera", "Opera Coast": "opera_coast", PhantomJS: "phantomjs", Puffin: "puffin", QupZilla: "qupzilla", QQ: "qq", QQLite: "qqlite", Safari: "safari", Sailfish: "sailfish", "Samsung Internet for Android": "samsung_internet", SeaMonkey: "seamonkey", Sleipnir: "sleipnir", Swing: "swing", Tizen: "tizen", "UC Browser": "uc", Vivaldi: "vivaldi", "WebOS Browser": "webos", WeChat: "wechat", "Yandex Browser": "yandex", Roku: "roku" };
    F = { amazon_silk: "Amazon Silk", android: "Android Browser", bada: "Bada", blackberry: "BlackBerry", chrome: "Chrome", chromium: "Chromium", electron: "Electron", epiphany: "Epiphany", firefox: "Firefox", focus: "Focus", generic: "Generic", googlebot: "Googlebot", google_search: "Google Search", ie: "Internet Explorer", k_meleon: "K-Meleon", maxthon: "Maxthon", edge: "Microsoft Edge", mz: "MZ Browser", naver: "NAVER Whale Browser", opera: "Opera", opera_coast: "Opera Coast", phantomjs: "PhantomJS", puffin: "Puffin", qupzilla: "QupZilla", qq: "QQ Browser", qqlite: "QQ Browser Lite", safari: "Safari", sailfish: "Sailfish", samsung_internet: "Samsung Internet for Android", seamonkey: "SeaMonkey", sleipnir: "Sleipnir", swing: "Swing", tizen: "Tizen", uc: "UC Browser", vivaldi: "Vivaldi", webos: "WebOS Browser", wechat: "WeChat", yandex: "Yandex Browser" };
    B = { tablet: "tablet", mobile: "mobile", desktop: "desktop", tv: "tv" };
    U = { WindowsPhone: "Windows Phone", Windows: "Windows", MacOS: "macOS", iOS: "iOS", Android: "Android", WebOS: "WebOS", BlackBerry: "BlackBerry", Bada: "Bada", Tizen: "Tizen", Linux: "Linux", ChromeOS: "Chrome OS", PlayStation4: "PlayStation 4", Roku: "Roku" };
    V = { EdgeHTML: "EdgeHTML", Blink: "Blink", Trident: "Trident", Presto: "Presto", Gecko: "Gecko", WebKit: "WebKit" };
    J = class _J {
      static getFirstMatch(e2, t2) {
        const n2 = t2.match(e2);
        return n2 && n2.length > 0 && n2[1] || "";
      }
      static getSecondMatch(e2, t2) {
        const n2 = t2.match(e2);
        return n2 && n2.length > 1 && n2[2] || "";
      }
      static matchAndReturnConst(e2, t2, n2) {
        if (e2.test(t2))
          return n2;
      }
      static getWindowsVersionName(e2) {
        switch (e2) {
          case "NT":
            return "NT";
          case "XP":
          case "NT 5.1":
            return "XP";
          case "NT 5.0":
            return "2000";
          case "NT 5.2":
            return "2003";
          case "NT 6.0":
            return "Vista";
          case "NT 6.1":
            return "7";
          case "NT 6.2":
            return "8";
          case "NT 6.3":
            return "8.1";
          case "NT 10.0":
            return "10";
          default:
            return;
        }
      }
      static getMacOSVersionName(e2) {
        const t2 = e2.split(".").splice(0, 2).map((e3) => parseInt(e3, 10) || 0);
        if (t2.push(0), 10 === t2[0])
          switch (t2[1]) {
            case 5:
              return "Leopard";
            case 6:
              return "Snow Leopard";
            case 7:
              return "Lion";
            case 8:
              return "Mountain Lion";
            case 9:
              return "Mavericks";
            case 10:
              return "Yosemite";
            case 11:
              return "El Capitan";
            case 12:
              return "Sierra";
            case 13:
              return "High Sierra";
            case 14:
              return "Mojave";
            case 15:
              return "Catalina";
            default:
              return;
          }
      }
      static getAndroidVersionName(e2) {
        const t2 = e2.split(".").splice(0, 2).map((e3) => parseInt(e3, 10) || 0);
        if (t2.push(0), !(1 === t2[0] && t2[1] < 5))
          return 1 === t2[0] && t2[1] < 6 ? "Cupcake" : 1 === t2[0] && t2[1] >= 6 ? "Donut" : 2 === t2[0] && t2[1] < 2 ? "Eclair" : 2 === t2[0] && 2 === t2[1] ? "Froyo" : 2 === t2[0] && t2[1] > 2 ? "Gingerbread" : 3 === t2[0] ? "Honeycomb" : 4 === t2[0] && t2[1] < 1 ? "Ice Cream Sandwich" : 4 === t2[0] && t2[1] < 4 ? "Jelly Bean" : 4 === t2[0] && t2[1] >= 4 ? "KitKat" : 5 === t2[0] ? "Lollipop" : 6 === t2[0] ? "Marshmallow" : 7 === t2[0] ? "Nougat" : 8 === t2[0] ? "Oreo" : 9 === t2[0] ? "Pie" : void 0;
      }
      static getVersionPrecision(e2) {
        return e2.split(".").length;
      }
      static compareVersions(e2, t2, n2 = false) {
        const r2 = _J.getVersionPrecision(e2), i2 = _J.getVersionPrecision(t2);
        let o2 = Math.max(r2, i2), s2 = 0;
        const a2 = _J.map([e2, t2], (e3) => {
          const t3 = o2 - _J.getVersionPrecision(e3), n3 = e3 + new Array(t3 + 1).join(".0");
          return _J.map(n3.split("."), (e4) => new Array(20 - e4.length).join("0") + e4).reverse();
        });
        for (n2 && (s2 = o2 - Math.min(r2, i2)), o2 -= 1; o2 >= s2; ) {
          if (a2[0][o2] > a2[1][o2])
            return 1;
          if (a2[0][o2] === a2[1][o2]) {
            if (o2 === s2)
              return 0;
            o2 -= 1;
          } else if (a2[0][o2] < a2[1][o2])
            return -1;
        }
      }
      static map(e2, t2) {
        const n2 = [];
        let r2;
        if (Array.prototype.map)
          return Array.prototype.map.call(e2, t2);
        for (r2 = 0; r2 < e2.length; r2 += 1)
          n2.push(t2(e2[r2]));
        return n2;
      }
      static find(e2, t2) {
        let n2, r2;
        if (Array.prototype.find)
          return Array.prototype.find.call(e2, t2);
        for (n2 = 0, r2 = e2.length; n2 < r2; n2 += 1) {
          const r3 = e2[n2];
          if (t2(r3, n2))
            return r3;
        }
      }
      static assign(e2, ...t2) {
        const n2 = e2;
        let r2, i2;
        if (Object.assign)
          return Object.assign(e2, ...t2);
        for (r2 = 0, i2 = t2.length; r2 < i2; r2 += 1) {
          const e3 = t2[r2];
          if ("object" == typeof e3 && null !== e3) {
            Object.keys(e3).forEach((t3) => {
              n2[t3] = e3[t3];
            });
          }
        }
        return e2;
      }
      static getBrowserAlias(e2) {
        return R[e2];
      }
      static getBrowserTypeByAlias(e2) {
        return F[e2] || "";
      }
    };
    $ = /version\/(\d+(\.?_?\d+)+)/i;
    q = [{ test: [/googlebot/i], describe(e2) {
      const t2 = { name: "Googlebot" }, n2 = J.getFirstMatch(/googlebot\/(\d+(\.\d+))/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/opera/i], describe(e2) {
      const t2 = { name: "Opera" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/opr\/|opios/i], describe(e2) {
      const t2 = { name: "Opera" }, n2 = J.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/SamsungBrowser/i], describe(e2) {
      const t2 = { name: "Samsung Internet for Android" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/Whale/i], describe(e2) {
      const t2 = { name: "NAVER Whale Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/MZBrowser/i], describe(e2) {
      const t2 = { name: "MZ Browser" }, n2 = J.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/focus/i], describe(e2) {
      const t2 = { name: "Focus" }, n2 = J.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/swing/i], describe(e2) {
      const t2 = { name: "Swing" }, n2 = J.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/coast/i], describe(e2) {
      const t2 = { name: "Opera Coast" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/opt\/\d+(?:.?_?\d+)+/i], describe(e2) {
      const t2 = { name: "Opera Touch" }, n2 = J.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/yabrowser/i], describe(e2) {
      const t2 = { name: "Yandex Browser" }, n2 = J.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/ucbrowser/i], describe(e2) {
      const t2 = { name: "UC Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/Maxthon|mxios/i], describe(e2) {
      const t2 = { name: "Maxthon" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/epiphany/i], describe(e2) {
      const t2 = { name: "Epiphany" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/puffin/i], describe(e2) {
      const t2 = { name: "Puffin" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/sleipnir/i], describe(e2) {
      const t2 = { name: "Sleipnir" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/k-meleon/i], describe(e2) {
      const t2 = { name: "K-Meleon" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/micromessenger/i], describe(e2) {
      const t2 = { name: "WeChat" }, n2 = J.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/qqbrowser/i], describe(e2) {
      const t2 = { name: /qqbrowserlite/i.test(e2) ? "QQ Browser Lite" : "QQ Browser" }, n2 = J.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/msie|trident/i], describe(e2) {
      const t2 = { name: "Internet Explorer" }, n2 = J.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/\sedg\//i], describe(e2) {
      const t2 = { name: "Microsoft Edge" }, n2 = J.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/edg([ea]|ios)/i], describe(e2) {
      const t2 = { name: "Microsoft Edge" }, n2 = J.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/vivaldi/i], describe(e2) {
      const t2 = { name: "Vivaldi" }, n2 = J.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/seamonkey/i], describe(e2) {
      const t2 = { name: "SeaMonkey" }, n2 = J.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/sailfish/i], describe(e2) {
      const t2 = { name: "Sailfish" }, n2 = J.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/silk/i], describe(e2) {
      const t2 = { name: "Amazon Silk" }, n2 = J.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/phantom/i], describe(e2) {
      const t2 = { name: "PhantomJS" }, n2 = J.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/slimerjs/i], describe(e2) {
      const t2 = { name: "SlimerJS" }, n2 = J.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/blackberry|\bbb\d+/i, /rim\stablet/i], describe(e2) {
      const t2 = { name: "BlackBerry" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/(web|hpw)[o0]s/i], describe(e2) {
      const t2 = { name: "WebOS Browser" }, n2 = J.getFirstMatch($, e2) || J.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/bada/i], describe(e2) {
      const t2 = { name: "Bada" }, n2 = J.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/tizen/i], describe(e2) {
      const t2 = { name: "Tizen" }, n2 = J.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/qupzilla/i], describe(e2) {
      const t2 = { name: "QupZilla" }, n2 = J.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/firefox|iceweasel|fxios/i], describe(e2) {
      const t2 = { name: "Firefox" }, n2 = J.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/electron/i], describe(e2) {
      const t2 = { name: "Electron" }, n2 = J.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/MiuiBrowser/i], describe(e2) {
      const t2 = { name: "Miui" }, n2 = J.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/chromium/i], describe(e2) {
      const t2 = { name: "Chromium" }, n2 = J.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i, e2) || J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/chrome|crios|crmo/i], describe(e2) {
      const t2 = { name: "Chrome" }, n2 = J.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/GSA/i], describe(e2) {
      const t2 = { name: "Google Search" }, n2 = J.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test(e2) {
      const t2 = !e2.test(/like android/i), n2 = e2.test(/android/i);
      return t2 && n2;
    }, describe(e2) {
      const t2 = { name: "Android Browser" }, n2 = J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/playstation 4/i], describe(e2) {
      const t2 = { name: "PlayStation 4" }, n2 = J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/safari|applewebkit/i], describe(e2) {
      const t2 = { name: "Safari" }, n2 = J.getFirstMatch($, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/.*/i], describe(e2) {
      const t2 = -1 !== e2.search("\\(") ? /^(.*)\/(.*)[ \t]\((.*)/ : /^(.*)\/(.*) /;
      return { name: J.getFirstMatch(t2, e2), version: J.getSecondMatch(t2, e2) };
    } }];
    z = [{ test: [/Roku\/DVP/], describe(e2) {
      const t2 = J.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i, e2);
      return { name: U.Roku, version: t2 };
    } }, { test: [/windows phone/i], describe(e2) {
      const t2 = J.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i, e2);
      return { name: U.WindowsPhone, version: t2 };
    } }, { test: [/windows /i], describe(e2) {
      const t2 = J.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i, e2), n2 = J.getWindowsVersionName(t2);
      return { name: U.Windows, version: t2, versionName: n2 };
    } }, { test: [/Macintosh(.*?) FxiOS(.*?)\//], describe(e2) {
      const t2 = { name: U.iOS }, n2 = J.getSecondMatch(/(Version\/)(\d[\d.]+)/, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/macintosh/i], describe(e2) {
      const t2 = J.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i, e2).replace(/[_\s]/g, "."), n2 = J.getMacOSVersionName(t2), r2 = { name: U.MacOS, version: t2 };
      return n2 && (r2.versionName = n2), r2;
    } }, { test: [/(ipod|iphone|ipad)/i], describe(e2) {
      const t2 = J.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i, e2).replace(/[_\s]/g, ".");
      return { name: U.iOS, version: t2 };
    } }, { test(e2) {
      const t2 = !e2.test(/like android/i), n2 = e2.test(/android/i);
      return t2 && n2;
    }, describe(e2) {
      const t2 = J.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i, e2), n2 = J.getAndroidVersionName(t2), r2 = { name: U.Android, version: t2 };
      return n2 && (r2.versionName = n2), r2;
    } }, { test: [/(web|hpw)[o0]s/i], describe(e2) {
      const t2 = J.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i, e2), n2 = { name: U.WebOS };
      return t2 && t2.length && (n2.version = t2), n2;
    } }, { test: [/blackberry|\bbb\d+/i, /rim\stablet/i], describe(e2) {
      const t2 = J.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i, e2) || J.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i, e2) || J.getFirstMatch(/\bbb(\d+)/i, e2);
      return { name: U.BlackBerry, version: t2 };
    } }, { test: [/bada/i], describe(e2) {
      const t2 = J.getFirstMatch(/bada\/(\d+(\.\d+)*)/i, e2);
      return { name: U.Bada, version: t2 };
    } }, { test: [/tizen/i], describe(e2) {
      const t2 = J.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i, e2);
      return { name: U.Tizen, version: t2 };
    } }, { test: [/linux/i], describe: () => ({ name: U.Linux }) }, { test: [/CrOS/], describe: () => ({ name: U.ChromeOS }) }, { test: [/PlayStation 4/], describe(e2) {
      const t2 = J.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i, e2);
      return { name: U.PlayStation4, version: t2 };
    } }];
    W = [{ test: [/googlebot/i], describe: () => ({ type: "bot", vendor: "Google" }) }, { test: [/huawei/i], describe(e2) {
      const t2 = J.getFirstMatch(/(can-l01)/i, e2) && "Nova", n2 = { type: B.mobile, vendor: "Huawei" };
      return t2 && (n2.model = t2), n2;
    } }, { test: [/nexus\s*(?:7|8|9|10).*/i], describe: () => ({ type: B.tablet, vendor: "Nexus" }) }, { test: [/ipad/i], describe: () => ({ type: B.tablet, vendor: "Apple", model: "iPad" }) }, { test: [/Macintosh(.*?) FxiOS(.*?)\//], describe: () => ({ type: B.tablet, vendor: "Apple", model: "iPad" }) }, { test: [/kftt build/i], describe: () => ({ type: B.tablet, vendor: "Amazon", model: "Kindle Fire HD 7" }) }, { test: [/silk/i], describe: () => ({ type: B.tablet, vendor: "Amazon" }) }, { test: [/tablet(?! pc)/i], describe: () => ({ type: B.tablet }) }, { test(e2) {
      const t2 = e2.test(/ipod|iphone/i), n2 = e2.test(/like (ipod|iphone)/i);
      return t2 && !n2;
    }, describe(e2) {
      const t2 = J.getFirstMatch(/(ipod|iphone)/i, e2);
      return { type: B.mobile, vendor: "Apple", model: t2 };
    } }, { test: [/nexus\s*[0-6].*/i, /galaxy nexus/i], describe: () => ({ type: B.mobile, vendor: "Nexus" }) }, { test: [/[^-]mobi/i], describe: () => ({ type: B.mobile }) }, { test: (e2) => "blackberry" === e2.getBrowserName(true), describe: () => ({ type: B.mobile, vendor: "BlackBerry" }) }, { test: (e2) => "bada" === e2.getBrowserName(true), describe: () => ({ type: B.mobile }) }, { test: (e2) => "windows phone" === e2.getBrowserName(), describe: () => ({ type: B.mobile, vendor: "Microsoft" }) }, { test(e2) {
      const t2 = Number(String(e2.getOSVersion()).split(".")[0]);
      return "android" === e2.getOSName(true) && t2 >= 3;
    }, describe: () => ({ type: B.tablet }) }, { test: (e2) => "android" === e2.getOSName(true), describe: () => ({ type: B.mobile }) }, { test: (e2) => "macos" === e2.getOSName(true), describe: () => ({ type: B.desktop, vendor: "Apple" }) }, { test: (e2) => "windows" === e2.getOSName(true), describe: () => ({ type: B.desktop }) }, { test: (e2) => "linux" === e2.getOSName(true), describe: () => ({ type: B.desktop }) }, { test: (e2) => "playstation 4" === e2.getOSName(true), describe: () => ({ type: B.tv }) }, { test: (e2) => "roku" === e2.getOSName(true), describe: () => ({ type: B.tv }) }];
    H = [{ test: (e2) => "microsoft edge" === e2.getBrowserName(true), describe(e2) {
      if (/\sedg\//i.test(e2))
        return { name: V.Blink };
      const t2 = J.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i, e2);
      return { name: V.EdgeHTML, version: t2 };
    } }, { test: [/trident/i], describe(e2) {
      const t2 = { name: V.Trident }, n2 = J.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: (e2) => e2.test(/presto/i), describe(e2) {
      const t2 = { name: V.Presto }, n2 = J.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test(e2) {
      const t2 = e2.test(/gecko/i), n2 = e2.test(/like gecko/i);
      return t2 && !n2;
    }, describe(e2) {
      const t2 = { name: V.Gecko }, n2 = J.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }, { test: [/(apple)?webkit\/537\.36/i], describe: () => ({ name: V.Blink }) }, { test: [/(apple)?webkit/i], describe(e2) {
      const t2 = { name: V.WebKit }, n2 = J.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i, e2);
      return n2 && (t2.version = n2), t2;
    } }];
    G = class {
      constructor(e2, t2 = false) {
        if (null == e2 || "" === e2)
          throw new Error("UserAgent parameter can't be empty");
        this._ua = e2, this.parsedResult = {}, true !== t2 && this.parse();
      }
      getUA() {
        return this._ua;
      }
      test(e2) {
        return e2.test(this._ua);
      }
      parseBrowser() {
        this.parsedResult.browser = {};
        const e2 = J.find(q, (e3) => {
          if ("function" == typeof e3.test)
            return e3.test(this);
          if (e3.test instanceof Array)
            return e3.test.some((e4) => this.test(e4));
          throw new Error("Browser's test function is not valid");
        });
        return e2 && (this.parsedResult.browser = e2.describe(this.getUA())), this.parsedResult.browser;
      }
      getBrowser() {
        return this.parsedResult.browser ? this.parsedResult.browser : this.parseBrowser();
      }
      getBrowserName(e2) {
        return e2 ? String(this.getBrowser().name).toLowerCase() || "" : this.getBrowser().name || "";
      }
      getBrowserVersion() {
        return this.getBrowser().version;
      }
      getOS() {
        return this.parsedResult.os ? this.parsedResult.os : this.parseOS();
      }
      parseOS() {
        this.parsedResult.os = {};
        const e2 = J.find(z, (e3) => {
          if ("function" == typeof e3.test)
            return e3.test(this);
          if (e3.test instanceof Array)
            return e3.test.some((e4) => this.test(e4));
          throw new Error("Browser's test function is not valid");
        });
        return e2 && (this.parsedResult.os = e2.describe(this.getUA())), this.parsedResult.os;
      }
      getOSName(e2) {
        const { name: t2 } = this.getOS();
        return e2 ? String(t2).toLowerCase() || "" : t2 || "";
      }
      getOSVersion() {
        return this.getOS().version;
      }
      getPlatform() {
        return this.parsedResult.platform ? this.parsedResult.platform : this.parsePlatform();
      }
      getPlatformType(e2 = false) {
        const { type: t2 } = this.getPlatform();
        return e2 ? String(t2).toLowerCase() || "" : t2 || "";
      }
      parsePlatform() {
        this.parsedResult.platform = {};
        const e2 = J.find(W, (e3) => {
          if ("function" == typeof e3.test)
            return e3.test(this);
          if (e3.test instanceof Array)
            return e3.test.some((e4) => this.test(e4));
          throw new Error("Browser's test function is not valid");
        });
        return e2 && (this.parsedResult.platform = e2.describe(this.getUA())), this.parsedResult.platform;
      }
      getEngine() {
        return this.parsedResult.engine ? this.parsedResult.engine : this.parseEngine();
      }
      getEngineName(e2) {
        return e2 ? String(this.getEngine().name).toLowerCase() || "" : this.getEngine().name || "";
      }
      parseEngine() {
        this.parsedResult.engine = {};
        const e2 = J.find(H, (e3) => {
          if ("function" == typeof e3.test)
            return e3.test(this);
          if (e3.test instanceof Array)
            return e3.test.some((e4) => this.test(e4));
          throw new Error("Browser's test function is not valid");
        });
        return e2 && (this.parsedResult.engine = e2.describe(this.getUA())), this.parsedResult.engine;
      }
      parse() {
        return this.parseBrowser(), this.parseOS(), this.parsePlatform(), this.parseEngine(), this;
      }
      getResult() {
        return J.assign({}, this.parsedResult);
      }
      satisfies(e2) {
        const t2 = {};
        let n2 = 0;
        const r2 = {};
        let i2 = 0;
        if (Object.keys(e2).forEach((o2) => {
          const s2 = e2[o2];
          "string" == typeof s2 ? (r2[o2] = s2, i2 += 1) : "object" == typeof s2 && (t2[o2] = s2, n2 += 1);
        }), n2 > 0) {
          const e3 = Object.keys(t2), n3 = J.find(e3, (e4) => this.isOS(e4));
          if (n3) {
            const e4 = this.satisfies(t2[n3]);
            if (void 0 !== e4)
              return e4;
          }
          const r3 = J.find(e3, (e4) => this.isPlatform(e4));
          if (r3) {
            const e4 = this.satisfies(t2[r3]);
            if (void 0 !== e4)
              return e4;
          }
        }
        if (i2 > 0) {
          const e3 = Object.keys(r2), t3 = J.find(e3, (e4) => this.isBrowser(e4, true));
          if (void 0 !== t3)
            return this.compareVersion(r2[t3]);
        }
      }
      isBrowser(e2, t2 = false) {
        const n2 = this.getBrowserName().toLowerCase();
        let r2 = e2.toLowerCase();
        const i2 = J.getBrowserTypeByAlias(r2);
        return t2 && i2 && (r2 = i2.toLowerCase()), r2 === n2;
      }
      compareVersion(e2) {
        let t2 = [0], n2 = e2, r2 = false;
        const i2 = this.getBrowserVersion();
        if ("string" == typeof i2)
          return ">" === e2[0] || "<" === e2[0] ? (n2 = e2.substr(1), "=" === e2[1] ? (r2 = true, n2 = e2.substr(2)) : t2 = [], ">" === e2[0] ? t2.push(1) : t2.push(-1)) : "=" === e2[0] ? n2 = e2.substr(1) : "~" === e2[0] && (r2 = true, n2 = e2.substr(1)), t2.indexOf(J.compareVersions(i2, n2, r2)) > -1;
      }
      isOS(e2) {
        return this.getOSName(true) === String(e2).toLowerCase();
      }
      isPlatform(e2) {
        return this.getPlatformType(true) === String(e2).toLowerCase();
      }
      isEngine(e2) {
        return this.getEngineName(true) === String(e2).toLowerCase();
      }
      is(e2, t2 = false) {
        return this.isBrowser(e2, t2) || this.isOS(e2) || this.isPlatform(e2);
      }
      some(e2 = []) {
        return e2.some((e3) => this.is(e3));
      }
    };
    Q = class {
      static getParser(e2, t2 = false) {
        if ("string" != typeof e2)
          throw new Error("UserAgent should be a string");
        return new G(e2, t2);
      }
      static parse(e2) {
        return new G(e2).getResult();
      }
      static get BROWSER_MAP() {
        return F;
      }
      static get ENGINE_MAP() {
        return V;
      }
      static get OS_MAP() {
        return U;
      }
      static get PLATFORMS_MAP() {
        return B;
      }
    };
    te = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
    ne = "8.55.0";
    re = globalThis;
    oe = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
    se = ["debug", "info", "warn", "error", "log", "assert", "trace"];
    ae = {};
    le = ie("logger", function() {
      let e2 = false;
      const t2 = { enable: () => {
        e2 = true;
      }, disable: () => {
        e2 = false;
      }, isEnabled: () => e2 };
      return oe ? se.forEach((n2) => {
        t2[n2] = (...t3) => {
          e2 && ce(() => {
            re.console[n2](`Sentry Logger [${n2}]:`, ...t3);
          });
        };
      }) : se.forEach((e3) => {
        t2[e3] = () => {
        };
      }), t2;
    });
    ue = "?";
    de = /\(error: (.*)\)/;
    he = /captureMessage|captureException/;
    fe = "<anonymous>";
    me = {};
    ye = {};
    Se = null;
    Me = null;
    Oe = Object.prototype.toString;
    Ve = re;
    it = function() {
      const { performance: e2 } = re;
      if (!e2 || !e2.now)
        return rt;
      const t2 = Date.now() - e2.now(), n2 = null == e2.timeOrigin ? t2 : e2.timeOrigin;
      return () => (n2 + e2.now()) / 1e3;
    }();
    (() => {
      const { performance: e2 } = re;
      if (!e2 || !e2.now)
        return;
      const t2 = 36e5, n2 = e2.now(), r2 = Date.now(), i2 = e2.timeOrigin ? Math.abs(e2.timeOrigin + n2 - r2) : t2, o2 = i2 < t2, s2 = e2.timing && e2.timing.navigationStart, a2 = "number" == typeof s2 ? Math.abs(s2 + n2 - r2) : t2;
      (o2 || a2 < t2) && (i2 <= a2 && e2.timeOrigin);
    })(), function(e2) {
      e2[e2.PENDING = 0] = "PENDING";
      e2[e2.RESOLVED = 1] = "RESOLVED";
      e2[e2.REJECTED = 2] = "REJECTED";
    }(dt || (dt = {}));
    ft = class _ft {
      constructor(e2) {
        _ft.prototype.__init.call(this), _ft.prototype.__init2.call(this), _ft.prototype.__init3.call(this), _ft.prototype.__init4.call(this), this._state = dt.PENDING, this._handlers = [];
        try {
          e2(this._resolve, this._reject);
        } catch (e3) {
          this._reject(e3);
        }
      }
      then(e2, t2) {
        return new _ft((n2, r2) => {
          this._handlers.push([false, (t3) => {
            if (e2)
              try {
                n2(e2(t3));
              } catch (e3) {
                r2(e3);
              }
            else
              n2(t3);
          }, (e3) => {
            if (t2)
              try {
                n2(t2(e3));
              } catch (e4) {
                r2(e4);
              }
            else
              r2(e3);
          }]), this._executeHandlers();
        });
      }
      catch(e2) {
        return this.then((e3) => e3, e2);
      }
      finally(e2) {
        return new _ft((t2, n2) => {
          let r2, i2;
          return this.then((t3) => {
            i2 = false, r2 = t3, e2 && e2();
          }, (t3) => {
            i2 = true, r2 = t3, e2 && e2();
          }).then(() => {
            i2 ? n2(r2) : t2(r2);
          });
        });
      }
      __init() {
        this._resolve = (e2) => {
          this._setResult(dt.RESOLVED, e2);
        };
      }
      __init2() {
        this._reject = (e2) => {
          this._setResult(dt.REJECTED, e2);
        };
      }
      __init3() {
        this._setResult = (e2, t2) => {
          this._state === dt.PENDING && (Fe(t2) ? t2.then(this._resolve, this._reject) : (this._state = e2, this._value = t2, this._executeHandlers()));
        };
      }
      __init4() {
        this._executeHandlers = () => {
          if (this._state === dt.PENDING)
            return;
          const e2 = this._handlers.slice();
          this._handlers = [], e2.forEach((e3) => {
            e3[0] || (this._state === dt.RESOLVED && e3[1](this._value), this._state === dt.REJECTED && e3[2](this._value), e3[0] = true);
          });
        };
      }
    };
    _t = "_sentrySpan";
    kt = class _kt {
      constructor() {
        this._notifyingListeners = false, this._scopeListeners = [], this._eventProcessors = [], this._breadcrumbs = [], this._attachments = [], this._user = {}, this._tags = {}, this._extra = {}, this._contexts = {}, this._sdkProcessingMetadata = {}, this._propagationContext = { traceId: mt(), spanId: yt() };
      }
      clone() {
        const e2 = new _kt();
        return e2._breadcrumbs = [...this._breadcrumbs], e2._tags = { ...this._tags }, e2._extra = { ...this._extra }, e2._contexts = { ...this._contexts }, this._contexts.flags && (e2._contexts.flags = { values: [...this._contexts.flags.values] }), e2._user = this._user, e2._level = this._level, e2._session = this._session, e2._transactionName = this._transactionName, e2._fingerprint = this._fingerprint, e2._eventProcessors = [...this._eventProcessors], e2._requestSession = this._requestSession, e2._attachments = [...this._attachments], e2._sdkProcessingMetadata = { ...this._sdkProcessingMetadata }, e2._propagationContext = { ...this._propagationContext }, e2._client = this._client, e2._lastEventId = this._lastEventId, wt(e2, St(this)), e2;
      }
      setClient(e2) {
        this._client = e2;
      }
      setLastEventId(e2) {
        this._lastEventId = e2;
      }
      getClient() {
        return this._client;
      }
      lastEventId() {
        return this._lastEventId;
      }
      addScopeListener(e2) {
        this._scopeListeners.push(e2);
      }
      addEventProcessor(e2) {
        return this._eventProcessors.push(e2), this;
      }
      setUser(e2) {
        return this._user = e2 || { email: void 0, id: void 0, ip_address: void 0, username: void 0 }, this._session && gt(this._session, { user: e2 }), this._notifyScopeListeners(), this;
      }
      getUser() {
        return this._user;
      }
      getRequestSession() {
        return this._requestSession;
      }
      setRequestSession(e2) {
        return this._requestSession = e2, this;
      }
      setTags(e2) {
        return this._tags = { ...this._tags, ...e2 }, this._notifyScopeListeners(), this;
      }
      setTag(e2, t2) {
        return this._tags = { ...this._tags, [e2]: t2 }, this._notifyScopeListeners(), this;
      }
      setExtras(e2) {
        return this._extra = { ...this._extra, ...e2 }, this._notifyScopeListeners(), this;
      }
      setExtra(e2, t2) {
        return this._extra = { ...this._extra, [e2]: t2 }, this._notifyScopeListeners(), this;
      }
      setFingerprint(e2) {
        return this._fingerprint = e2, this._notifyScopeListeners(), this;
      }
      setLevel(e2) {
        return this._level = e2, this._notifyScopeListeners(), this;
      }
      setTransactionName(e2) {
        return this._transactionName = e2, this._notifyScopeListeners(), this;
      }
      setContext(e2, t2) {
        return null === t2 ? delete this._contexts[e2] : this._contexts[e2] = t2, this._notifyScopeListeners(), this;
      }
      setSession(e2) {
        return e2 ? this._session = e2 : delete this._session, this._notifyScopeListeners(), this;
      }
      getSession() {
        return this._session;
      }
      update(e2) {
        if (!e2)
          return this;
        const t2 = "function" == typeof e2 ? e2(this) : e2, [n2, r2] = t2 instanceof Mt ? [t2.getScopeData(), t2.getRequestSession()] : Ne(t2) ? [e2, e2.requestSession] : [], { tags: i2, extra: o2, user: s2, contexts: a2, level: c2, fingerprint: l2 = [], propagationContext: u2 } = n2 || {};
        return this._tags = { ...this._tags, ...i2 }, this._extra = { ...this._extra, ...o2 }, this._contexts = { ...this._contexts, ...a2 }, s2 && Object.keys(s2).length && (this._user = s2), c2 && (this._level = c2), l2.length && (this._fingerprint = l2), u2 && (this._propagationContext = u2), r2 && (this._requestSession = r2), this;
      }
      clear() {
        return this._breadcrumbs = [], this._tags = {}, this._extra = {}, this._user = {}, this._contexts = {}, this._level = void 0, this._transactionName = void 0, this._fingerprint = void 0, this._requestSession = void 0, this._session = void 0, wt(this, void 0), this._attachments = [], this.setPropagationContext({ traceId: mt() }), this._notifyScopeListeners(), this;
      }
      addBreadcrumb(e2, t2) {
        const n2 = "number" == typeof t2 ? t2 : 100;
        if (n2 <= 0)
          return this;
        const r2 = { timestamp: rt(), ...e2 };
        return this._breadcrumbs.push(r2), this._breadcrumbs.length > n2 && (this._breadcrumbs = this._breadcrumbs.slice(-n2), this._client && this._client.recordDroppedEvent("buffer_overflow", "log_item")), this._notifyScopeListeners(), this;
      }
      getLastBreadcrumb() {
        return this._breadcrumbs[this._breadcrumbs.length - 1];
      }
      clearBreadcrumbs() {
        return this._breadcrumbs = [], this._notifyScopeListeners(), this;
      }
      addAttachment(e2) {
        return this._attachments.push(e2), this;
      }
      clearAttachments() {
        return this._attachments = [], this;
      }
      getScopeData() {
        return { breadcrumbs: this._breadcrumbs, attachments: this._attachments, contexts: this._contexts, tags: this._tags, extra: this._extra, user: this._user, level: this._level, fingerprint: this._fingerprint || [], eventProcessors: this._eventProcessors, propagationContext: this._propagationContext, sdkProcessingMetadata: this._sdkProcessingMetadata, transactionName: this._transactionName, span: St(this) };
      }
      setSDKProcessingMetadata(e2) {
        return this._sdkProcessingMetadata = bt(this._sdkProcessingMetadata, e2, 2), this;
      }
      setPropagationContext(e2) {
        return this._propagationContext = { spanId: yt(), ...e2 }, this;
      }
      getPropagationContext() {
        return this._propagationContext;
      }
      captureException(e2, t2) {
        const n2 = t2 && t2.event_id ? t2.event_id : ot();
        if (!this._client)
          return le.warn("No client configured on scope - will not capture exception!"), n2;
        const r2 = new Error("Sentry syntheticException");
        return this._client.captureException(e2, { originalException: e2, syntheticException: r2, ...t2, event_id: n2 }, this), n2;
      }
      captureMessage(e2, t2, n2) {
        const r2 = n2 && n2.event_id ? n2.event_id : ot();
        if (!this._client)
          return le.warn("No client configured on scope - will not capture message!"), r2;
        const i2 = new Error(e2);
        return this._client.captureMessage(e2, t2, { originalException: e2, syntheticException: i2, ...n2, event_id: r2 }, this), r2;
      }
      captureEvent(e2, t2) {
        const n2 = t2 && t2.event_id ? t2.event_id : ot();
        return this._client ? (this._client.captureEvent(e2, { ...t2, event_id: n2 }, this), n2) : (le.warn("No client configured on scope - will not capture event!"), n2);
      }
      _notifyScopeListeners() {
        this._notifyingListeners || (this._notifyingListeners = true, this._scopeListeners.forEach((e2) => {
          e2(this);
        }), this._notifyingListeners = false);
      }
    };
    Mt = kt;
    Ct = class {
      constructor(e2, t2) {
        let n2, r2;
        n2 = e2 || new Mt(), r2 = t2 || new Mt(), this._stack = [{ scope: n2 }], this._isolationScope = r2;
      }
      withScope(e2) {
        const t2 = this._pushScope();
        let n2;
        try {
          n2 = e2(t2);
        } catch (e3) {
          throw this._popScope(), e3;
        }
        return Fe(n2) ? n2.then((e3) => (this._popScope(), e3), (e3) => {
          throw this._popScope(), e3;
        }) : (this._popScope(), n2);
      }
      getClient() {
        return this.getStackTop().client;
      }
      getScope() {
        return this.getStackTop().scope;
      }
      getIsolationScope() {
        return this._isolationScope;
      }
      getStackTop() {
        return this._stack[this._stack.length - 1];
      }
      _pushScope() {
        const e2 = this.getScope().clone();
        return this._stack.push({ client: this.getClient(), scope: e2 }), e2;
      }
      _popScope() {
        return !(this._stack.length <= 1) && !!this._stack.pop();
      }
    };
    Nt = /^sentry-/;
    Bt = false;
    Ht = "production";
    Kt = /^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;
    un = { session: "session", sessions: "session", attachment: "attachment", transaction: "transaction", event: "error", client_report: "internal", user_report: "default", profile: "profile", profile_chunk: "profile", replay_event: "replay", replay_recording: "replay", check_in: "monitor", feedback: "feedback", span: "span", statsd: "metric_bucket", raw_security: "security" };
    kn = ["user", "level", "extra", "contexts", "tags", "fingerprint", "requestSession", "propagationContext"];
    An = [];
    xn = class extends Error {
      constructor(e2, t2 = "warn") {
        super(e2), this.message = e2, this.logLevel = t2;
      }
    };
    Ln = "Not capturing exception because it's already been captured.";
    Dn = class {
      constructor(e2) {
        if (this._options = e2, this._integrations = {}, this._numProcessing = 0, this._outcomes = {}, this._hooks = {}, this._eventProcessors = [], e2.dsn ? this._dsn = Zt(e2.dsn) : te && le.warn("No DSN provided, client will not send events."), this._dsn) {
          const t3 = Pn(this._dsn, e2.tunnel, e2._metadata ? e2._metadata.sdk : void 0);
          this._transport = e2.transport({ tunnel: this._options.tunnel, recordDroppedEvent: this.recordDroppedEvent.bind(this), ...e2.transportOptions, url: t3 });
        }
        const t2 = ["enableTracing", "tracesSampleRate", "tracesSampler"].find((t3) => t3 in e2 && null == e2[t3]);
        t2 && ce(() => {
          console.warn(`[Sentry] Deprecation warning: \`${t2}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`);
        });
      }
      captureException(e2, t2, n2) {
        const r2 = ot();
        if (ut(e2))
          return te && le.log(Ln), r2;
        const i2 = { event_id: r2, ...t2 };
        return this._process(this.eventFromException(e2, i2).then((e3) => this._captureEvent(e3, i2, n2))), i2.event_id;
      }
      captureMessage(e2, t2, n2, r2) {
        const i2 = { event_id: ot(), ...n2 }, o2 = Le(e2) ? e2 : String(e2), s2 = De(e2) ? this.eventFromMessage(o2, t2, i2) : this.eventFromException(e2, i2);
        return this._process(s2.then((e3) => this._captureEvent(e3, i2, r2))), i2.event_id;
      }
      captureEvent(e2, t2, n2) {
        const r2 = ot();
        if (t2 && t2.originalException && ut(t2.originalException))
          return te && le.log(Ln), r2;
        const i2 = { event_id: r2, ...t2 }, o2 = (e2.sdkProcessingMetadata || {}).capturedSpanScope;
        return this._process(this._captureEvent(e2, i2, o2 || n2)), i2.event_id;
      }
      captureSession(e2) {
        "string" != typeof e2.release ? te && le.warn("Discarded session because of missing or non-string release") : (this.sendSession(e2), gt(e2, { init: false }));
      }
      getDsn() {
        return this._dsn;
      }
      getOptions() {
        return this._options;
      }
      getSdkMetadata() {
        return this._options._metadata;
      }
      getTransport() {
        return this._transport;
      }
      flush(e2) {
        const t2 = this._transport;
        return t2 ? (this.emit("flush"), this._isClientDoneProcessing(e2).then((n2) => t2.flush(e2).then((e3) => n2 && e3))) : ht(true);
      }
      close(e2) {
        return this.flush(e2).then((e3) => (this.getOptions().enabled = false, this.emit("close"), e3));
      }
      getEventProcessors() {
        return this._eventProcessors;
      }
      addEventProcessor(e2) {
        this._eventProcessors.push(e2);
      }
      init() {
        (this._isEnabled() || this._options.integrations.some(({ name: e2 }) => e2.startsWith("Spotlight"))) && this._setupIntegrations();
      }
      getIntegrationByName(e2) {
        return this._integrations[e2];
      }
      addIntegration(e2) {
        const t2 = this._integrations[e2.name];
        In(this, e2, this._integrations), t2 || jn(this, [e2]);
      }
      sendEvent(e2, t2 = {}) {
        this.emit("beforeSendEvent", e2, t2);
        let n2 = pn(e2, this._dsn, this._options._metadata, this._options.tunnel);
        for (const e3 of t2.attachments || [])
          n2 = on(n2, ln(e3));
        const r2 = this.sendEnvelope(n2);
        r2 && r2.then((t3) => this.emit("afterSendEvent", e2, t3), null);
      }
      sendSession(e2) {
        const t2 = function(e3, t3, n2, r2) {
          const i2 = hn(n2);
          return rn({ sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...i2 && { sdk: i2 }, ...!!r2 && t3 && { dsn: Yt(t3) } }, ["aggregates" in e3 ? [{ type: "sessions" }, e3] : [{ type: "session" }, e3.toJSON()]]);
        }(e2, this._dsn, this._options._metadata, this._options.tunnel);
        this.sendEnvelope(t2);
      }
      recordDroppedEvent(e2, t2, n2) {
        if (this._options.sendClientReports) {
          const r2 = "number" == typeof n2 ? n2 : 1, i2 = `${e2}:${t2}`;
          te && le.log(`Recording outcome: "${i2}"${r2 > 1 ? ` (${r2} times)` : ""}`), this._outcomes[i2] = (this._outcomes[i2] || 0) + r2;
        }
      }
      on(e2, t2) {
        const n2 = this._hooks[e2] = this._hooks[e2] || [];
        return n2.push(t2), () => {
          const e3 = n2.indexOf(t2);
          e3 > -1 && n2.splice(e3, 1);
        };
      }
      emit(e2, ...t2) {
        const n2 = this._hooks[e2];
        n2 && n2.forEach((e3) => e3(...t2));
      }
      sendEnvelope(e2) {
        return this.emit("beforeEnvelope", e2), this._isEnabled() && this._transport ? this._transport.send(e2).then(null, (e3) => (te && le.error("Error while sending envelope:", e3), e3)) : (te && le.error("Transport disabled"), ht({}));
      }
      _setupIntegrations() {
        const { integrations: e2 } = this._options;
        this._integrations = function(e3, t2) {
          const n2 = {};
          return t2.forEach((t3) => {
            t3 && In(e3, t3, n2);
          }), n2;
        }(this, e2), jn(this, e2);
      }
      _updateSessionFromEvent(e2, t2) {
        let n2 = "fatal" === t2.level, r2 = false;
        const i2 = t2.exception && t2.exception.values;
        if (i2) {
          r2 = true;
          for (const e3 of i2) {
            const t3 = e3.mechanism;
            if (t3 && false === t3.handled) {
              n2 = true;
              break;
            }
          }
        }
        const o2 = "ok" === e2.status;
        (o2 && 0 === e2.errors || o2 && n2) && (gt(e2, { ...n2 && { status: "crashed" }, errors: e2.errors || Number(r2 || n2) }), this.captureSession(e2));
      }
      _isClientDoneProcessing(e2) {
        return new ft((t2) => {
          let n2 = 0;
          const r2 = setInterval(() => {
            0 == this._numProcessing ? (clearInterval(r2), t2(true)) : (n2 += 1, e2 && n2 >= e2 && (clearInterval(r2), t2(false)));
          }, 1);
        });
      }
      _isEnabled() {
        return false !== this.getOptions().enabled && void 0 !== this._transport;
      }
      _prepareEvent(e2, t2, n2 = jt(), r2 = It()) {
        const i2 = this.getOptions(), o2 = Object.keys(this._integrations);
        return !t2.integrations && o2.length > 0 && (t2.integrations = o2), this.emit("preprocessEvent", e2, t2), e2.type || r2.setLastEventId(e2.event_id || t2.event_id), wn(i2, e2, t2, n2, this, r2).then((e3) => {
          if (null === e3)
            return e3;
          e3.contexts = { trace: Lt(n2), ...e3.contexts };
          const t3 = function(e4, t4) {
            const n3 = t4.getPropagationContext();
            return n3.dsc || Gt(n3.traceId, e4);
          }(this, n2);
          return e3.sdkProcessingMetadata = { dynamicSamplingContext: t3, ...e3.sdkProcessingMetadata }, e3;
        });
      }
      _captureEvent(e2, t2 = {}, n2) {
        return this._processEvent(e2, t2, n2).then((e3) => e3.event_id, (e3) => {
          te && (e3 instanceof xn && "log" === e3.logLevel ? le.log(e3.message) : le.warn(e3));
        });
      }
      _processEvent(e2, t2, n2) {
        const r2 = this.getOptions(), { sampleRate: i2 } = r2, o2 = Rn(e2), s2 = Nn(e2), a2 = e2.type || "error", c2 = `before send for type \`${a2}\``, l2 = void 0 === i2 ? void 0 : function(e3) {
          if ("boolean" == typeof e3)
            return Number(e3);
          const t3 = "string" == typeof e3 ? parseFloat(e3) : e3;
          if (!("number" != typeof t3 || isNaN(t3) || t3 < 0 || t3 > 1))
            return t3;
          te && le.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e3)} of type ${JSON.stringify(typeof e3)}.`);
        }(i2);
        if (s2 && "number" == typeof l2 && Math.random() > l2)
          return this.recordDroppedEvent("sample_rate", "error", e2), pt(new xn(`Discarding event because it's not included in the random sample (sampling rate = ${i2})`, "log"));
        const u2 = "replay_event" === a2 ? "replay" : a2, d2 = (e2.sdkProcessingMetadata || {}).capturedSpanIsolationScope;
        return this._prepareEvent(e2, t2, n2, d2).then((n3) => {
          if (null === n3)
            throw this.recordDroppedEvent("event_processor", u2, e2), new xn("An event processor returned `null`, will not send event.", "log");
          if (t2.data && true === t2.data.__sentry__)
            return n3;
          const i3 = function(e3, t3, n4, r3) {
            const { beforeSend: i4, beforeSendTransaction: o3, beforeSendSpan: s3 } = t3;
            if (Nn(n4) && i4)
              return i4(n4, r3);
            if (Rn(n4)) {
              if (n4.spans && s3) {
                const t4 = [];
                for (const r4 of n4.spans) {
                  const n5 = s3(r4);
                  n5 ? t4.push(n5) : (Wt(), e3.recordDroppedEvent("before_send", "span"));
                }
                n4.spans = t4;
              }
              if (o3) {
                if (n4.spans) {
                  const e4 = n4.spans.length;
                  n4.sdkProcessingMetadata = { ...n4.sdkProcessingMetadata, spanCountBeforeProcessing: e4 };
                }
                return o3(n4, r3);
              }
            }
            return n4;
          }(this, r2, n3, t2);
          return function(e3, t3) {
            const n4 = `${t3} must return \`null\` or a valid event.`;
            if (Fe(e3))
              return e3.then((e4) => {
                if (!Ne(e4) && null !== e4)
                  throw new xn(n4);
                return e4;
              }, (e4) => {
                throw new xn(`${t3} rejected with ${e4}`);
              });
            if (!Ne(e3) && null !== e3)
              throw new xn(n4);
            return e3;
          }(i3, c2);
        }).then((r3) => {
          if (null === r3) {
            if (this.recordDroppedEvent("before_send", u2, e2), o2) {
              const t3 = 1 + (e2.spans || []).length;
              this.recordDroppedEvent("before_send", "span", t3);
            }
            throw new xn(`${c2} returned \`null\`, will not send event.`, "log");
          }
          const i3 = n2 && n2.getSession();
          if (!o2 && i3 && this._updateSessionFromEvent(i3, r3), o2) {
            const e3 = (r3.sdkProcessingMetadata && r3.sdkProcessingMetadata.spanCountBeforeProcessing || 0) - (r3.spans ? r3.spans.length : 0);
            e3 > 0 && this.recordDroppedEvent("before_send", "span", e3);
          }
          const s3 = r3.transaction_info;
          if (o2 && s3 && r3.transaction !== e2.transaction) {
            const e3 = "custom";
            r3.transaction_info = { ...s3, source: e3 };
          }
          return this.sendEvent(r3, t2), r3;
        }).then(null, (e3) => {
          if (e3 instanceof xn)
            throw e3;
          throw this.captureException(e3, { data: { __sentry__: true }, originalException: e3 }), new xn(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e3}`);
        });
      }
      _process(e2) {
        this._numProcessing++, e2.then((e3) => (this._numProcessing--, e3), (e3) => (this._numProcessing--, e3));
      }
      _clearOutcomes() {
        const e2 = this._outcomes;
        return this._outcomes = {}, Object.entries(e2).map(([e3, t2]) => {
          const [n2, r2] = e3.split(":");
          return { reason: n2, category: r2, quantity: t2 };
        });
      }
      _flushOutcomes() {
        te && le.log("Flushing outcomes...");
        const e2 = this._clearOutcomes();
        if (0 === e2.length)
          return void (te && le.log("No outcomes to send"));
        if (!this._dsn)
          return void (te && le.log("No dsn provided, will not send outcomes"));
        te && le.log("Sending outcomes:", e2);
        const t2 = (n2 = e2, rn((r2 = this._options.tunnel && Yt(this._dsn)) ? { dsn: r2 } : {}, [[{ type: "client_report" }, { timestamp: i2 || rt(), discarded_events: n2 }]]));
        var n2, r2, i2;
        this.sendEnvelope(t2);
      }
    };
    Jn = 100;
    zn = /* @__PURE__ */ new WeakMap();
    Wn = () => ({ name: "FunctionToString", setupOnce() {
      qn = Function.prototype.toString;
      try {
        Function.prototype.toString = function(...e2) {
          const t2 = Ye(this), n2 = zn.has(xt()) && void 0 !== t2 ? t2 : this;
          return qn.apply(n2, e2);
        };
      } catch (e2) {
      }
    }, setup(e2) {
      zn.set(e2, true);
    } });
    Hn = [/^Script error\.?$/, /^Javascript error: Script error\.? on line 0$/, /^ResizeObserver loop completed with undelivered notifications.$/, /^Cannot redefine property: googletag$/, "undefined is not an object (evaluating 'a.L')", `can't redefine non-configurable property "solana"`, "vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)", "Can't find variable: _AutofillCallbackHandler", /^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/];
    Gn = (e2 = {}) => ({ name: "InboundFilters", processEvent(t2, n2, r2) {
      const i2 = r2.getOptions(), o2 = function(e3 = {}, t3 = {}) {
        return { allowUrls: [...e3.allowUrls || [], ...t3.allowUrls || []], denyUrls: [...e3.denyUrls || [], ...t3.denyUrls || []], ignoreErrors: [...e3.ignoreErrors || [], ...t3.ignoreErrors || [], ...e3.disableErrorDefaults ? [] : Hn], ignoreTransactions: [...e3.ignoreTransactions || [], ...t3.ignoreTransactions || []], ignoreInternal: void 0 === e3.ignoreInternal || e3.ignoreInternal };
      }(e2, i2);
      return function(e3, t3) {
        if (t3.ignoreInternal && function(e4) {
          try {
            return "SentryError" === e4.exception.values[0].type;
          } catch (e5) {
          }
          return false;
        }(e3))
          return te && le.warn(`Event dropped due to being internal Sentry Error.
Event: ${at(e3)}`), true;
        if (function(e4, t4) {
          if (e4.type || !t4 || !t4.length)
            return false;
          return function(e5) {
            const t5 = [];
            e5.message && t5.push(e5.message);
            let n3;
            try {
              n3 = e5.exception.values[e5.exception.values.length - 1];
            } catch (e6) {
            }
            n3 && n3.value && (t5.push(n3.value), n3.type && t5.push(`${n3.type}: ${n3.value}`));
            return t5;
          }(e4).some((e5) => He(e5, t4));
        }(e3, t3.ignoreErrors))
          return te && le.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${at(e3)}`), true;
        if (function(e4) {
          if (e4.type)
            return false;
          if (!e4.exception || !e4.exception.values || 0 === e4.exception.values.length)
            return false;
          return !e4.message && !e4.exception.values.some((e5) => e5.stacktrace || e5.type && "Error" !== e5.type || e5.value);
        }(e3))
          return te && le.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${at(e3)}`), true;
        if (function(e4, t4) {
          if ("transaction" !== e4.type || !t4 || !t4.length)
            return false;
          const n3 = e4.transaction;
          return !!n3 && He(n3, t4);
        }(e3, t3.ignoreTransactions))
          return te && le.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${at(e3)}`), true;
        if (function(e4, t4) {
          if (!t4 || !t4.length)
            return false;
          const n3 = Qn(e4);
          return !!n3 && He(n3, t4);
        }(e3, t3.denyUrls))
          return te && le.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${at(e3)}.
Url: ${Qn(e3)}`), true;
        if (!function(e4, t4) {
          if (!t4 || !t4.length)
            return true;
          const n3 = Qn(e4);
          return !n3 || He(n3, t4);
        }(e3, t3.allowUrls))
          return te && le.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${at(e3)}.
Url: ${Qn(e3)}`), true;
        return false;
      }(t2, o2) ? null : t2;
    } });
    rr = () => {
      let e2;
      return { name: "Dedupe", processEvent(t2) {
        if (t2.type)
          return t2;
        try {
          if (function(e3, t3) {
            if (!t3)
              return false;
            if (function(e4, t4) {
              const n2 = e4.message, r2 = t4.message;
              if (!n2 && !r2)
                return false;
              if (n2 && !r2 || !n2 && r2)
                return false;
              if (n2 !== r2)
                return false;
              if (!or(e4, t4))
                return false;
              if (!ir(e4, t4))
                return false;
              return true;
            }(e3, t3))
              return true;
            if (function(e4, t4) {
              const n2 = sr(t4), r2 = sr(e4);
              if (!n2 || !r2)
                return false;
              if (n2.type !== r2.type || n2.value !== r2.value)
                return false;
              if (!or(e4, t4))
                return false;
              if (!ir(e4, t4))
                return false;
              return true;
            }(e3, t3))
              return true;
            return false;
          }(t2, e2))
            return te && le.warn("Event dropped due to being a duplicate of previously captured event."), null;
        } catch (e3) {
        }
        return e2 = t2;
      } };
    };
    cr = re;
    fr = re;
    vr = re;
    gr = 0;
    br = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
    Mr = /Minified React error #\d+;/i;
    jr = class extends Dn {
      constructor(e2) {
        const t2 = { parentSpanIsAlwaysRootSpan: true, ...e2 };
        !function(e3, t3, n2 = [t3], r2 = "npm") {
          const i2 = e3._metadata || {};
          i2.sdk || (i2.sdk = { name: `sentry.javascript.${t3}`, packages: n2.map((e4) => ({ name: `${r2}:@sentry/${e4}`, version: ne })), version: ne }), e3._metadata = i2;
        }(t2, "browser", ["browser"], vr.SENTRY_SDK_SOURCE || "npm"), super(t2), t2.sendClientReports && vr.document && vr.document.addEventListener("visibilitychange", () => {
          "hidden" === vr.document.visibilityState && this._flushOutcomes();
        });
      }
      eventFromException(e2, t2) {
        return function(e3, t3, n2, r2) {
          const i2 = Or(e3, t3, n2 && n2.syntheticException || void 0, r2);
          return lt(i2), i2.level = "error", n2 && n2.event_id && (i2.event_id = n2.event_id), ht(i2);
        }(this._options.stackParser, e2, t2, this._options.attachStacktrace);
      }
      eventFromMessage(e2, t2 = "info", n2) {
        return function(e3, t3, n3 = "info", r2, i2) {
          const o2 = Pr(e3, t3, r2 && r2.syntheticException || void 0, i2);
          return o2.level = n3, r2 && r2.event_id && (o2.event_id = r2.event_id), ht(o2);
        }(this._options.stackParser, e2, t2, n2, this._options.attachStacktrace);
      }
      captureUserFeedback(e2) {
        if (!this._isEnabled())
          return void (br && le.warn("SDK not enabled, will not capture user feedback."));
        const t2 = function(e3, { metadata: t3, tunnel: n2, dsn: r2 }) {
          const i2 = { event_id: e3.event_id, sent_at: (/* @__PURE__ */ new Date()).toISOString(), ...t3 && t3.sdk && { sdk: { name: t3.sdk.name, version: t3.sdk.version } }, ...!!n2 && !!r2 && { dsn: Yt(r2) } }, o2 = function(e4) {
            return [{ type: "user_report" }, e4];
          }(e3);
          return rn(i2, [o2]);
        }(e2, { metadata: this.getSdkMetadata(), dsn: this.getDsn(), tunnel: this.getOptions().tunnel });
        this.sendEnvelope(t2);
      }
      _prepareEvent(e2, t2, n2) {
        return e2.platform = e2.platform || "javascript", super._prepareEvent(e2, t2, n2);
      }
    };
    Ir = "undefined" == typeof __SENTRY_DEBUG__ || __SENTRY_DEBUG__;
    xr = re;
    Jr = {};
    qr = "__sentry_xhr_v3__";
    Gr = /^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i;
    Qr = /^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i;
    Kr = /\((\S*)(?::(\d+))(?::(\d+))\)/;
    Yr = /^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i;
    Xr = /(\S+) line (\d+)(?: > eval line \d+)* > eval/i;
    Zr = function(...e2) {
      const t2 = e2.sort((e3, t3) => e3[0] - t3[0]).map((e3) => e3[1]);
      return (e3, n2 = 0, r2 = 0) => {
        const i2 = [], o2 = e3.split("\n");
        for (let e4 = n2; e4 < o2.length; e4++) {
          const n3 = o2[e4];
          if (n3.length > 1024)
            continue;
          const s2 = de.test(n3) ? n3.replace(de, "$1") : n3;
          if (!s2.match(/\S*Error: /)) {
            for (const e5 of t2) {
              const t3 = e5(s2);
              if (t3) {
                i2.push(t3);
                break;
              }
            }
            if (i2.length >= 50 + r2)
              break;
          }
        }
        return function(e4) {
          if (!e4.length)
            return [];
          const t3 = Array.from(e4);
          /sentryWrapped/.test(pe(t3).function || "") && t3.pop();
          t3.reverse(), he.test(pe(t3).function || "") && (t3.pop(), he.test(pe(t3).function || "") && t3.pop());
          return t3.slice(0, 50).map((e5) => ({ ...e5, filename: e5.filename || pe(t3).filename, function: e5.function || ue }));
        }(i2.slice(r2));
      };
    }(...[[30, (e2) => {
      const t2 = Gr.exec(e2);
      if (t2) {
        const [, e3, n3, r2] = t2;
        return Hr(e3, ue, +n3, +r2);
      }
      const n2 = Qr.exec(e2);
      if (n2) {
        if (n2[2] && 0 === n2[2].indexOf("eval")) {
          const e4 = Kr.exec(n2[2]);
          e4 && (n2[2] = e4[1], n2[3] = e4[2], n2[4] = e4[3]);
        }
        const [e3, t3] = ei(n2[1] || ue, n2[2]);
        return Hr(t3, e3, n2[3] ? +n2[3] : void 0, n2[4] ? +n2[4] : void 0);
      }
    }], [50, (e2) => {
      const t2 = Yr.exec(e2);
      if (t2) {
        if (t2[3] && t2[3].indexOf(" > eval") > -1) {
          const e4 = Xr.exec(t2[3]);
          e4 && (t2[1] = t2[1] || "eval", t2[3] = e4[1], t2[4] = e4[2], t2[5] = "");
        }
        let e3 = t2[3], n2 = t2[1] || ue;
        return [n2, e3] = ei(n2, e3), Hr(e3, n2, t2[4] ? +t2[4] : void 0, t2[5] ? +t2[5] : void 0);
      }
    }]]);
    ei = (e2, t2) => {
      const n2 = -1 !== e2.indexOf("safari-extension"), r2 = -1 !== e2.indexOf("safari-web-extension");
      return n2 || r2 ? [-1 !== e2.indexOf("@") ? e2.split("@")[0] : ue, n2 ? `safari-extension:${t2}` : `safari-web-extension:${t2}`] : [e2, t2];
    };
    ti = 1024;
    ni = (e2 = {}) => {
      const t2 = { console: true, dom: true, fetch: true, history: true, sentry: true, xhr: true, ...e2 };
      return { name: "Breadcrumbs", setup(e3) {
        var n2;
        t2.console && function(e4) {
          const t3 = "console";
          be(t3, e4), _e(t3, tr);
        }(function(e4) {
          return function(t3) {
            if (xt() !== e4)
              return;
            const n3 = { category: "console", data: { arguments: t3.args, logger: "console" }, level: nr(t3.level), message: ze(t3.args, " ") };
            if ("assert" === t3.level) {
              if (false !== t3.args[0])
                return;
              n3.message = `Assertion failed: ${ze(t3.args.slice(1), " ") || "console.assert"}`, n3.data.arguments = t3.args.slice(1);
            }
            $n(n3, { input: t3.args, level: t3.level });
          };
        }(e3)), t2.dom && (n2 = function(e4, t3) {
          return function(n3) {
            if (xt() !== e4)
              return;
            let r2, i2, o2 = "object" == typeof t3 ? t3.serializeAttribute : void 0, s2 = "object" == typeof t3 && "number" == typeof t3.maxStringLength ? t3.maxStringLength : void 0;
            s2 && s2 > ti && (br && le.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${s2} was configured. Sentry will use 1024 instead.`), s2 = ti), "string" == typeof o2 && (o2 = [o2]);
            try {
              const e5 = n3.event, t4 = function(e6) {
                return !!e6 && !!e6.target;
              }(e5) ? e5.target : e5;
              r2 = Je(t4, { keyAttrs: o2, maxStringLength: s2 }), i2 = function(e6) {
                if (!Ve.HTMLElement)
                  return null;
                let t5 = e6;
                for (let e7 = 0; e7 < 5; e7++) {
                  if (!t5)
                    return null;
                  if (t5 instanceof HTMLElement) {
                    if (t5.dataset.sentryComponent)
                      return t5.dataset.sentryComponent;
                    if (t5.dataset.sentryElement)
                      return t5.dataset.sentryElement;
                  }
                  t5 = t5.parentNode;
                }
                return null;
              }(t4);
            } catch (e5) {
              r2 = "<unknown>";
            }
            if (0 === r2.length)
              return;
            const a2 = { category: `ui.${n3.name}`, message: r2 };
            i2 && (a2.data = { "ui.component_name": i2 }), $n(a2, { event: n3.event, name: n3.name, global: n3.global });
          };
        }(e3, t2.dom), be("dom", n2), _e("dom", Fr)), t2.xhr && function(e4) {
          be("xhr", e4), _e("xhr", zr);
        }(function(e4) {
          return function(t3) {
            if (xt() !== e4)
              return;
            const { startTimestamp: n3, endTimestamp: r2 } = t3, i2 = t3.xhr[qr];
            if (!n3 || !r2 || !i2)
              return;
            const { method: o2, url: s2, status_code: a2, body: c2 } = i2, l2 = { method: o2, url: s2, status_code: a2 }, u2 = { xhr: t3.xhr, input: c2, startTimestamp: n3, endTimestamp: r2 };
            $n({ category: "xhr", data: l2, type: "http", level: ar(a2) }, u2);
          };
        }(e3)), t2.fetch && dr(function(e4) {
          return function(t3) {
            if (xt() !== e4)
              return;
            const { startTimestamp: n3, endTimestamp: r2 } = t3;
            if (r2 && (!t3.fetchData.url.match(/sentry_key/) || "POST" !== t3.fetchData.method))
              if (t3.error) {
                $n({ category: "fetch", data: t3.fetchData, level: "error", type: "http" }, { data: t3.error, input: t3.args, startTimestamp: n3, endTimestamp: r2 });
              } else {
                const e5 = t3.response, i2 = { ...t3.fetchData, status_code: e5 && e5.status }, o2 = { input: t3.args, response: e5, startTimestamp: n3, endTimestamp: r2 };
                $n({ category: "fetch", data: i2, type: "http", level: ar(i2.status_code) }, o2);
              }
          };
        }(e3)), t2.history && Ur(function(e4) {
          return function(t3) {
            if (xt() !== e4)
              return;
            let n3 = t3.from, r2 = t3.to;
            const i2 = er(vr.location.href);
            let o2 = n3 ? er(n3) : void 0;
            const s2 = er(r2);
            o2 && o2.path || (o2 = i2), i2.protocol === s2.protocol && i2.host === s2.host && (r2 = s2.relative), i2.protocol === o2.protocol && i2.host === o2.host && (n3 = o2.relative), $n({ category: "navigation", data: { from: n3, to: r2 } });
          };
        }(e3)), t2.sentry && e3.on("beforeSendEvent", function(e4) {
          return function(t3) {
            xt() === e4 && $n({ category: "sentry." + ("transaction" === t3.type ? "transaction" : "event"), event_id: t3.event_id, level: t3.level, message: at(t3) }, { event: t3 });
          };
        }(e3));
      } };
    };
    ri = ["EventTarget", "Window", "Node", "ApplicationCache", "AudioTrackList", "BroadcastChannel", "ChannelMergerNode", "CryptoOperation", "EventSource", "FileReader", "HTMLUnknownElement", "IDBDatabase", "IDBRequest", "IDBTransaction", "KeyOperation", "MediaController", "MessagePort", "ModalWindow", "Notification", "SVGElementInstance", "Screen", "SharedWorker", "TextTrack", "TextTrackCue", "TextTrackList", "WebSocket", "WebSocketWorker", "Worker", "XMLHttpRequest", "XMLHttpRequestEventTarget", "XMLHttpRequestUpload"];
    ii = (e2 = {}) => {
      const t2 = { XMLHttpRequest: true, eventTarget: true, requestAnimationFrame: true, setInterval: true, setTimeout: true, ...e2 };
      return { name: "BrowserApiErrors", setupOnce() {
        t2.setTimeout && Ge(vr, "setTimeout", oi), t2.setInterval && Ge(vr, "setInterval", oi), t2.requestAnimationFrame && Ge(vr, "requestAnimationFrame", si), t2.XMLHttpRequest && "XMLHttpRequest" in vr && Ge(XMLHttpRequest.prototype, "send", ai);
        const e3 = t2.eventTarget;
        if (e3) {
          (Array.isArray(e3) ? e3 : ri).forEach(ci);
        }
      } };
    };
    li = () => ({ name: "BrowserSession", setupOnce() {
      void 0 !== vr.document ? (Cn({ ignoreDuration: true }), On(), Ur(({ from: e2, to: t2 }) => {
        void 0 !== e2 && e2 !== t2 && (Cn({ ignoreDuration: true }), On());
      })) : br && le.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.");
    } });
    ui = (e2 = {}) => {
      const t2 = { onerror: true, onunhandledrejection: true, ...e2 };
      return { name: "GlobalHandlers", setupOnce() {
        Error.stackTraceLimit = 50;
      }, setup(e3) {
        t2.onerror && (!function(e4) {
          !function(e5) {
            const t3 = "error";
            be(t3, e5), _e(t3, ke);
          }((t3) => {
            const { stackParser: n2, attachStacktrace: r2 } = hi();
            if (xt() !== e4 || mr())
              return;
            const { msg: i2, url: o2, line: s2, column: a2, error: c2 } = t3, l2 = function(e5, t4, n3, r3) {
              const i3 = e5.exception = e5.exception || {}, o3 = i3.values = i3.values || [], s3 = o3[0] = o3[0] || {}, a3 = s3.stacktrace = s3.stacktrace || {}, c3 = a3.frames = a3.frames || [], l3 = r3, u2 = n3, d2 = xe(t4) && t4.length > 0 ? t4 : function() {
                try {
                  return Ve.document.location.href;
                } catch (e6) {
                  return "";
                }
              }();
              0 === c3.length && c3.push({ colno: l3, filename: d2, function: ue, in_app: true, lineno: u2 });
              return e5;
            }(Or(n2, c2 || i2, void 0, r2, false), o2, s2, a2);
            l2.level = "error", Mn(l2, { originalException: c2, mechanism: { handled: false, type: "onerror" } });
          });
        }(e3), di("onerror")), t2.onunhandledrejection && (!function(e4) {
          !function(e5) {
            const t3 = "unhandledrejection";
            be(t3, e5), _e(t3, Ce);
          }((t3) => {
            const { stackParser: n2, attachStacktrace: r2 } = hi();
            if (xt() !== e4 || mr())
              return;
            const i2 = function(e5) {
              if (De(e5))
                return e5;
              try {
                if ("reason" in e5)
                  return e5.reason;
                if ("detail" in e5 && "reason" in e5.detail)
                  return e5.detail.reason;
              } catch (e6) {
              }
              return e5;
            }(t3), o2 = De(i2) ? { exception: { values: [{ type: "UnhandledRejection", value: `Non-Error promise rejection captured with value: ${String(i2)}` }] } } : Or(n2, i2, void 0, r2, true);
            o2.level = "error", Mn(o2, { originalException: i2, mechanism: { handled: false, type: "onunhandledrejection" } });
          });
        }(e3), di("onunhandledrejection"));
      } };
    };
    pi = () => ({ name: "HttpContext", preprocessEvent(e2) {
      if (!vr.navigator && !vr.location && !vr.document)
        return;
      const t2 = e2.request && e2.request.url || vr.location && vr.location.href, { referrer: n2 } = vr.document || {}, { userAgent: r2 } = vr.navigator || {}, i2 = { ...e2.request && e2.request.headers, ...n2 && { Referer: n2 }, ...r2 && { "User-Agent": r2 } }, o2 = { ...e2.request, ...t2 && { url: t2 }, headers: i2 };
      e2.request = o2;
    } });
    fi = (e2 = {}) => {
      const t2 = e2.limit || 5, n2 = e2.key || "cause";
      return { name: "LinkedErrors", preprocessEvent(e3, r2, i2) {
        const o2 = i2.getOptions();
        Kn(_r, o2.stackParser, o2.maxValueLength, n2, t2, e3, r2);
      } };
    };
    vi = "new";
    gi = "loading";
    mi = "loaded";
    yi = "joining-meeting";
    bi = "joined-meeting";
    _i = "left-meeting";
    wi = "error";
    Si = "blocked";
    ki = "off";
    Mi = "sendable";
    Ci = "loading";
    Ei = "interrupted";
    Ti = "playable";
    Oi = "unknown";
    Pi = "full";
    Ai = "lobby";
    ji = "none";
    Ii = "base";
    xi = "*";
    Li = "ejected";
    Di = "nbf-room";
    Ni = "nbf-token";
    Ri = "exp-room";
    Fi = "exp-token";
    Bi = "no-room";
    Ui = "meeting-full";
    Vi = "end-of-life";
    Ji = "not-allowed";
    $i = "connection-error";
    qi = "cam-in-use";
    zi = "mic-in-use";
    Wi = "cam-mic-in-use";
    Hi = "permissions";
    Gi = "undefined-mediadevices";
    Qi = "not-found";
    Ki = "constraints";
    Yi = "unknown";
    Xi = "iframe-ready-for-launch-config";
    Zi = "iframe-launch-config";
    eo = "theme-updated";
    to = "loading";
    no = "load-attempt-failed";
    ro = "loaded";
    io = "started-camera";
    oo = "camera-error";
    so = "joining-meeting";
    ao = "joined-meeting";
    co = "left-meeting";
    lo = "participant-joined";
    uo = "participant-updated";
    ho = "participant-left";
    po = "participant-counts-updated";
    fo = "access-state-updated";
    vo = "meeting-session-summary-updated";
    go = "meeting-session-state-updated";
    mo = "meeting-session-data-error";
    yo = "waiting-participant-added";
    bo = "waiting-participant-updated";
    _o = "waiting-participant-removed";
    wo = "track-started";
    So = "track-stopped";
    ko = "transcription-started";
    Mo = "transcription-stopped";
    Co = "transcription-error";
    Eo = "recording-started";
    To = "recording-stopped";
    Oo = "recording-stats";
    Po = "recording-error";
    Ao = "recording-upload-completed";
    jo = "recording-data";
    Io = "app-message";
    xo = "transcription-message";
    Lo = "remote-media-player-started";
    Do = "remote-media-player-updated";
    No = "remote-media-player-stopped";
    Ro = "local-screen-share-started";
    Fo = "local-screen-share-stopped";
    Bo = "local-screen-share-canceled";
    Uo = "active-speaker-change";
    Vo = "active-speaker-mode-change";
    Jo = "network-quality-change";
    $o = "network-connection";
    qo = "cpu-load-change";
    zo = "face-counts-updated";
    Wo = "fullscreen";
    Ho = "exited-fullscreen";
    Go = "live-streaming-started";
    Qo = "live-streaming-updated";
    Ko = "live-streaming-stopped";
    Yo = "live-streaming-error";
    Xo = "lang-updated";
    Zo = "receive-settings-updated";
    es = "input-settings-updated";
    ts = "nonfatal-error";
    ns = "error";
    rs = 4096;
    is = 102400;
    os = "iframe-call-message";
    ss = "local-screen-start";
    as = "daily-method-update-live-streaming-endpoints";
    cs = "transmit-log";
    ls = "daily-custom-track";
    us = { NONE: "none", BGBLUR: "background-blur", BGIMAGE: "background-image", FACE_DETECTION: "face-detection" };
    ds = { NONE: "none", NOISE_CANCELLATION: "noise-cancellation" };
    hs = { PLAY: "play", PAUSE: "pause" };
    ps = ["jpg", "png", "jpeg"];
    fs = "add-endpoints";
    vs = "remove-endpoints";
    gs = "sip-call-transfer";
    Ss = "none";
    ks = "software";
    Ms = "hardware";
    Cs = function() {
      try {
        var e2, t2 = document.createElement("canvas"), n2 = false;
        (e2 = t2.getContext("webgl2", { failIfMajorPerformanceCaveat: true })) || (n2 = true, e2 = t2.getContext("webgl2"));
        var r2 = null != e2;
        return t2.remove(), r2 ? n2 ? ks : Ms : Ss;
      } catch (e3) {
        return Ss;
      }
    }();
    Fs = function() {
      return o(function e2() {
        t(this, e2);
      }, [{ key: "addListenerForMessagesFromCallMachine", value: function(e2, t2, n2) {
        Y();
      } }, { key: "addListenerForMessagesFromDailyJs", value: function(e2, t2, n2) {
        Y();
      } }, { key: "sendMessageToCallMachine", value: function(e2, t2, n2, r2) {
        Y();
      } }, { key: "sendMessageToDailyJs", value: function(e2, t2) {
        Y();
      } }, { key: "removeListener", value: function(e2) {
        Y();
      } }]);
    }();
    Js = function() {
      function e2() {
        var n2, r2, i2, o2;
        return t(this, e2), r2 = this, i2 = a(i2 = e2), (n2 = s(r2, Vs() ? Reflect.construct(i2, o2 || [], a(r2).constructor) : i2.apply(r2, o2)))._wrappedListeners = {}, n2._messageCallbacks = {}, n2;
      }
      return l(e2, Fs), o(e2, [{ key: "addListenerForMessagesFromCallMachine", value: function(e3, t2, n2) {
        var r2 = this, i2 = function(i3) {
          if (i3.data && "iframe-call-message" === i3.data.what && (!i3.data.callClientId || i3.data.callClientId === t2) && (!i3.data.from || "module" !== i3.data.from)) {
            var o2 = Us({}, i3.data);
            if (delete o2.from, o2.callbackStamp && r2._messageCallbacks[o2.callbackStamp]) {
              var s2 = o2.callbackStamp;
              r2._messageCallbacks[s2].call(n2, o2), delete r2._messageCallbacks[s2];
            }
            delete o2.what, delete o2.callbackStamp, e3.call(n2, o2);
          }
        };
        this._wrappedListeners[e3] = i2, window.addEventListener("message", i2);
      } }, { key: "addListenerForMessagesFromDailyJs", value: function(e3, t2, n2) {
        var r2 = function(r3) {
          var i2;
          if (!(!r3.data || r3.data.what !== os || !r3.data.action || r3.data.from && "module" !== r3.data.from || r3.data.callClientId && t2 && r3.data.callClientId !== t2 || null != r3 && null !== (i2 = r3.data) && void 0 !== i2 && i2.callFrameId)) {
            var o2 = r3.data;
            e3.call(n2, o2);
          }
        };
        this._wrappedListeners[e3] = r2, window.addEventListener("message", r2);
      } }, { key: "sendMessageToCallMachine", value: function(e3, t2, n2, r2) {
        if (!n2)
          throw new Error("undefined callClientId. Are you trying to use a DailyCall instance previously destroyed?");
        var i2 = Us({}, e3);
        if (i2.what = os, i2.from = "module", i2.callClientId = n2, t2) {
          var o2 = K();
          this._messageCallbacks[o2] = t2, i2.callbackStamp = o2;
        }
        var s2 = r2 ? r2.contentWindow : window, a2 = this._callMachineTargetOrigin(r2);
        a2 && s2.postMessage(i2, a2);
      } }, { key: "sendMessageToDailyJs", value: function(e3, t2) {
        e3.what = os, e3.callClientId = t2, e3.from = "embedded", window.postMessage(e3, this._targetOriginFromWindowLocation());
      } }, { key: "removeListener", value: function(e3) {
        var t2 = this._wrappedListeners[e3];
        t2 && (window.removeEventListener("message", t2), delete this._wrappedListeners[e3]);
      } }, { key: "forwardPackagedMessageToCallMachine", value: function(e3, t2, n2) {
        var r2 = Us({}, e3);
        r2.callClientId = n2;
        var i2 = t2 ? t2.contentWindow : window, o2 = this._callMachineTargetOrigin(t2);
        o2 && i2.postMessage(r2, o2);
      } }, { key: "addListenerForPackagedMessagesFromCallMachine", value: function(e3, t2) {
        var n2 = function(n3) {
          if (n3.data && "iframe-call-message" === n3.data.what && (!n3.data.callClientId || n3.data.callClientId === t2) && (!n3.data.from || "module" !== n3.data.from)) {
            var r2 = n3.data;
            e3(r2);
          }
        };
        return this._wrappedListeners[e3] = n2, window.addEventListener("message", n2), e3;
      } }, { key: "removeListenerForPackagedMessagesFromCallMachine", value: function(e3) {
        var t2 = this._wrappedListeners[e3];
        t2 && (window.removeEventListener("message", t2), delete this._wrappedListeners[e3]);
      } }, { key: "_callMachineTargetOrigin", value: function(e3) {
        return e3 ? e3.src ? new URL(e3.src).origin : void 0 : this._targetOriginFromWindowLocation();
      } }, { key: "_targetOriginFromWindowLocation", value: function() {
        return "file:" === window.location.protocol ? "*" : window.location.origin;
      } }]);
    }();
    zs = function() {
      function e2() {
        var n2, r2, i2, o2;
        return t(this, e2), r2 = this, i2 = a(i2 = e2), n2 = s(r2, qs() ? Reflect.construct(i2, o2 || [], a(r2).constructor) : i2.apply(r2, o2)), global.callMachineToDailyJsEmitter = global.callMachineToDailyJsEmitter || new I.EventEmitter(), global.dailyJsToCallMachineEmitter = global.dailyJsToCallMachineEmitter || new I.EventEmitter(), n2._wrappedListeners = {}, n2._messageCallbacks = {}, n2;
      }
      return l(e2, Fs), o(e2, [{ key: "addListenerForMessagesFromCallMachine", value: function(e3, t2, n2) {
        this._addListener(e3, global.callMachineToDailyJsEmitter, t2, n2, "received call machine message");
      } }, { key: "addListenerForMessagesFromDailyJs", value: function(e3, t2, n2) {
        this._addListener(e3, global.dailyJsToCallMachineEmitter, t2, n2, "received daily-js message");
      } }, { key: "sendMessageToCallMachine", value: function(e3, t2, n2) {
        this._sendMessage(e3, global.dailyJsToCallMachineEmitter, n2, t2, "sending message to call machine");
      } }, { key: "sendMessageToDailyJs", value: function(e3, t2) {
        this._sendMessage(e3, global.callMachineToDailyJsEmitter, t2, null, "sending message to daily-js");
      } }, { key: "removeListener", value: function(e3) {
        var t2 = this._wrappedListeners[e3];
        t2 && (global.callMachineToDailyJsEmitter.removeListener("message", t2), global.dailyJsToCallMachineEmitter.removeListener("message", t2), delete this._wrappedListeners[e3]);
      } }, { key: "_addListener", value: function(e3, t2, n2, r2, i2) {
        var o2 = this, s2 = function(t3) {
          if (t3.callClientId === n2) {
            if (t3.callbackStamp && o2._messageCallbacks[t3.callbackStamp]) {
              var i3 = t3.callbackStamp;
              o2._messageCallbacks[i3].call(r2, t3), delete o2._messageCallbacks[i3];
            }
            e3.call(r2, t3);
          }
        };
        this._wrappedListeners[e3] = s2, t2.addListener("message", s2);
      } }, { key: "_sendMessage", value: function(e3, t2, n2, r2, i2) {
        var o2 = function(e4) {
          for (var t3 = 1; t3 < arguments.length; t3++) {
            var n3 = null != arguments[t3] ? arguments[t3] : {};
            t3 % 2 ? $s(Object(n3), true).forEach(function(t4) {
              u(e4, t4, n3[t4]);
            }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e4, Object.getOwnPropertyDescriptors(n3)) : $s(Object(n3)).forEach(function(t4) {
              Object.defineProperty(e4, t4, Object.getOwnPropertyDescriptor(n3, t4));
            });
          }
          return e4;
        }({}, e3);
        if (o2.callClientId = n2, r2) {
          var s2 = K();
          this._messageCallbacks[s2] = r2, o2.callbackStamp = s2;
        }
        t2.emit("message", o2);
      } }]);
    }();
    Ws = "replace";
    Hs = "shallow-merge";
    Gs = [Ws, Hs];
    Qs = function() {
      function e2() {
        var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.data, i2 = n2.mergeStrategy, o2 = void 0 === i2 ? Ws : i2;
        t(this, e2), e2._validateMergeStrategy(o2), e2._validateData(r2, o2), this.mergeStrategy = o2, this.data = r2;
      }
      return o(e2, [{ key: "isNoOp", value: function() {
        return e2.isNoOpUpdate(this.data, this.mergeStrategy);
      } }], [{ key: "isNoOpUpdate", value: function(e3, t2) {
        return 0 === Object.keys(e3).length && t2 === Hs;
      } }, { key: "_validateMergeStrategy", value: function(e3) {
        if (!Gs.includes(e3))
          throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(Gs, "]"));
      } }, { key: "_validateData", value: function(e3, t2) {
        if (!function(e4) {
          if (null == e4 || "object" !== n(e4))
            return false;
          var t3 = Object.getPrototypeOf(e4);
          return null == t3 || t3 === Object.prototype;
        }(e3))
          throw Error("Meeting session data must be a plain (map-like) object");
        var r2;
        try {
          if (r2 = JSON.stringify(e3), t2 === Ws) {
            var i2 = JSON.parse(r2);
            N(i2, e3) || console.warn("The meeting session data provided will be modified when serialized.", i2, e3);
          } else if (t2 === Hs) {
            for (var o2 in e3)
              if (Object.hasOwnProperty.call(e3, o2) && void 0 !== e3[o2]) {
                var s2 = JSON.parse(JSON.stringify(e3[o2]));
                N(e3[o2], s2) || console.warn("At least one key in the meeting session data provided will be modified when serialized.", s2, e3[o2]);
              }
          }
        } catch (e4) {
          throw Error("Meeting session data must be serializable to JSON: ".concat(e4));
        }
        if (r2.length > is)
          throw Error("Meeting session data is too large (".concat(r2.length, " characters). Maximum size suppported is ").concat(is, "."));
      } }]);
    }();
    ea = function() {
      return o(function e2(n2) {
        t(this, e2), this._currentLoad = null, this._callClientId = n2;
      }, [{ key: "load", value: function() {
        var e2, t2 = this, n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = arguments.length > 1 ? arguments[1] : void 0, i2 = arguments.length > 2 ? arguments[2] : void 0;
        if (this.loaded)
          return window._daily.instances[this._callClientId].callMachine.reset(), void r2(true);
        e2 = this._callClientId, window._daily.pendings.push(e2), this._currentLoad && this._currentLoad.cancel(), this._currentLoad = new ta(n2, function() {
          r2(false);
        }, function(e3, n3) {
          n3 || Zs(t2._callClientId), i2(e3, n3);
        }), this._currentLoad.start();
      } }, { key: "cancel", value: function() {
        this._currentLoad && this._currentLoad.cancel(), Zs(this._callClientId);
      } }, { key: "loaded", get: function() {
        return this._currentLoad && this._currentLoad.succeeded;
      } }]);
    }();
    ta = function() {
      return o(function e2() {
        var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = arguments.length > 1 ? arguments[1] : void 0, i2 = arguments.length > 2 ? arguments[2] : void 0;
        t(this, e2), this._attemptsRemaining = 3, this._currentAttempt = null, this._dailyConfig = n2, this._successCallback = r2, this._failureCallback = i2;
      }, [{ key: "start", value: function() {
        var e2 = this;
        if (!this._currentAttempt) {
          var t2 = function(n2) {
            e2._currentAttempt.cancelled || (e2._attemptsRemaining--, e2._failureCallback(n2, e2._attemptsRemaining > 0), e2._attemptsRemaining <= 0 || setTimeout(function() {
              e2._currentAttempt.cancelled || (e2._currentAttempt = new ia(e2._dailyConfig, e2._successCallback, t2), e2._currentAttempt.start());
            }, 3e3));
          };
          this._currentAttempt = new ia(this._dailyConfig, this._successCallback, t2), this._currentAttempt.start();
        }
      } }, { key: "cancel", value: function() {
        this._currentAttempt && this._currentAttempt.cancel();
      } }, { key: "cancelled", get: function() {
        return this._currentAttempt && this._currentAttempt.cancelled;
      } }, { key: "succeeded", get: function() {
        return this._currentAttempt && this._currentAttempt.succeeded;
      } }]);
    }();
    na = function() {
      function e2() {
        return t(this, e2), n2 = this, i2 = arguments, r2 = a(r2 = e2), s(n2, Xs() ? Reflect.construct(r2, i2 || [], a(n2).constructor) : r2.apply(n2, i2));
        var n2, r2, i2;
      }
      return l(e2, Ys(Error)), o(e2);
    }();
    ra = 2e4;
    ia = function() {
      return o(function e3(n2, r2, i2) {
        t(this, e3), this._loadAttemptImpl = ys() || !n2.avoidEval ? new oa(n2, r2, i2) : new sa(n2, r2, i2);
      }, [{ key: "start", value: (e2 = h(function* () {
        return this._loadAttemptImpl.start();
      }), function() {
        return e2.apply(this, arguments);
      }) }, { key: "cancel", value: function() {
        this._loadAttemptImpl.cancel();
      } }, { key: "cancelled", get: function() {
        return this._loadAttemptImpl.cancelled;
      } }, { key: "succeeded", get: function() {
        return this._loadAttemptImpl.succeeded;
      } }]);
      var e2;
    }();
    oa = function() {
      return o(function e3(n3, r3, i3) {
        t(this, e3), this.cancelled = false, this.succeeded = false, this._networkTimedOut = false, this._networkTimeout = null, this._iosCache = "undefined" != typeof iOSCallObjectBundleCache && iOSCallObjectBundleCache, this._refetchHeaders = null, this._dailyConfig = n3, this._successCallback = r3, this._failureCallback = i3;
      }, [{ key: "start", value: (i2 = h(function* () {
        var e3 = Z(this._dailyConfig);
        !(yield this._tryLoadFromIOSCache(e3)) && this._loadFromNetwork(e3);
      }), function() {
        return i2.apply(this, arguments);
      }) }, { key: "cancel", value: function() {
        clearTimeout(this._networkTimeout), this.cancelled = true;
      } }, { key: "_tryLoadFromIOSCache", value: (r2 = h(function* (e3) {
        if (!this._iosCache)
          return false;
        try {
          var t2 = yield this._iosCache.get(e3);
          return !!this.cancelled || !!t2 && (t2.code ? (Function('"use strict";' + t2.code)(), this.succeeded = true, this._successCallback(), true) : (this._refetchHeaders = t2.refetchHeaders, false));
        } catch (e4) {
          return false;
        }
      }), function(e3) {
        return r2.apply(this, arguments);
      }) }, { key: "_loadFromNetwork", value: (n2 = h(function* (e3) {
        var t2 = this;
        this._networkTimeout = setTimeout(function() {
          t2._networkTimedOut = true, t2._failureCallback({ msg: "Timed out (>".concat(ra, " ms) when loading call object bundle ").concat(e3), type: "timeout" });
        }, ra);
        try {
          var n3 = this._refetchHeaders ? { headers: this._refetchHeaders } : {}, r3 = yield fetch(e3, n3);
          if (clearTimeout(this._networkTimeout), this.cancelled || this._networkTimedOut)
            throw new na();
          var i3 = yield this._getBundleCodeFromResponse(e3, r3);
          if (this.cancelled)
            throw new na();
          Function('"use strict";' + i3)(), this._iosCache && this._iosCache.set(e3, i3, r3.headers), this.succeeded = true, this._successCallback();
        } catch (t3) {
          if (clearTimeout(this._networkTimeout), t3 instanceof na || this.cancelled || this._networkTimedOut)
            return;
          this._failureCallback({ msg: "Failed to load call object bundle ".concat(e3, ": ").concat(t3), type: t3.message });
        }
      }), function(e3) {
        return n2.apply(this, arguments);
      }) }, { key: "_getBundleCodeFromResponse", value: (e2 = h(function* (e3, t2) {
        if (t2.ok)
          return yield t2.text();
        if (this._iosCache && 304 === t2.status)
          return (yield this._iosCache.renew(e3, t2.headers)).code;
        throw new Error("Received ".concat(t2.status, " response"));
      }), function(t2, n3) {
        return e2.apply(this, arguments);
      }) }]);
      var e2, n2, r2, i2;
    }();
    sa = function() {
      return o(function e2(n2, r2, i2) {
        t(this, e2), this.cancelled = false, this.succeeded = false, this._dailyConfig = n2, this._successCallback = r2, this._failureCallback = i2, this._attemptId = K(), this._networkTimeout = null, this._scriptElement = null;
      }, [{ key: "start", value: function() {
        window._dailyCallMachineLoadWaitlist || (window._dailyCallMachineLoadWaitlist = /* @__PURE__ */ new Set());
        var e2 = Z(this._dailyConfig);
        "object" === ("undefined" == typeof document ? "undefined" : n(document)) ? this._startLoading(e2) : this._failureCallback({ msg: "Call object bundle must be loaded in a DOM/web context", type: "missing context" });
      } }, { key: "cancel", value: function() {
        this._stopLoading(), this.cancelled = true;
      } }, { key: "_startLoading", value: function(e2) {
        var t2 = this;
        this._signUpForCallMachineLoadWaitlist(), this._networkTimeout = setTimeout(function() {
          t2._stopLoading(), t2._failureCallback({ msg: "Timed out (>".concat(ra, " ms) when loading call object bundle ").concat(e2), type: "timeout" });
        }, ra);
        var n2 = document.getElementsByTagName("head")[0], r2 = document.createElement("script");
        this._scriptElement = r2, r2.onload = function() {
          t2._stopLoading(), t2.succeeded = true, t2._successCallback();
        }, r2.onerror = function(e3) {
          t2._stopLoading(), t2._failureCallback({ msg: "Failed to load call object bundle ".concat(e3.target.src), type: e3.message });
        }, r2.src = e2, n2.appendChild(r2);
      } }, { key: "_stopLoading", value: function() {
        this._withdrawFromCallMachineLoadWaitlist(), clearTimeout(this._networkTimeout), this._scriptElement && (this._scriptElement.onload = null, this._scriptElement.onerror = null);
      } }, { key: "_signUpForCallMachineLoadWaitlist", value: function() {
        window._dailyCallMachineLoadWaitlist.add(this._attemptId);
      } }, { key: "_withdrawFromCallMachineLoadWaitlist", value: function() {
        window._dailyCallMachineLoadWaitlist.delete(this._attemptId);
      } }]);
    }();
    aa = function(e2, t2, n2) {
      return true === ua(e2.local, t2, n2);
    };
    ca = function(e2, t2, n2) {
      return e2.local.streams && e2.local.streams[t2] && e2.local.streams[t2].stream && e2.local.streams[t2].stream["get".concat("video" === n2 ? "Video" : "Audio", "Tracks")]()[0];
    };
    la = function(e2, t2, n2, r2) {
      var i2 = da(e2, t2, n2, r2);
      return i2 && i2.pendingTrack;
    };
    ua = function(e2, t2, n2) {
      if (!e2)
        return false;
      var r2 = function(e3) {
        switch (e3) {
          case "avatar":
            return true;
          case "staged":
            return e3;
          default:
            return !!e3;
        }
      }, i2 = e2.public.subscribedTracks;
      return i2 && i2[t2] ? -1 === ["cam-audio", "cam-video", "screen-video", "screen-audio", "rmpAudio", "rmpVideo"].indexOf(n2) && i2[t2].custom ? [true, "staged"].includes(i2[t2].custom) ? r2(i2[t2].custom) : r2(i2[t2].custom[n2]) : r2(i2[t2][n2]) : !i2 || r2(i2.ALL);
    };
    da = function(e2, t2, n2, r2) {
      var i2 = Object.values(e2.streams || {}).filter(function(e3) {
        return e3.participantId === t2 && e3.type === n2 && e3.pendingTrack && e3.pendingTrack.kind === r2;
      }).sort(function(e3, t3) {
        return new Date(t3.starttime) - new Date(e3.starttime);
      });
      return i2 && i2[0];
    };
    ha = function(e2, t2) {
      var n2 = e2.local.public.customTracks;
      if (n2 && n2[t2])
        return n2[t2].track;
    };
    ba = /* @__PURE__ */ new Map();
    _a = null;
    ka = /* @__PURE__ */ new Map();
    Ma = null;
    Oa = /* @__PURE__ */ new Set();
    Ia = Object.freeze({ VIDEO: "video", AUDIO: "audio", SCREEN_VIDEO: "screenVideo", SCREEN_AUDIO: "screenAudio", CUSTOM_VIDEO: "customVideo", CUSTOM_AUDIO: "customAudio" });
    xa = Object.freeze({ PARTICIPANTS: "participants", STREAMING: "streaming", TRANSCRIPTION: "transcription" });
    La = Object.values(Ia);
    Da = ["v", "a", "sv", "sa", "cv", "ca"];
    Object.freeze(La.reduce(function(e2, t2, n2) {
      return e2[t2] = Da[n2], e2;
    }, {})), Object.freeze(Da.reduce(function(e2, t2, n2) {
      return e2[t2] = La[n2], e2;
    }, {}));
    Na = [Ia.VIDEO, Ia.AUDIO, Ia.SCREEN_VIDEO, Ia.SCREEN_AUDIO];
    Ra = Object.values(xa);
    Fa = ["p", "s", "t"];
    Object.freeze(Ra.reduce(function(e2, t2, n2) {
      return e2[t2] = Fa[n2], e2;
    }, {})), Object.freeze(Fa.reduce(function(e2, t2, n2) {
      return e2[t2] = Ra[n2], e2;
    }, {}));
    Ba = function() {
      function e2() {
        var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.base, i2 = n2.byUserId, o2 = n2.byParticipantId;
        t(this, e2), this.base = r2, this.byUserId = i2, this.byParticipantId = o2;
      }
      return o(e2, [{ key: "clone", value: function() {
        var t2 = new e2();
        if (this.base instanceof Ua ? t2.base = this.base.clone() : t2.base = this.base, void 0 !== this.byUserId)
          for (var n2 in t2.byUserId = {}, this.byUserId) {
            var r2 = this.byUserId[n2];
            t2.byUserId[n2] = r2 instanceof Ua ? r2.clone() : r2;
          }
        if (void 0 !== this.byParticipantId)
          for (var i2 in t2.byParticipantId = {}, this.byParticipantId) {
            var o2 = this.byParticipantId[i2];
            t2.byParticipantId[i2] = o2 instanceof Ua ? o2.clone() : o2;
          }
        return t2;
      } }, { key: "toJSONObject", value: function() {
        var e3 = {};
        if ("boolean" == typeof this.base ? e3.base = this.base : this.base instanceof Ua && (e3.base = this.base.toJSONObject()), void 0 !== this.byUserId)
          for (var t2 in e3.byUserId = {}, this.byUserId) {
            var n2 = this.byUserId[t2];
            e3.byUserId[t2] = n2 instanceof Ua ? n2.toJSONObject() : n2;
          }
        if (void 0 !== this.byParticipantId)
          for (var r2 in e3.byParticipantId = {}, this.byParticipantId) {
            var i2 = this.byParticipantId[r2];
            e3.byParticipantId[r2] = i2 instanceof Ua ? i2.toJSONObject() : i2;
          }
        return e3;
      } }, { key: "toMinifiedJSONObject", value: function() {
        var e3 = {};
        if (void 0 !== this.base && ("boolean" == typeof this.base ? e3.b = this.base : e3.b = this.base.toMinifiedJSONObject()), void 0 !== this.byUserId)
          for (var t2 in e3.u = {}, this.byUserId) {
            var n2 = this.byUserId[t2];
            e3.u[t2] = "boolean" == typeof n2 ? n2 : n2.toMinifiedJSONObject();
          }
        if (void 0 !== this.byParticipantId)
          for (var r2 in e3.p = {}, this.byParticipantId) {
            var i2 = this.byParticipantId[r2];
            e3.p[r2] = "boolean" == typeof i2 ? i2 : i2.toMinifiedJSONObject();
          }
        return e3;
      } }, { key: "normalize", value: function() {
        return this.base instanceof Ua && (this.base = this.base.normalize()), this.byUserId && (this.byUserId = Object.fromEntries(Object.entries(this.byUserId).map(function(e3) {
          var t2 = f(e3, 2), n2 = t2[0], r2 = t2[1];
          return [n2, r2 instanceof Ua ? r2.normalize() : r2];
        }))), this.byParticipantId && (this.byParticipantId = Object.fromEntries(Object.entries(this.byParticipantId).map(function(e3) {
          var t2 = f(e3, 2), n2 = t2[0], r2 = t2[1];
          return [n2, r2 instanceof Ua ? r2.normalize() : r2];
        }))), this;
      } }], [{ key: "fromJSONObject", value: function(t2) {
        var n2, r2, i2;
        if (void 0 !== t2.base && (n2 = "boolean" == typeof t2.base ? t2.base : Ua.fromJSONObject(t2.base)), void 0 !== t2.byUserId)
          for (var o2 in r2 = {}, t2.byUserId) {
            var s2 = t2.byUserId[o2];
            r2[o2] = "boolean" == typeof s2 ? s2 : Ua.fromJSONObject(s2);
          }
        if (void 0 !== t2.byParticipantId)
          for (var a2 in i2 = {}, t2.byParticipantId) {
            var c2 = t2.byParticipantId[a2];
            i2[a2] = "boolean" == typeof c2 ? c2 : Ua.fromJSONObject(c2);
          }
        return new e2({ base: n2, byUserId: r2, byParticipantId: i2 });
      } }, { key: "fromMinifiedJSONObject", value: function(t2) {
        var n2, r2, i2;
        if (void 0 !== t2.b && (n2 = "boolean" == typeof t2.b ? t2.b : Ua.fromMinifiedJSONObject(t2.b)), void 0 !== t2.u)
          for (var o2 in r2 = {}, t2.u) {
            var s2 = t2.u[o2];
            r2[o2] = "boolean" == typeof s2 ? s2 : Ua.fromMinifiedJSONObject(s2);
          }
        if (void 0 !== t2.p)
          for (var a2 in i2 = {}, t2.p) {
            var c2 = t2.p[a2];
            i2[a2] = "boolean" == typeof c2 ? c2 : Ua.fromMinifiedJSONObject(c2);
          }
        return new e2({ base: n2, byUserId: r2, byParticipantId: i2 });
      } }, { key: "validateJSONObject", value: function(e3) {
        if ("object" !== n(e3))
          return [false, "canReceive must be an object"];
        for (var t2 = ["base", "byUserId", "byParticipantId"], r2 = 0, i2 = Object.keys(e3); r2 < i2.length; r2++) {
          var o2 = i2[r2];
          if (!t2.includes(o2))
            return [false, "canReceive can only contain keys (".concat(t2.join(", "), ")")];
          if ("base" === o2) {
            var s2 = f(Ua.validateJSONObject(e3.base, true), 2), a2 = s2[0], c2 = s2[1];
            if (!a2)
              return [false, c2];
          } else {
            if ("object" !== n(e3[o2]))
              return [false, "invalid (non-object) value for field '".concat(o2, "' in canReceive")];
            for (var l2 = 0, u2 = Object.values(e3[o2]); l2 < u2.length; l2++) {
              var d2 = u2[l2], h2 = f(Ua.validateJSONObject(d2), 2), p2 = h2[0], v2 = h2[1];
              if (!p2)
                return [false, v2];
            }
          }
        }
        return [true];
      } }]);
    }();
    Ua = function() {
      function e2() {
        var n2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, r2 = n2.video, i2 = n2.audio, o2 = n2.screenVideo, s2 = n2.screenAudio, a2 = n2.customVideo, c2 = n2.customAudio;
        t(this, e2), this.video = r2, this.audio = i2, this.screenVideo = o2, this.screenAudio = s2, this.customVideo = a2, this.customAudio = c2;
      }
      return o(e2, [{ key: "clone", value: function() {
        var t2 = new e2();
        return void 0 !== this.video && (t2.video = this.video), void 0 !== this.audio && (t2.audio = this.audio), void 0 !== this.screenVideo && (t2.screenVideo = this.screenVideo), void 0 !== this.screenAudio && (t2.screenAudio = this.screenAudio), void 0 !== this.customVideo && (t2.customVideo = ja({}, this.customVideo)), void 0 !== this.customAudio && (t2.customAudio = ja({}, this.customAudio)), t2;
      } }, { key: "toJSONObject", value: function() {
        var e3 = {};
        return void 0 !== this.video && (e3.video = this.video), void 0 !== this.audio && (e3.audio = this.audio), void 0 !== this.screenVideo && (e3.screenVideo = this.screenVideo), void 0 !== this.screenAudio && (e3.screenAudio = this.screenAudio), void 0 !== this.customVideo && (e3.customVideo = ja({}, this.customVideo)), void 0 !== this.customAudio && (e3.customAudio = ja({}, this.customAudio)), e3;
      } }, { key: "toMinifiedJSONObject", value: function() {
        var e3 = {};
        return void 0 !== this.video && (e3.v = this.video), void 0 !== this.audio && (e3.a = this.audio), void 0 !== this.screenVideo && (e3.sv = this.screenVideo), void 0 !== this.screenAudio && (e3.sa = this.screenAudio), void 0 !== this.customVideo && (e3.cv = ja({}, this.customVideo)), void 0 !== this.customAudio && (e3.ca = ja({}, this.customAudio)), e3;
      } }, { key: "normalize", value: function() {
        function e3(e4, t2) {
          return e4 && 1 === Object.keys(e4).length && e4["*"] === t2;
        }
        return !(true !== this.video || true !== this.audio || true !== this.screenVideo || true !== this.screenAudio || !e3(this.customVideo, true) || !e3(this.customAudio, true)) || (false !== this.video || false !== this.audio || false !== this.screenVideo || false !== this.screenAudio || !e3(this.customVideo, false) || !e3(this.customAudio, false)) && this;
      } }], [{ key: "fromBoolean", value: function(t2) {
        return new e2({ video: t2, audio: t2, screenVideo: t2, screenAudio: t2, customVideo: { "*": t2 }, customAudio: { "*": t2 } });
      } }, { key: "fromJSONObject", value: function(t2) {
        return new e2({ video: t2.video, audio: t2.audio, screenVideo: t2.screenVideo, screenAudio: t2.screenAudio, customVideo: void 0 !== t2.customVideo ? ja({}, t2.customVideo) : void 0, customAudio: void 0 !== t2.customAudio ? ja({}, t2.customAudio) : void 0 });
      } }, { key: "fromMinifiedJSONObject", value: function(t2) {
        return new e2({ video: t2.v, audio: t2.a, screenVideo: t2.sv, screenAudio: t2.sa, customVideo: t2.cv, customAudio: t2.ca });
      } }, { key: "validateJSONObject", value: function(e3, t2) {
        if ("boolean" == typeof e3)
          return [true];
        if ("object" !== n(e3))
          return [false, "invalid (non-object, non-boolean) value in canReceive"];
        for (var r2 = Object.keys(e3), i2 = 0, o2 = r2; i2 < o2.length; i2++) {
          var s2 = o2[i2];
          if (!La.includes(s2))
            return [false, "invalid media type '".concat(s2, "' in canReceive")];
          if (Na.includes(s2)) {
            if ("boolean" != typeof e3[s2])
              return [false, "invalid (non-boolean) value for media type '".concat(s2, "' in canReceive")];
          } else {
            if ("object" !== n(e3[s2]))
              return [false, "invalid (non-object) value for media type '".concat(s2, "' in canReceive")];
            for (var a2 = 0, c2 = Object.values(e3[s2]); a2 < c2.length; a2++) {
              if ("boolean" != typeof c2[a2])
                return [false, "invalid (non-boolean) value for entry within '".concat(s2, "' in canReceive")];
            }
            if (t2 && void 0 === e3[s2]["*"])
              return [false, `canReceive "base" permission must specify "*" as an entry within '`.concat(s2, "'")];
          }
        }
        return t2 && r2.length !== La.length ? [false, 'canReceive "base" permission must specify all media types: '.concat(La.join(", "), " (or be set to a boolean shorthand)")] : [true];
      } }]);
    }();
    Va = ["result"];
    Ja = ["preserveIframe"];
    Ga = {};
    Qa = "video";
    Ka = "voice";
    Ya = ys() ? { data: {} } : { data: {}, topology: "none" };
    Xa = { present: 0, hidden: 0 };
    Za = { maxBitrate: { min: 1e5, max: 25e5 }, maxFramerate: { min: 1, max: 30 }, scaleResolutionDownBy: { min: 1, max: 8 } };
    ec = Object.keys(Za);
    tc = ["state", "volume", "simulcastEncodings"];
    nc = { androidInCallNotification: { title: "string", subtitle: "string", iconName: "string", disableForCustomOverride: "boolean" }, disableAutoDeviceManagement: { audio: "boolean", video: "boolean" } };
    rc = { id: { iconPath: "string", iconPathDarkMode: "string", label: "string", tooltip: "string", visualState: "'default' | 'sidebar-open' | 'active'" } };
    ic = { id: { allow: "string", controlledBy: "'*' | 'owners' | string[]", csp: "string", iconURL: "string", label: "string", loading: "'eager' | 'lazy'", location: "'main' | 'sidebar'", name: "string", referrerPolicy: "string", sandbox: "string", src: "string", srcdoc: "string", shared: "string[] | 'owners' | boolean" } };
    oc = { customIntegrations: { validate: Pc, help: Tc() }, customTrayButtons: { validate: Oc, help: "customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(rc)) }, url: { validate: function(e2) {
      return "string" == typeof e2;
    }, help: "url should be a string" }, baseUrl: { validate: function(e2) {
      return "string" == typeof e2;
    }, help: "baseUrl should be a string" }, token: { validate: function(e2) {
      return "string" == typeof e2;
    }, help: "token should be a string", queryString: "t" }, dailyConfig: { validate: function(e2, t2) {
      try {
        return t2.validateDailyConfig(e2), true;
      } catch (e3) {
        console.error("Failed to validate dailyConfig", e3);
      }
      return false;
    }, help: "Unsupported dailyConfig. Check error logs for detailed info." }, reactNativeConfig: { validate: function(e2) {
      return Ac(e2, nc);
    }, help: "reactNativeConfig should look like ".concat(JSON.stringify(nc), ", all fields optional") }, lang: { validate: function(e2) {
      return ["da", "de", "en-us", "en", "es", "fi", "fr", "it", "jp", "ka", "nl", "no", "pl", "pt", "pt-BR", "ru", "sv", "tr", "user"].includes(e2);
    }, help: "language not supported. Options are: da, de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user" }, userName: true, userData: { validate: function(e2) {
      try {
        return yc(e2), true;
      } catch (e3) {
        return console.error(e3), false;
      }
    }, help: "invalid userData type provided" }, startVideoOff: true, startAudioOff: true, allowLocalVideo: true, allowLocalAudio: true, activeSpeakerMode: true, showLeaveButton: true, showLocalVideo: true, showParticipantsBar: true, showFullscreenButton: true, showUserNameChangeUI: true, iframeStyle: true, customLayout: true, cssFile: true, cssText: true, bodyClass: true, videoSource: { validate: function(e2, t2) {
      if ("boolean" == typeof e2)
        return t2._preloadCache.allowLocalVideo = e2, true;
      var n2;
      if (e2 instanceof MediaStreamTrack)
        t2._sharedTracks.videoTrack = e2, n2 = { customTrack: ls };
      else {
        if (delete t2._sharedTracks.videoTrack, "string" != typeof e2)
          return console.error("videoSource must be a MediaStreamTrack, boolean, or a string"), false;
        n2 = { deviceId: e2 };
      }
      return t2._updatePreloadCacheInputSettings({ video: { settings: n2 } }, false), true;
    } }, audioSource: { validate: function(e2, t2) {
      if ("boolean" == typeof e2)
        return t2._preloadCache.allowLocalAudio = e2, true;
      var n2;
      if (e2 instanceof MediaStreamTrack)
        t2._sharedTracks.audioTrack = e2, n2 = { customTrack: ls };
      else {
        if (delete t2._sharedTracks.audioTrack, "string" != typeof e2)
          return console.error("audioSource must be a MediaStreamTrack, boolean, or a string"), false;
        n2 = { deviceId: e2 };
      }
      return t2._updatePreloadCacheInputSettings({ audio: { settings: n2 } }, false), true;
    } }, subscribeToTracksAutomatically: { validate: function(e2, t2) {
      return t2._preloadCache.subscribeToTracksAutomatically = e2, true;
    } }, theme: { validate: function(e2) {
      var t2 = ["accent", "accentText", "background", "backgroundAccent", "baseText", "border", "mainAreaBg", "mainAreaBgAccent", "mainAreaText", "supportiveText"], r2 = function(e3) {
        for (var n2 = 0, r3 = Object.keys(e3); n2 < r3.length; n2++) {
          var i2 = r3[n2];
          if (!t2.includes(i2))
            return console.error('unsupported color "'.concat(i2, '". Valid colors: ').concat(t2.join(", "))), false;
          if (!e3[i2].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i))
            return console.error("".concat(i2, ' theme color should be provided in valid hex color format. Received: "').concat(e3[i2], '"')), false;
        }
        return true;
      };
      return "object" === n(e2) && ("light" in e2 && "dark" in e2 || "colors" in e2) ? "light" in e2 && "dark" in e2 ? "colors" in e2.light ? "colors" in e2.dark ? r2(e2.light.colors) && r2(e2.dark.colors) : (console.error('Dark theme is missing "colors" property.', e2), false) : (console.error('Light theme is missing "colors" property.', e2), false) : r2(e2.colors) : (console.error('Theme must contain either both "light" and "dark" properties, or "colors".', e2), false);
    }, help: "unsupported theme configuration. Check error logs for detailed info." }, layoutConfig: { validate: function(e2) {
      if ("grid" in e2) {
        var t2 = e2.grid;
        if ("maxTilesPerPage" in t2) {
          if (!Number.isInteger(t2.maxTilesPerPage))
            return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(t2.maxTilesPerPage, ".")), false;
          if (t2.maxTilesPerPage > 49)
            return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."), false;
        }
        if ("minTilesPerPage" in t2) {
          if (!Number.isInteger(t2.minTilesPerPage))
            return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(t2.minTilesPerPage, ".")), false;
          if (t2.minTilesPerPage < 1)
            return console.error("grid.minTilesPerPage can't be lower than 1."), false;
          if ("maxTilesPerPage" in t2 && t2.minTilesPerPage > t2.maxTilesPerPage)
            return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."), false;
        }
      }
      return true;
    }, help: "unsupported layoutConfig. Check error logs for detailed info." }, receiveSettings: { validate: function(e2) {
      return bc(e2, { allowAllParticipantsKey: false });
    }, help: Ec({ allowAllParticipantsKey: false }) }, sendSettings: { validate: function(e2, t2) {
      return !!function(e3, t3) {
        try {
          return t3.validateUpdateSendSettings(e3), true;
        } catch (e4) {
          return console.error("Failed to validate send settings", e4), false;
        }
      }(e2, t2) && (t2._preloadCache.sendSettings = e2, true);
    }, help: "Invalid sendSettings provided. Check error logs for detailed info." }, inputSettings: { validate: function(e2, t2) {
      var n2;
      return !!_c(e2) && (t2._inputSettings || (t2._inputSettings = {}), wc(e2, null === (n2 = t2.properties) || void 0 === n2 ? void 0 : n2.dailyConfig, t2._sharedTracks), t2._updatePreloadCacheInputSettings(e2, true), true);
    }, help: Cc() }, layout: { validate: function(e2) {
      return "custom-v1" === e2 || "browser" === e2 || "none" === e2;
    }, help: 'layout may only be set to "custom-v1"', queryString: "layout" }, emb: { queryString: "emb" }, embHref: { queryString: "embHref" }, dailyJsVersion: { queryString: "dailyJsVersion" }, proxy: { queryString: "proxy" }, strictMode: true, allowMultipleCallInstances: true };
    sc = { styles: { validate: function(e2) {
      for (var t2 in e2)
        if ("cam" !== t2 && "screen" !== t2)
          return false;
      if (e2.cam) {
        for (var n2 in e2.cam)
          if ("div" !== n2 && "video" !== n2)
            return false;
      }
      if (e2.screen) {
        for (var r2 in e2.screen)
          if ("div" !== r2 && "video" !== r2)
            return false;
      }
      return true;
    }, help: "styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }" }, setSubscribedTracks: { validate: function(e2, t2) {
      if (t2._preloadCache.subscribeToTracksAutomatically)
        return false;
      var n2 = [true, false, "staged"];
      if (n2.includes(e2) || !ys() && "avatar" === e2)
        return true;
      var r2 = ["audio", "video", "screenAudio", "screenVideo", "rmpAudio", "rmpVideo"], i2 = function(e3) {
        var t3 = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
        for (var o2 in e3)
          if ("custom" === o2) {
            if (!n2.includes(e3[o2]) && !i2(e3[o2], true))
              return false;
          } else {
            var s2 = !t3 && !r2.includes(o2), a2 = !n2.includes(e3[o2]);
            if (s2 || a2)
              return false;
          }
        return true;
      };
      return i2(e2);
    }, help: "setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: " + "true".concat(ys() ? "" : " | 'avatar'", " | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }") }, setAudio: true, setVideo: true, setScreenShare: { validate: function(e2) {
      return false === e2;
    }, help: "setScreenShare must be false, as it's only meant for stopping remote participants' screen shares" }, eject: true, updatePermissions: { validate: function(e2) {
      for (var t2 = 0, n2 = Object.entries(e2); t2 < n2.length; t2++) {
        var r2 = f(n2[t2], 2), i2 = r2[0], o2 = r2[1];
        switch (i2) {
          case "hasPresence":
            if ("boolean" != typeof o2)
              return false;
            break;
          case "canSend":
            if (o2 instanceof Set || o2 instanceof Array || Array.isArray(o2)) {
              var s2, a2 = ["video", "audio", "screenVideo", "screenAudio", "customVideo", "customAudio"], c2 = Wa(o2);
              try {
                for (c2.s(); !(s2 = c2.n()).done; ) {
                  var l2 = s2.value;
                  if (!a2.includes(l2))
                    return false;
                }
              } catch (e3) {
                c2.e(e3);
              } finally {
                c2.f();
              }
            } else if ("boolean" != typeof o2)
              return false;
            (o2 instanceof Array || Array.isArray(o2)) && (e2.canSend = new Set(o2));
            break;
          case "canReceive":
            var u2 = f(Ba.validateJSONObject(o2), 2), d2 = u2[0], h2 = u2[1];
            if (!d2)
              return console.error(h2), false;
            break;
          case "canAdmin":
            if (o2 instanceof Set || o2 instanceof Array || Array.isArray(o2)) {
              var p2, v2 = ["participants", "streaming", "transcription"], g2 = Wa(o2);
              try {
                for (g2.s(); !(p2 = g2.n()).done; ) {
                  var m2 = p2.value;
                  if (!v2.includes(m2))
                    return false;
                }
              } catch (e3) {
                g2.e(e3);
              } finally {
                g2.f();
              }
            } else if ("boolean" != typeof o2)
              return false;
            (o2 instanceof Array || Array.isArray(o2)) && (e2.canAdmin = new Set(o2));
            break;
          default:
            return false;
        }
      }
      return true;
    }, help: "updatePermissions can take hasPresence, canSend, canReceive, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canReceive must be an object specifying base, byUserId, and/or byParticipantId fields (see documentation for more details). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)." } };
    Promise.any || (Promise.any = function() {
      var e2 = h(function* (e3) {
        return new Promise(function(t2, n2) {
          var r2 = [];
          e3.forEach(function(i2) {
            return Promise.resolve(i2).then(function(e4) {
              t2(e4);
            }).catch(function(t3) {
              r2.push(t3), r2.length === e3.length && n2(r2);
            });
          });
        });
      });
      return function(t2) {
        return e2.apply(this, arguments);
      };
    }());
    ac = function() {
      function r2(e2) {
        var n2, i3, o2, c3, l2, d3, p3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        if (t(this, r2), o2 = this, c3 = a(c3 = r2), u(i3 = s(o2, za() ? Reflect.construct(c3, l2 || [], a(o2).constructor) : c3.apply(o2, l2)), "startListeningForDeviceChanges", function() {
          Ca(i3.handleDeviceChange);
        }), u(i3, "stopListeningForDeviceChanges", function() {
          Ea(i3.handleDeviceChange);
        }), u(i3, "handleDeviceChange", function(e3) {
          e3 = e3.map(function(e4) {
            return JSON.parse(JSON.stringify(e4));
          }), i3.emitDailyJSEvent({ action: "available-devices-updated", availableDevices: e3 });
        }), u(i3, "handleNativeAppStateChange", function() {
          var e3 = h(function* (e4) {
            if ("destroyed" === e4)
              return console.warn("App has been destroyed before leaving the meeting. Cleaning up all the resources!"), void (yield i3.destroy());
            var t2 = "active" === e4;
            i3.disableReactNativeAutoDeviceManagement("video") || (t2 ? i3.camUnmutedBeforeLosingNativeActiveState && i3.setLocalVideo(true) : (i3.camUnmutedBeforeLosingNativeActiveState = i3.localVideo(), i3.camUnmutedBeforeLosingNativeActiveState && i3.setLocalVideo(false)));
          });
          return function(t2) {
            return e3.apply(this, arguments);
          };
        }()), u(i3, "handleNativeAudioFocusChange", function(e3) {
          i3.disableReactNativeAutoDeviceManagement("audio") || (i3._hasNativeAudioFocus = e3, i3.toggleParticipantAudioBasedOnNativeAudioFocus(), i3._hasNativeAudioFocus ? i3.micUnmutedBeforeLosingNativeAudioFocus && i3.setLocalAudio(true) : (i3.micUnmutedBeforeLosingNativeAudioFocus = i3.localAudio(), i3.setLocalAudio(false)));
        }), u(i3, "handleNativeSystemScreenCaptureStop", function() {
          i3.stopScreenShare();
        }), i3.strictMode = void 0 === p3.strictMode || p3.strictMode, i3.allowMultipleCallInstances = null !== (n2 = p3.allowMultipleCallInstances) && void 0 !== n2 && n2, Object.keys(Ga).length && (i3._logDuplicateInstanceAttempt(), !i3.allowMultipleCallInstances)) {
          if (i3.strictMode)
            throw new Error("Duplicate DailyIframe instances are not allowed");
          console.warn("Using strictMode: false to allow multiple call instances is now deprecated. Set `allowMultipleCallInstances: true`");
        }
        if (window._daily || (window._daily = { pendings: [], instances: {} }), i3.callClientId = K(), Ga[(d3 = i3).callClientId] = d3, window._daily.instances[i3.callClientId] = {}, i3._sharedTracks = {}, window._daily.instances[i3.callClientId].tracks = i3._sharedTracks, p3.dailyJsVersion = r2.version(), i3._iframe = e2, i3._callObjectMode = "none" === p3.layout && !i3._iframe, i3._preloadCache = { subscribeToTracksAutomatically: true, outputDeviceId: null, inputSettings: null, sendSettings: null, videoTrackForNetworkConnectivityTest: null, videoTrackForConnectionQualityTest: null }, void 0 !== p3.showLocalVideo ? i3._callObjectMode ? console.error("showLocalVideo is not available in call object mode") : i3._showLocalVideo = !!p3.showLocalVideo : i3._showLocalVideo = true, void 0 !== p3.showParticipantsBar ? i3._callObjectMode ? console.error("showParticipantsBar is not available in call object mode") : i3._showParticipantsBar = !!p3.showParticipantsBar : i3._showParticipantsBar = true, void 0 !== p3.customIntegrations ? i3._callObjectMode ? console.error("customIntegrations is not available in call object mode") : i3._customIntegrations = p3.customIntegrations : i3._customIntegrations = {}, void 0 !== p3.customTrayButtons ? i3._callObjectMode ? console.error("customTrayButtons is not available in call object mode") : i3._customTrayButtons = p3.customTrayButtons : i3._customTrayButtons = {}, void 0 !== p3.activeSpeakerMode ? i3._callObjectMode ? console.error("activeSpeakerMode is not available in call object mode") : i3._activeSpeakerMode = !!p3.activeSpeakerMode : i3._activeSpeakerMode = false, p3.receiveSettings ? i3._callObjectMode ? i3._receiveSettings = p3.receiveSettings : console.error("receiveSettings is only available in call object mode") : i3._receiveSettings = {}, i3.validateProperties(p3), i3.properties = qa({}, p3), i3._inputSettings || (i3._inputSettings = {}), i3._callObjectLoader = i3._callObjectMode ? new ea(i3.callClientId) : null, i3._callState = vi, i3._isPreparingToJoin = false, i3._accessState = { access: Oi }, i3._meetingSessionSummary = {}, i3._finalSummaryOfPrevSession = {}, i3._meetingSessionState = Lc(Ya, i3._callObjectMode), i3._nativeInCallAudioMode = Qa, i3._participants = {}, i3._isScreenSharing = false, i3._participantCounts = Xa, i3._rmpPlayerState = {}, i3._waitingParticipants = {}, i3._network = { threshold: "good", quality: 100, networkState: "unknown", stats: {} }, i3._activeSpeaker = {}, i3._localAudioLevel = 0, i3._isLocalAudioLevelObserverRunning = false, i3._remoteParticipantsAudioLevel = {}, i3._isRemoteParticipantsAudioLevelObserverRunning = false, i3._maxAppMessageSize = rs, i3._messageChannel = ys() ? new zs() : new Js(), i3._iframe && (i3._iframe.requestFullscreen ? i3._iframe.addEventListener("fullscreenchange", function() {
          document.fullscreenElement === i3._iframe ? (i3.emitDailyJSEvent({ action: Wo }), i3.sendMessageToCallMachine({ action: Wo })) : (i3.emitDailyJSEvent({ action: Ho }), i3.sendMessageToCallMachine({ action: Ho }));
        }) : i3._iframe.webkitRequestFullscreen && i3._iframe.addEventListener("webkitfullscreenchange", function() {
          document.webkitFullscreenElement === i3._iframe ? (i3.emitDailyJSEvent({ action: Wo }), i3.sendMessageToCallMachine({ action: Wo })) : (i3.emitDailyJSEvent({ action: Ho }), i3.sendMessageToCallMachine({ action: Ho }));
        })), ys()) {
          var f2 = i3.nativeUtils();
          f2.addAudioFocusChangeListener && f2.removeAudioFocusChangeListener && f2.addAppStateChangeListener && f2.removeAppStateChangeListener && f2.addSystemScreenCaptureStopListener && f2.removeSystemScreenCaptureStopListener || console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"), i3._hasNativeAudioFocus = true, f2.addAudioFocusChangeListener(i3.handleNativeAudioFocusChange), f2.addAppStateChangeListener(i3.handleNativeAppStateChange), f2.addSystemScreenCaptureStopListener(i3.handleNativeSystemScreenCaptureStop);
        }
        return i3._callObjectMode && i3.startListeningForDeviceChanges(), i3._messageChannel.addListenerForMessagesFromCallMachine(i3.handleMessageFromCallMachine, i3.callClientId, i3), i3;
      }
      return l(r2, x), o(r2, [{ key: "destroy", value: (ee2 = h(function* () {
        var e2;
        try {
          yield this.leave();
        } catch (e3) {
        }
        var t2 = this._iframe;
        if (t2) {
          var n2 = t2.parentElement;
          n2 && n2.removeChild(t2);
        }
        if (this._messageChannel.removeListener(this.handleMessageFromCallMachine), ys()) {
          var r3 = this.nativeUtils();
          r3.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange), r3.removeAppStateChangeListener(this.handleNativeAppStateChange), r3.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop);
        }
        this._callObjectMode && this.stopListeningForDeviceChanges(), this.resetMeetingDependentVars(), this._destroyed = true, this.emitDailyJSEvent({ action: "call-instance-destroyed" }), delete Ga[this.callClientId], (null === (e2 = window) || void 0 === e2 || null === (e2 = e2._daily) || void 0 === e2 ? void 0 : e2.instances) && delete window._daily.instances[this.callClientId], this.strictMode && (this.callClientId = void 0);
      }), function() {
        return ee2.apply(this, arguments);
      }) }, { key: "isDestroyed", value: function() {
        return !!this._destroyed;
      } }, { key: "loadCss", value: function(e2) {
        var t2 = e2.bodyClass, n2 = e2.cssFile, r3 = e2.cssText;
        return gc(), this.sendMessageToCallMachine({ action: "load-css", cssFile: this.absoluteUrl(n2), bodyClass: t2, cssText: r3 }), this;
      } }, { key: "iframe", value: function() {
        return gc(), this._iframe;
      } }, { key: "meetingState", value: function() {
        return this._callState;
      } }, { key: "accessState", value: function() {
        return fc(this._callObjectMode, "accessState()"), this._accessState;
      } }, { key: "participants", value: function() {
        return this._participants;
      } }, { key: "participantCounts", value: function() {
        return this._participantCounts;
      } }, { key: "waitingParticipants", value: function() {
        return fc(this._callObjectMode, "waitingParticipants()"), this._waitingParticipants;
      } }, { key: "validateParticipantProperties", value: function(e2, t2) {
        for (var n2 in t2) {
          if (!sc[n2])
            throw new Error("unrecognized updateParticipant property ".concat(n2));
          if (sc[n2].validate && !sc[n2].validate(t2[n2], this, this._participants[e2]))
            throw new Error(sc[n2].help);
        }
      } }, { key: "updateParticipant", value: function(e2, t2) {
        return this._participants.local && this._participants.local.session_id === e2 && (e2 = "local"), e2 && t2 && (this.validateParticipantProperties(e2, t2), this.sendMessageToCallMachine({ action: "update-participant", id: e2, properties: t2 })), this;
      } }, { key: "updateParticipants", value: function(e2) {
        var t2 = this._participants.local && this._participants.local.session_id;
        for (var n2 in e2)
          n2 === t2 && (n2 = "local"), n2 && e2[n2] && this.validateParticipantProperties(n2, e2[n2]);
        return this.sendMessageToCallMachine({ action: "update-participants", participants: e2 }), this;
      } }, { key: "updateWaitingParticipant", value: (Y2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : "", r3 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        if (fc(this._callObjectMode, "updateWaitingParticipant()"), lc(this._callState, "updateWaitingParticipant()"), "string" != typeof t2 || "object" !== n(r3))
          throw new Error("updateWaitingParticipant() must take an id string and a updates object");
        return new Promise(function(n2, i3) {
          e2.sendMessageToCallMachine({ action: "daily-method-update-waiting-participant", id: t2, updates: r3 }, function(e3) {
            e3.error && i3(e3.error), e3.id || i3(new Error("unknown error in updateWaitingParticipant()")), n2({ id: e3.id });
          });
        });
      }), function() {
        return Y2.apply(this, arguments);
      }) }, { key: "updateWaitingParticipants", value: (G2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        if (fc(this._callObjectMode, "updateWaitingParticipants()"), lc(this._callState, "updateWaitingParticipants()"), "object" !== n(t2))
          throw new Error("updateWaitingParticipants() must take a mapping between ids and update objects");
        return new Promise(function(n2, r3) {
          e2.sendMessageToCallMachine({ action: "daily-method-update-waiting-participants", updatesById: t2 }, function(e3) {
            e3.error && r3(e3.error), e3.ids || r3(new Error("unknown error in updateWaitingParticipants()")), n2({ ids: e3.ids });
          });
        });
      }), function() {
        return G2.apply(this, arguments);
      }) }, { key: "requestAccess", value: (H2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, n2 = t2.access, r3 = void 0 === n2 ? { level: Pi } : n2, i3 = t2.name, o2 = void 0 === i3 ? "" : i3;
        return fc(this._callObjectMode, "requestAccess()"), lc(this._callState, "requestAccess()"), new Promise(function(t3, n3) {
          e2.sendMessageToCallMachine({ action: "daily-method-request-access", access: r3, name: o2 }, function(e3) {
            e3.error && n3(e3.error), e3.access || n3(new Error("unknown error in requestAccess()")), t3({ access: e3.access, granted: e3.granted });
          });
        });
      }), function() {
        return H2.apply(this, arguments);
      }) }, { key: "localAudio", value: function() {
        return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.audio.state) : null;
      } }, { key: "localVideo", value: function() {
        return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.video.state) : null;
      } }, { key: "setLocalAudio", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        return "forceDiscardTrack" in t2 && (ys() ? (console.warn("forceDiscardTrack option not supported in React Native; ignoring"), t2 = {}) : e2 && (console.warn("forceDiscardTrack option only supported when calling setLocalAudio(false); ignoring"), t2 = {})), this.sendMessageToCallMachine({ action: "local-audio", state: e2, options: t2 }), this;
      } }, { key: "localScreenAudio", value: function() {
        return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.screenAudio.state) : null;
      } }, { key: "localScreenVideo", value: function() {
        return this._participants.local ? !["blocked", "off"].includes(this._participants.local.tracks.screenVideo.state) : null;
      } }, { key: "updateScreenShare", value: function(e2) {
        if (this._isScreenSharing)
          return this.sendMessageToCallMachine({ action: "local-screen-update", options: e2 }), this;
        console.warn("There is no screen share in progress. Try calling startScreenShare first.");
      } }, { key: "setLocalVideo", value: function(e2) {
        return this.sendMessageToCallMachine({ action: "local-video", state: e2 }), this;
      } }, { key: "_setAllowLocalAudio", value: function(e2) {
        if (this._preloadCache.allowLocalAudio = e2, this._callMachineInitialized)
          return this.sendMessageToCallMachine({ action: "set-allow-local-audio", state: e2 }), this;
      } }, { key: "_setAllowLocalVideo", value: function(e2) {
        if (this._preloadCache.allowLocalVideo = e2, this._callMachineInitialized)
          return this.sendMessageToCallMachine({ action: "set-allow-local-video", state: e2 }), this;
      } }, { key: "getReceiveSettings", value: (W2 = h(function* (e2) {
        var t2 = this, r3 = (arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {}).showInheritedValues, i3 = void 0 !== r3 && r3;
        if (fc(this._callObjectMode, "getReceiveSettings()"), !this._callMachineInitialized)
          return this._receiveSettings;
        switch (n(e2)) {
          case "string":
            return new Promise(function(n2) {
              t2.sendMessageToCallMachine({ action: "get-single-participant-receive-settings", id: e2, showInheritedValues: i3 }, function(e3) {
                n2(e3.receiveSettings);
              });
            });
          case "undefined":
            return this._receiveSettings;
          default:
            throw new Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments');
        }
      }), function(e2) {
        return W2.apply(this, arguments);
      }) }, { key: "updateReceiveSettings", value: (z2 = h(function* (e2) {
        var t2 = this;
        if (fc(this._callObjectMode, "updateReceiveSettings()"), !bc(e2, { allowAllParticipantsKey: true }))
          throw new Error(Ec({ allowAllParticipantsKey: true }));
        return lc(this._callState, "updateReceiveSettings()", "To specify receive settings earlier, use the receiveSettings config property."), new Promise(function(n2) {
          t2.sendMessageToCallMachine({ action: "update-receive-settings", receiveSettings: e2 }, function(e3) {
            n2({ receiveSettings: e3.receiveSettings });
          });
        });
      }), function(e2) {
        return z2.apply(this, arguments);
      }) }, { key: "_prepInputSettingsForSharing", value: function(e2, t2) {
        if (e2) {
          var n2 = {};
          if (e2.audio) {
            var r3, i3, o2;
            e2.audio.settings && (!Object.keys(e2.audio.settings).length && t2 || (n2.audio = { settings: qa({}, e2.audio.settings) })), t2 && null !== (r3 = n2.audio) && void 0 !== r3 && null !== (r3 = r3.settings) && void 0 !== r3 && r3.customTrack && (n2.audio.settings = { customTrack: this._sharedTracks.audioTrack });
            var s2 = "none" === (null === (i3 = e2.audio.processor) || void 0 === i3 ? void 0 : i3.type) && (null === (o2 = e2.audio.processor) || void 0 === o2 ? void 0 : o2._isDefaultWhenNone);
            if (e2.audio.processor && !s2) {
              var a2 = qa({}, e2.audio.processor);
              delete a2._isDefaultWhenNone, n2.audio = qa(qa({}, n2.audio), {}, { processor: a2 });
            }
          }
          if (e2.video) {
            var c3, l2, u2;
            e2.video.settings && (!Object.keys(e2.video.settings).length && t2 || (n2.video = { settings: qa({}, e2.video.settings) })), t2 && null !== (c3 = n2.video) && void 0 !== c3 && null !== (c3 = c3.settings) && void 0 !== c3 && c3.customTrack && (n2.video.settings = { customTrack: this._sharedTracks.videoTrack });
            var d3 = "none" === (null === (l2 = e2.video.processor) || void 0 === l2 ? void 0 : l2.type) && (null === (u2 = e2.video.processor) || void 0 === u2 ? void 0 : u2._isDefaultWhenNone);
            if (e2.video.processor && !d3) {
              var h2 = qa({}, e2.video.processor);
              delete h2._isDefaultWhenNone, n2.video = qa(qa({}, n2.video), {}, { processor: h2 });
            }
          }
          return n2;
        }
      } }, { key: "getInputSettings", value: function() {
        var e2 = this;
        return gc(), new Promise(function(t2) {
          t2(e2._getInputSettings());
        });
      } }, { key: "_getInputSettings", value: function() {
        var e2, t2, n2, r3, i3, o2, s2 = { processor: { type: "none", _isDefaultWhenNone: true } };
        this._inputSettings ? (e2 = (null === (n2 = this._inputSettings) || void 0 === n2 ? void 0 : n2.video) || s2, t2 = (null === (r3 = this._inputSettings) || void 0 === r3 ? void 0 : r3.audio) || s2) : (e2 = (null === (i3 = this._preloadCache) || void 0 === i3 || null === (i3 = i3.inputSettings) || void 0 === i3 ? void 0 : i3.video) || s2, t2 = (null === (o2 = this._preloadCache) || void 0 === o2 || null === (o2 = o2.inputSettings) || void 0 === o2 ? void 0 : o2.audio) || s2);
        var a2 = { audio: t2, video: e2 };
        return this._prepInputSettingsForSharing(a2, true);
      } }, { key: "_updatePreloadCacheInputSettings", value: function(e2, t2) {
        var n2 = this._inputSettings || {}, r3 = {};
        if (e2.video) {
          var i3, o2, s2;
          if (r3.video = {}, e2.video.settings)
            r3.video.settings = {}, t2 || e2.video.settings.customTrack || null === (s2 = n2.video) || void 0 === s2 || !s2.settings ? r3.video.settings = e2.video.settings : r3.video.settings = qa(qa({}, n2.video.settings), e2.video.settings), Object.keys(r3.video.settings).length || delete r3.video.settings;
          else
            null !== (i3 = n2.video) && void 0 !== i3 && i3.settings && (r3.video.settings = n2.video.settings);
          e2.video.processor ? r3.video.processor = e2.video.processor : null !== (o2 = n2.video) && void 0 !== o2 && o2.processor && (r3.video.processor = n2.video.processor);
        } else
          n2.video && (r3.video = n2.video);
        if (e2.audio) {
          var a2, c3, l2;
          if (r3.audio = {}, e2.audio.settings)
            r3.audio.settings = {}, t2 || e2.audio.settings.customTrack || null === (l2 = n2.audio) || void 0 === l2 || !l2.settings ? r3.audio.settings = e2.audio.settings : r3.audio.settings = qa(qa({}, n2.audio.settings), e2.audio.settings), Object.keys(r3.audio.settings).length || delete r3.audio.settings;
          else
            null !== (a2 = n2.audio) && void 0 !== a2 && a2.settings && (r3.audio.settings = n2.audio.settings);
          e2.audio.processor ? r3.audio.processor = e2.audio.processor : null !== (c3 = n2.audio) && void 0 !== c3 && c3.processor && (r3.audio.processor = n2.audio.processor);
        } else
          n2.audio && (r3.audio = n2.audio);
        this._maybeUpdateInputSettings(r3);
      } }, { key: "_devicesFromInputSettings", value: function(e2) {
        var t2, n2, r3 = (null == e2 || null === (t2 = e2.video) || void 0 === t2 || null === (t2 = t2.settings) || void 0 === t2 ? void 0 : t2.deviceId) || null, i3 = (null == e2 || null === (n2 = e2.audio) || void 0 === n2 || null === (n2 = n2.settings) || void 0 === n2 ? void 0 : n2.deviceId) || null, o2 = this._preloadCache.outputDeviceId || null;
        return { camera: r3 ? { deviceId: r3 } : {}, mic: i3 ? { deviceId: i3 } : {}, speaker: o2 ? { deviceId: o2 } : {} };
      } }, { key: "updateInputSettings", value: (q2 = h(function* (e2) {
        var t2 = this;
        return gc(), _c(e2) ? e2.video || e2.audio ? (wc(e2, this.properties.dailyConfig, this._sharedTracks), this._callObjectMode && !this._callMachineInitialized ? (this._updatePreloadCacheInputSettings(e2, true), this._getInputSettings()) : new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine({ action: "update-input-settings", inputSettings: e2 }, function(i3) {
            if (i3.error)
              r3(i3.error);
            else {
              if (i3.returnPreloadCache)
                return t2._updatePreloadCacheInputSettings(e2, true), void n2(t2._getInputSettings());
              t2._maybeUpdateInputSettings(i3.inputSettings), n2(t2._prepInputSettingsForSharing(i3.inputSettings, true));
            }
          });
        })) : this._getInputSettings() : (console.error(Cc()), Promise.reject(Cc()));
      }), function(e2) {
        return q2.apply(this, arguments);
      }) }, { key: "setBandwidth", value: function(e2) {
        var t2 = e2.kbs, n2 = e2.trackConstraints;
        if (gc(), this._callMachineInitialized)
          return this.sendMessageToCallMachine({ action: "set-bandwidth", kbs: t2, trackConstraints: n2 }), this;
      } }, { key: "getDailyLang", value: function() {
        var e2 = this;
        if (gc(), this._callMachineInitialized)
          return new Promise(function(t2) {
            e2.sendMessageToCallMachine({ action: "get-daily-lang" }, function(e3) {
              delete e3.action, delete e3.callbackStamp, t2(e3);
            });
          });
      } }, { key: "setDailyLang", value: function(e2) {
        return gc(), this.sendMessageToCallMachine({ action: "set-daily-lang", lang: e2 }), this;
      } }, { key: "setProxyUrl", value: function(e2) {
        return this.sendMessageToCallMachine({ action: "set-proxy-url", proxyUrl: e2 }), this;
      } }, { key: "setIceConfig", value: function(e2) {
        return this.sendMessageToCallMachine({ action: "set-ice-config", iceConfig: e2 }), this;
      } }, { key: "meetingSessionSummary", value: function() {
        return [_i, wi].includes(this._callState) ? this._finalSummaryOfPrevSession : this._meetingSessionSummary;
      } }, { key: "getMeetingSession", value: ($2 = h(function* () {
        var e2 = this;
        return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"), lc(this._callState, "getMeetingSession()"), new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "get-meeting-session" }, function(e3) {
            delete e3.action, delete e3.callbackStamp, t2(e3);
          });
        });
      }), function() {
        return $2.apply(this, arguments);
      }) }, { key: "meetingSessionState", value: function() {
        return lc(this._callState, "meetingSessionState"), this._meetingSessionState;
      } }, { key: "setMeetingSessionData", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "replace";
        fc(this._callObjectMode, "setMeetingSessionData()"), lc(this._callState, "setMeetingSessionData");
        try {
          !function(e3, t3) {
            new Qs({ data: e3, mergeStrategy: t3 });
          }(e2, t2);
        } catch (e3) {
          throw console.error(e3), e3;
        }
        try {
          this.sendMessageToCallMachine({ action: "set-session-data", data: e2, mergeStrategy: t2 });
        } catch (e3) {
          throw new Error("Error setting meeting session data: ".concat(e3));
        }
      } }, { key: "setUserName", value: function(e2, t2) {
        var n2 = this;
        return this.properties.userName = e2, new Promise(function(r3) {
          n2.sendMessageToCallMachine({ action: "set-user-name", name: null != e2 ? e2 : "", thisMeetingOnly: ys() || !!t2 && !!t2.thisMeetingOnly }, function(e3) {
            delete e3.action, delete e3.callbackStamp, r3(e3);
          });
        });
      } }, { key: "setUserData", value: (J2 = h(function* (e2) {
        var t2 = this;
        try {
          yc(e2);
        } catch (e3) {
          throw console.error(e3), e3;
        }
        if (this.properties.userData = e2, this._callMachineInitialized)
          return new Promise(function(n2) {
            try {
              t2.sendMessageToCallMachine({ action: "set-user-data", userData: e2 }, function(e3) {
                delete e3.action, delete e3.callbackStamp, n2(e3);
              });
            } catch (e3) {
              throw new Error("Error setting user data: ".concat(e3));
            }
          });
      }), function(e2) {
        return J2.apply(this, arguments);
      }) }, { key: "validateAudioLevelInterval", value: function(e2) {
        if (e2 && (e2 < 100 || "number" != typeof e2))
          throw new Error("The interval must be a number greater than or equal to 100 milliseconds.");
      } }, { key: "startLocalAudioLevelObserver", value: function(e2) {
        var t2 = this;
        if ("undefined" == typeof AudioWorkletNode && !ys())
          throw new Error("startLocalAudioLevelObserver() is not supported on this browser");
        if (this.validateAudioLevelInterval(e2), this._callMachineInitialized)
          return this._isLocalAudioLevelObserverRunning = true, new Promise(function(n2, r3) {
            t2.sendMessageToCallMachine({ action: "start-local-audio-level-observer", interval: e2 }, function(e3) {
              t2._isLocalAudioLevelObserverRunning = !e3.error, e3.error ? r3({ error: e3.error }) : n2();
            });
          });
        this._preloadCache.localAudioLevelObserver = { enabled: true, interval: e2 };
      } }, { key: "isLocalAudioLevelObserverRunning", value: function() {
        return this._isLocalAudioLevelObserverRunning;
      } }, { key: "stopLocalAudioLevelObserver", value: function() {
        this._preloadCache.localAudioLevelObserver = null, this._localAudioLevel = 0, this._isLocalAudioLevelObserverRunning = false, this.sendMessageToCallMachine({ action: "stop-local-audio-level-observer" });
      } }, { key: "startRemoteParticipantsAudioLevelObserver", value: function(e2) {
        var t2 = this;
        if (this.validateAudioLevelInterval(e2), this._callMachineInitialized)
          return this._isRemoteParticipantsAudioLevelObserverRunning = true, new Promise(function(n2, r3) {
            t2.sendMessageToCallMachine({ action: "start-remote-participants-audio-level-observer", interval: e2 }, function(e3) {
              t2._isRemoteParticipantsAudioLevelObserverRunning = !e3.error, e3.error ? r3({ error: e3.error }) : n2();
            });
          });
        this._preloadCache.remoteParticipantsAudioLevelObserver = { enabled: true, interval: e2 };
      } }, { key: "isRemoteParticipantsAudioLevelObserverRunning", value: function() {
        return this._isRemoteParticipantsAudioLevelObserverRunning;
      } }, { key: "stopRemoteParticipantsAudioLevelObserver", value: function() {
        this._preloadCache.remoteParticipantsAudioLevelObserver = null, this._remoteParticipantsAudioLevel = {}, this._isRemoteParticipantsAudioLevelObserverRunning = false, this.sendMessageToCallMachine({ action: "stop-remote-participants-audio-level-observer" });
      } }, { key: "startCamera", value: (V2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        if (fc(this._callObjectMode, "startCamera()"), dc(this._callState, this._isPreparingToJoin, "startCamera()", "Did you mean to use setLocalAudio() and/or setLocalVideo() instead?"), this.needsLoad())
          try {
            yield this.load(t2);
          } catch (e3) {
            return Promise.reject(e3);
          }
        else {
          if (this._didPreAuth) {
            if (t2.url && t2.url !== this.properties.url)
              return console.error("url in startCamera() is different than the one used in preAuth()"), Promise.reject();
            if (t2.token && t2.token !== this.properties.token)
              return console.error("token in startCamera() is different than the one used in preAuth()"), Promise.reject();
          }
          this.validateProperties(t2), this.properties = qa(qa({}, this.properties), t2);
        }
        return new Promise(function(t3) {
          e2._preloadCache.inputSettings = e2._prepInputSettingsForSharing(e2._inputSettings, false), e2.sendMessageToCallMachine({ action: "start-camera", properties: cc(e2.properties, e2.callClientId), preloadCache: cc(e2._preloadCache, e2.callClientId) }, function(e3) {
            t3({ camera: e3.camera, mic: e3.mic, speaker: e3.speaker });
          });
        });
      }), function() {
        return V2.apply(this, arguments);
      }) }, { key: "validateCustomTrack", value: function(e2, t2, n2) {
        if (n2 && n2.length > 50)
          throw new Error("Custom track `trackName` must not be more than 50 characters");
        if (t2 && "music" !== t2 && "speech" !== t2 && !(t2 instanceof Object))
          throw new Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");
        if (!!n2 && ["cam-audio", "cam-video", "screen-video", "screen-audio", "rmpAudio", "rmpVideo", "customVideoDefaults"].includes(n2))
          throw new Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");
        if (!(e2 instanceof MediaStreamTrack))
          throw new Error("Custom tracks provided must be instances of MediaStreamTrack");
      } }, { key: "startCustomTrack", value: function() {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : { track, mode, trackName };
        return gc(), lc(this._callState, "startCustomTrack()"), this.validateCustomTrack(t2.track, t2.mode, t2.trackName), new Promise(function(n2, r3) {
          e2._sharedTracks.customTrack = t2.track, t2.track = ls, e2.sendMessageToCallMachine({ action: "start-custom-track", properties: t2 }, function(e3) {
            e3.error ? r3({ error: e3.error }) : n2(e3.mediaTag);
          });
        });
      } }, { key: "stopCustomTrack", value: function(e2) {
        var t2 = this;
        return gc(), lc(this._callState, "stopCustomTrack()"), new Promise(function(n2) {
          t2.sendMessageToCallMachine({ action: "stop-custom-track", mediaTag: e2 }, function(e3) {
            n2(e3.mediaTag);
          });
        });
      } }, { key: "setCamera", value: function(e2) {
        var t2 = this;
        return mc(), hc(this._callMachineInitialized, "setCamera()"), new Promise(function(n2) {
          t2.sendMessageToCallMachine({ action: "set-camera", cameraDeviceId: e2 }, function(e3) {
            n2({ device: e3.device });
          });
        });
      } }, { key: "setAudioDevice", value: (U2 = h(function* (e2) {
        return mc(), this.nativeUtils().setAudioDevice(e2), { deviceId: yield this.nativeUtils().getAudioDevice() };
      }), function(e2) {
        return U2.apply(this, arguments);
      }) }, { key: "cycleCamera", value: function() {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        return new Promise(function(n2) {
          e2.sendMessageToCallMachine({ action: "cycle-camera", properties: t2 }, function(e3) {
            n2({ device: e3.device });
          });
        });
      } }, { key: "cycleMic", value: function() {
        var e2 = this;
        return gc(), new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "cycle-mic" }, function(e3) {
            t2({ device: e3.device });
          });
        });
      } }, { key: "getCameraFacingMode", value: function() {
        var e2 = this;
        return mc(), new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "get-camera-facing-mode" }, function(e3) {
            t2(e3.facingMode);
          });
        });
      } }, { key: "setInputDevicesAsync", value: (B2 = h(function* (e2) {
        var t2 = this, n2 = e2.audioDeviceId, r3 = e2.videoDeviceId, i3 = e2.audioSource, o2 = e2.videoSource;
        if (gc(), void 0 !== i3 && (n2 = i3), void 0 !== o2 && (r3 = o2), "boolean" == typeof n2 && (this._setAllowLocalAudio(n2), n2 = void 0), "boolean" == typeof r3 && (this._setAllowLocalVideo(r3), r3 = void 0), !n2 && !r3)
          return yield this.getInputDevices();
        var s2 = {};
        return n2 && (n2 instanceof MediaStreamTrack ? (this._sharedTracks.audioTrack = n2, n2 = ls, s2.audio = { settings: { customTrack: n2 } }) : (delete this._sharedTracks.audioTrack, s2.audio = { settings: { deviceId: n2 } })), r3 && (r3 instanceof MediaStreamTrack ? (this._sharedTracks.videoTrack = r3, r3 = ls, s2.video = { settings: { customTrack: r3 } }) : (delete this._sharedTracks.videoTrack, s2.video = { settings: { deviceId: r3 } })), this._callObjectMode && this.needsLoad() ? (this._updatePreloadCacheInputSettings(s2, false), this._devicesFromInputSettings(this._inputSettings)) : new Promise(function(e3) {
          t2.sendMessageToCallMachine({ action: "set-input-devices", audioDeviceId: n2, videoDeviceId: r3 }, function(n3) {
            if (delete n3.action, delete n3.callbackStamp, n3.returnPreloadCache)
              return t2._updatePreloadCacheInputSettings(s2, false), void e3(t2._devicesFromInputSettings(t2._inputSettings));
            e3(n3);
          });
        });
      }), function(e2) {
        return B2.apply(this, arguments);
      }) }, { key: "setOutputDeviceAsync", value: (F2 = h(function* (e2) {
        var t2 = this, n2 = e2.outputDeviceId;
        return gc(), n2 && (this._preloadCache.outputDeviceId = n2), this._callObjectMode && this.needsLoad() ? this._devicesFromInputSettings(this._inputSettings) : new Promise(function(e3) {
          t2.sendMessageToCallMachine({ action: "set-output-device", outputDeviceId: n2 }, function(n3) {
            delete n3.action, delete n3.callbackStamp, n3.returnPreloadCache ? e3(t2._devicesFromInputSettings(t2._inputSettings)) : e3(n3);
          });
        });
      }), function(e2) {
        return F2.apply(this, arguments);
      }) }, { key: "getInputDevices", value: (R2 = h(function* () {
        var e2 = this;
        return this._callObjectMode && this.needsLoad() ? this._devicesFromInputSettings(this._inputSettings) : new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "get-input-devices" }, function(n2) {
            n2.returnPreloadCache ? t2(e2._devicesFromInputSettings(e2._inputSettings)) : t2({ camera: n2.camera, mic: n2.mic, speaker: n2.speaker });
          });
        });
      }), function() {
        return R2.apply(this, arguments);
      }) }, { key: "nativeInCallAudioMode", value: function() {
        return mc(), this._nativeInCallAudioMode;
      } }, { key: "setNativeInCallAudioMode", value: function(e2) {
        if (mc(), [Qa, Ka].includes(e2)) {
          if (e2 !== this._nativeInCallAudioMode)
            return this._nativeInCallAudioMode = e2, !this.disableReactNativeAutoDeviceManagement("audio") && uc(this._callState, this._isPreparingToJoin) && this.nativeUtils().setAudioMode(this._nativeInCallAudioMode), this;
        } else
          console.error("invalid in-call audio mode specified: ", e2);
      } }, { key: "preAuth", value: (D2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        if (fc(this._callObjectMode, "preAuth()"), dc(this._callState, this._isPreparingToJoin, "preAuth()"), this.needsLoad() && (yield this.load(t2)), !t2.url)
          throw new Error("preAuth() requires at least a url to be provided");
        return this.validateProperties(t2), this.properties = qa(qa({}, this.properties), t2), new Promise(function(t3, n2) {
          e2._preloadCache.inputSettings = e2._prepInputSettingsForSharing(e2._inputSettings, false), e2.sendMessageToCallMachine({ action: "daily-method-preauth", properties: cc(e2.properties, e2.callClientId), preloadCache: cc(e2._preloadCache, e2.callClientId) }, function(r3) {
            return r3.error ? n2(r3.error) : r3.access ? (e2._didPreAuth = true, void t3({ access: r3.access })) : n2(new Error("unknown error in preAuth()"));
          });
        });
      }), function() {
        return D2.apply(this, arguments);
      }) }, { key: "load", value: (L2 = h(function* (e2) {
        var t2 = this;
        if (this.needsLoad()) {
          if (this._destroyed && (this._logUseAfterDestroy(), this.strictMode))
            throw new Error("Use after destroy");
          if (e2 && (this.validateProperties(e2), this.properties = qa(qa({}, this.properties), e2)), !this._callObjectMode && !this.properties.url)
            throw new Error("can't load iframe meeting because url property isn't set");
          return this._updateCallState(gi), this.emitDailyJSEvent({ action: to }), this._callObjectMode ? new Promise(function(e3, n2) {
            t2._callObjectLoader.cancel();
            var r3 = Date.now();
            t2._callObjectLoader.load(t2.properties.dailyConfig, function(n3) {
              t2._bundleLoadTime = n3 ? "no-op" : Date.now() - r3, t2._updateCallState(mi), n3 && t2.emitDailyJSEvent({ action: ro }), e3();
            }, function(e4, r4) {
              if (t2.emitDailyJSEvent({ action: no }), !r4) {
                t2._updateCallState(wi), t2.resetMeetingDependentVars();
                var i3 = { action: ns, errorMsg: e4.msg, error: { type: "connection-error", msg: "Failed to load call object bundle.", details: { on: "load", sourceError: e4, bundleUrl: Z(t2.properties.dailyConfig) } } };
                t2._maybeSendToSentry(i3), t2.emitDailyJSEvent(i3), n2(e4.msg);
              }
            });
          }) : (this._iframe.src = X(this.assembleMeetingUrl(), this.properties.dailyConfig), new Promise(function(e3, n2) {
            t2._loadedCallback = function(r3) {
              t2._callState !== wi ? (t2._updateCallState(mi), (t2.properties.cssFile || t2.properties.cssText) && t2.loadCss(t2.properties), e3()) : n2(r3);
            };
          }));
        }
      }), function(e2) {
        return L2.apply(this, arguments);
      }) }, { key: "join", value: (I2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        this._testCallInProgress && this.stopTestCallQuality();
        var n2 = false;
        if (this.needsLoad()) {
          this.updateIsPreparingToJoin(true);
          try {
            yield this.load(t2);
          } catch (e3) {
            return this.updateIsPreparingToJoin(false), Promise.reject(e3);
          }
        } else {
          if (n2 = !(!this.properties.cssFile && !this.properties.cssText), this._didPreAuth) {
            if (t2.url && t2.url !== this.properties.url)
              return console.error("url in join() is different than the one used in preAuth()"), this.updateIsPreparingToJoin(false), Promise.reject();
            if (t2.token && t2.token !== this.properties.token)
              return console.error("token in join() is different than the one used in preAuth()"), this.updateIsPreparingToJoin(false), Promise.reject();
          }
          if (t2.url && !this._callObjectMode && t2.url && t2.url !== this.properties.url)
            return console.error("url in join() is different than the one used in load() (".concat(this.properties.url, " -> ").concat(t2.url, ")")), this.updateIsPreparingToJoin(false), Promise.reject();
          this.validateProperties(t2), this.properties = qa(qa({}, this.properties), t2);
        }
        return void 0 !== t2.showLocalVideo && (this._callObjectMode ? console.error("showLocalVideo is not available in callObject mode") : this._showLocalVideo = !!t2.showLocalVideo), void 0 !== t2.showParticipantsBar && (this._callObjectMode ? console.error("showParticipantsBar is not available in callObject mode") : this._showParticipantsBar = !!t2.showParticipantsBar), this._callState === bi || this._callState === yi ? (console.warn("already joined meeting, call leave() before joining again"), void this.updateIsPreparingToJoin(false)) : (this._updateCallState(yi, false), this.emitDailyJSEvent({ action: so }), this._preloadCache.inputSettings = this._prepInputSettingsForSharing(this._inputSettings || {}, false), this.sendMessageToCallMachine({ action: "join-meeting", properties: cc(this.properties, this.callClientId), preloadCache: cc(this._preloadCache, this.callClientId) }), new Promise(function(t3, r3) {
          e2._joinedCallback = function(i3, o2) {
            if (e2._callState !== wi) {
              if (e2._updateCallState(bi), i3)
                for (var s2 in i3) {
                  if (e2._callObjectMode) {
                    var a2 = e2._callMachine().store;
                    pa(i3[s2], a2), fa(i3[s2], a2), ga(i3[s2], e2._participants[s2], a2);
                  }
                  e2._participants[s2] = qa({}, i3[s2]), e2.toggleParticipantAudioBasedOnNativeAudioFocus();
                }
              n2 && e2.loadCss(e2.properties), t3(i3);
            } else
              r3(o2);
          };
        }));
      }), function() {
        return I2.apply(this, arguments);
      }) }, { key: "leave", value: (j2 = h(function* () {
        var e2 = this;
        return this._testCallInProgress && this.stopTestCallQuality(), new Promise(function(t2) {
          e2._callState === _i || e2._callState === wi ? t2() : e2._callObjectLoader && !e2._callObjectLoader.loaded ? (e2._callObjectLoader.cancel(), e2._updateCallState(_i), e2.resetMeetingDependentVars(), e2.emitDailyJSEvent({ action: _i }), t2()) : (e2._resolveLeave = t2, e2.sendMessageToCallMachine({ action: "leave-meeting" }));
        });
      }), function() {
        return j2.apply(this, arguments);
      }) }, { key: "startScreenShare", value: (A2 = h(function* () {
        var e2 = this, t2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        if (hc(this._callMachineInitialized, "startScreenShare()"), t2.screenVideoSendSettings && this._validateVideoSendSettings("screenVideo", t2.screenVideoSendSettings), t2.mediaStream && (this._sharedTracks.screenMediaStream = t2.mediaStream, t2.mediaStream = ls), "undefined" != typeof DailyNativeUtils && void 0 !== DailyNativeUtils.isIOS && DailyNativeUtils.isIOS) {
          var n2 = this.nativeUtils();
          if (yield n2.isScreenBeingCaptured())
            return void this.emitDailyJSEvent({ action: ts, type: "screen-share-error", errorMsg: "Could not start the screen sharing. The screen is already been captured!" });
          n2.setSystemScreenCaptureStartCallback(function() {
            n2.setSystemScreenCaptureStartCallback(null), e2.sendMessageToCallMachine({ action: ss, captureOptions: t2 });
          }), n2.presentSystemScreenCapturePrompt();
        } else
          this.sendMessageToCallMachine({ action: ss, captureOptions: t2 });
      }), function() {
        return A2.apply(this, arguments);
      }) }, { key: "stopScreenShare", value: function() {
        hc(this._callMachineInitialized, "stopScreenShare()"), this.sendMessageToCallMachine({ action: "local-screen-stop" });
      } }, { key: "startRecording", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}, t2 = e2.type;
        if (t2 && "cloud" !== t2 && "raw-tracks" !== t2 && "local" !== t2)
          throw new Error("invalid type: ".concat(t2, ", allowed values 'cloud', 'raw-tracks', or 'local'"));
        this.sendMessageToCallMachine(qa({ action: "local-recording-start" }, e2));
      } }, { key: "updateRecording", value: function(e2) {
        var t2 = e2.layout, n2 = void 0 === t2 ? { preset: "default" } : t2, r3 = e2.instanceId;
        this.sendMessageToCallMachine({ action: "daily-method-update-recording", layout: n2, instanceId: r3 });
      } }, { key: "stopRecording", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        this.sendMessageToCallMachine(qa({ action: "local-recording-stop" }, e2));
      } }, { key: "startLiveStreaming", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        this.sendMessageToCallMachine(qa({ action: "daily-method-start-live-streaming" }, e2));
      } }, { key: "updateLiveStreaming", value: function(e2) {
        var t2 = e2.layout, n2 = void 0 === t2 ? { preset: "default" } : t2, r3 = e2.instanceId;
        this.sendMessageToCallMachine({ action: "daily-method-update-live-streaming", layout: n2, instanceId: r3 });
      } }, { key: "addLiveStreamingEndpoints", value: function(e2) {
        var t2 = e2.endpoints, n2 = e2.instanceId;
        this.sendMessageToCallMachine({ action: as, endpointsOp: fs, endpoints: t2, instanceId: n2 });
      } }, { key: "removeLiveStreamingEndpoints", value: function(e2) {
        var t2 = e2.endpoints, n2 = e2.instanceId;
        this.sendMessageToCallMachine({ action: as, endpointsOp: vs, endpoints: t2, instanceId: n2 });
      } }, { key: "stopLiveStreaming", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        this.sendMessageToCallMachine(qa({ action: "daily-method-stop-live-streaming" }, e2));
      } }, { key: "validateDailyConfig", value: function(e2) {
        e2.camSimulcastEncodings && (console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."), this.validateSimulcastEncodings(e2.camSimulcastEncodings)), e2.screenSimulcastEncodings && console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings."), Ps() && e2.noAutoDefaultDeviceChange && console.warn("noAutoDefaultDeviceChange is not supported on Android, and will be ignored.");
      } }, { key: "validateSimulcastEncodings", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : null, n2 = arguments.length > 2 && void 0 !== arguments[2] && arguments[2];
        if (e2) {
          if (!(e2 instanceof Array || Array.isArray(e2)))
            throw new Error("encodings must be an Array");
          if (!xc(e2.length, 1, 3))
            throw new Error("encodings must be an Array with between 1 to ".concat(3, " layers"));
          for (var r3 = 0; r3 < e2.length; r3++) {
            var i3 = e2[r3];
            for (var o2 in this._validateEncodingLayerHasValidProperties(i3), i3)
              if (ec.includes(o2)) {
                if ("number" != typeof i3[o2])
                  throw new Error("".concat(o2, " must be a number"));
                if (t2) {
                  var s2 = t2[o2], a2 = s2.min, c3 = s2.max;
                  if (!xc(i3[o2], a2, c3))
                    throw new Error("".concat(o2, " value not in range. valid range: ").concat(a2, " to ").concat(c3));
                }
              } else if (!["active", "scalabilityMode"].includes(o2))
                throw new Error("Invalid key ".concat(o2, ", valid keys are:") + Object.values(ec));
            if (n2 && !i3.hasOwnProperty("maxBitrate"))
              throw new Error("maxBitrate is not specified");
          }
        }
      } }, { key: "startRemoteMediaPlayer", value: (P2 = h(function* (e2) {
        var t2 = this, n2 = e2.url, r3 = e2.settings, i3 = void 0 === r3 ? { state: hs.PLAY } : r3;
        try {
          !function(e3) {
            if ("string" != typeof e3)
              throw new Error('url parameter must be "string" type');
          }(n2), Ic(i3), function(e3) {
            for (var t3 in e3)
              if (!tc.includes(t3))
                throw new Error("Invalid key ".concat(t3, ", valid keys are: ").concat(tc));
            e3.simulcastEncodings && this.validateSimulcastEncodings(e3.simulcastEncodings, Za, true);
          }(i3);
        } catch (e3) {
          throw console.error("invalid argument Error: ".concat(e3)), console.error('startRemoteMediaPlayer arguments must be of the form:\n  { url: "playback url",\n  settings?:\n  {state: "play"|"pause", simulcastEncodings?: [{}] } }'), e3;
        }
        return new Promise(function(e3, r4) {
          t2.sendMessageToCallMachine({ action: "daily-method-start-remote-media-player", url: n2, settings: i3 }, function(t3) {
            t3.error ? r4({ error: t3.error, errorMsg: t3.errorMsg }) : e3({ session_id: t3.session_id, remoteMediaPlayerState: { state: t3.state, settings: t3.settings } });
          });
        });
      }), function(e2) {
        return P2.apply(this, arguments);
      }) }, { key: "stopRemoteMediaPlayer", value: (O2 = h(function* (e2) {
        var t2 = this;
        if ("string" != typeof e2)
          throw new Error(" remotePlayerID must be of type string");
        return new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine({ action: "daily-method-stop-remote-media-player", session_id: e2 }, function(e3) {
            e3.error ? r3({ error: e3.error, errorMsg: e3.errorMsg }) : n2();
          });
        });
      }), function(e2) {
        return O2.apply(this, arguments);
      }) }, { key: "updateRemoteMediaPlayer", value: (T2 = h(function* (e2) {
        var t2 = this, n2 = e2.session_id, r3 = e2.settings;
        try {
          Ic(r3);
        } catch (e3) {
          throw console.error("invalid argument Error: ".concat(e3)), console.error('updateRemoteMediaPlayer arguments must be of the form:\n  session_id: "participant session",\n  { settings?: {state: "play"|"pause"} }'), e3;
        }
        return new Promise(function(e3, i3) {
          t2.sendMessageToCallMachine({ action: "daily-method-update-remote-media-player", session_id: n2, settings: r3 }, function(t3) {
            t3.error ? i3({ error: t3.error, errorMsg: t3.errorMsg }) : e3({ session_id: t3.session_id, remoteMediaPlayerState: { state: t3.state, settings: t3.settings } });
          });
        });
      }), function(e2) {
        return T2.apply(this, arguments);
      }) }, { key: "startTranscription", value: function(e2) {
        lc(this._callState, "startTranscription()"), this.sendMessageToCallMachine(qa({ action: "daily-method-start-transcription" }, e2));
      } }, { key: "updateTranscription", value: function(e2) {
        if (lc(this._callState, "updateTranscription()"), !e2)
          throw new Error("updateTranscription Error: options is mandatory");
        if ("object" !== n(e2))
          throw new Error("updateTranscription Error: options must be object type");
        if (e2.participants && !Array.isArray(e2.participants))
          throw new Error("updateTranscription Error: participants must be an array");
        this.sendMessageToCallMachine(qa({ action: "daily-method-update-transcription" }, e2));
      } }, { key: "stopTranscription", value: function(e2) {
        if (lc(this._callState, "stopTranscription()"), e2 && "object" !== n(e2))
          throw new Error("stopTranscription Error: options must be object type");
        if (e2 && !e2.instanceId)
          throw new Error('"instanceId" not provided');
        this.sendMessageToCallMachine(qa({ action: "daily-method-stop-transcription" }, e2));
      } }, { key: "startDialOut", value: (E2 = h(function* (e2) {
        var t2 = this;
        lc(this._callState, "startDialOut()");
        var n2 = function(e3) {
          if (e3) {
            if (!Array.isArray(e3))
              throw new Error("Error starting dial out: audio codec must be an array");
            if (e3.length <= 0)
              throw new Error("Error starting dial out: audio codec array specified but empty");
            e3.forEach(function(e4) {
              if ("string" != typeof e4)
                throw new Error("Error starting dial out: audio codec must be a string");
              if ("OPUS" !== e4 && "PCMU" !== e4 && "PCMA" !== e4 && "G722" !== e4)
                throw new Error("Error starting dial out: audio codec must be one of OPUS, PCMU, PCMA, G722");
            });
          }
        };
        if (!e2.sipUri && !e2.phoneNumber)
          throw new Error("Error starting dial out: either a sip uri or phone number must be provided");
        if (e2.sipUri && e2.phoneNumber)
          throw new Error("Error starting dial out: only one of sip uri or phone number must be provided");
        if (e2.sipUri) {
          if ("string" != typeof e2.sipUri)
            throw new Error("Error starting dial out: sipUri must be a string");
          if (!e2.sipUri.startsWith("sip:"))
            throw new Error("Error starting dial out: Invalid SIP URI, must start with 'sip:'");
          if (e2.video && "boolean" != typeof e2.video)
            throw new Error("Error starting dial out: video must be a boolean value");
          !function(e3) {
            if (e3 && (n2(e3.audio), e3.video)) {
              if (!Array.isArray(e3.video))
                throw new Error("Error starting dial out: video codec must be an array");
              if (e3.video.length <= 0)
                throw new Error("Error starting dial out: video codec array specified but empty");
              e3.video.forEach(function(e4) {
                if ("string" != typeof e4)
                  throw new Error("Error starting dial out: video codec must be a string");
                if ("H264" !== e4 && "VP8" !== e4)
                  throw new Error("Error starting dial out: video codec must be H264 or VP8");
              });
            }
          }(e2.codecs);
        }
        if (e2.phoneNumber) {
          if ("string" != typeof e2.phoneNumber)
            throw new Error("Error starting dial out: phoneNumber must be a string");
          if (!/^\+\d{1,}$/.test(e2.phoneNumber))
            throw new Error("Error starting dial out: Invalid phone number, must be valid phone number as per E.164");
          e2.codecs && n2(e2.codecs.audio);
        }
        if (e2.callerId) {
          if ("string" != typeof e2.callerId)
            throw new Error("Error starting dial out: callerId must be a string");
          if (e2.sipUri)
            throw new Error("Error starting dial out: callerId not allowed with sipUri");
        }
        if (e2.displayName) {
          if ("string" != typeof e2.displayName)
            throw new Error("Error starting dial out: displayName must be a string");
          if (e2.displayName.length >= 200)
            throw new Error("Error starting dial out: displayName length must be less than 200");
        }
        if (e2.userId) {
          if ("string" != typeof e2.userId)
            throw new Error("Error starting dial out: userId must be a string");
          if (e2.userId.length > 36)
            throw new Error("Error starting dial out: userId length must be less than or equal to 36");
        }
        return new Promise(function(n3, r3) {
          t2.sendMessageToCallMachine(qa({ action: "dialout-start" }, e2), function(e3) {
            e3.error ? r3(e3.error) : n3(e3);
          });
        });
      }), function(e2) {
        return E2.apply(this, arguments);
      }) }, { key: "stopDialOut", value: function(e2) {
        var t2 = this;
        return lc(this._callState, "stopDialOut()"), new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine(qa({ action: "dialout-stop" }, e2), function(e3) {
            e3.error ? r3(e3.error) : n2(e3);
          });
        });
      } }, { key: "sipCallTransfer", value: (C2 = h(function* (e2) {
        var t2 = this;
        if (lc(this._callState, "sipCallTransfer()"), !e2)
          throw new Error("sipCallTransfer() requires a sessionId and toEndPoint");
        return e2.useSipRefer = false, jc(e2, "sipCallTransfer"), new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine(qa({ action: gs }, e2), function(e3) {
            e3.error ? r3(e3.error) : n2(e3);
          });
        });
      }), function(e2) {
        return C2.apply(this, arguments);
      }) }, { key: "sipRefer", value: (M2 = h(function* (e2) {
        var t2 = this;
        if (lc(this._callState, "sipRefer()"), !e2)
          throw new Error("sessionId and toEndPoint are mandatory parameter");
        return e2.useSipRefer = true, jc(e2, "sipRefer"), new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine(qa({ action: gs }, e2), function(e3) {
            e3.error ? r3(e3.error) : n2(e3);
          });
        });
      }), function(e2) {
        return M2.apply(this, arguments);
      }) }, { key: "sendDTMF", value: (k2 = h(function* (e2) {
        var t2 = this;
        return lc(this._callState, "sendDTMF()"), function(e3) {
          var t3 = e3.sessionId, n2 = e3.tones;
          if (!t3 || !n2)
            throw new Error("sessionId and tones are mandatory parameter");
          if ("string" != typeof t3 || "string" != typeof n2)
            throw new Error("sessionId and tones should be of string type");
          if (n2.length > 20)
            throw new Error("tones string must be upto 20 characters");
          var r3 = /[^0-9A-D*#]/g, i3 = n2.match(r3);
          if (i3 && i3[0])
            throw new Error("".concat(i3[0], " is not valid DTMF tone"));
        }(e2), new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine(qa({ action: "send-dtmf" }, e2), function(e3) {
            e3.error ? r3(e3.error) : n2(e3);
          });
        });
      }), function(e2) {
        return k2.apply(this, arguments);
      }) }, { key: "getNetworkStats", value: function() {
        var e2 = this;
        if (this._callState !== bi) {
          return Promise.resolve(qa({ stats: { latest: {} } }, this._network));
        }
        return new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "get-calc-stats" }, function(n2) {
            t2(qa(qa({}, e2._network), {}, { stats: n2.stats }));
          });
        });
      } }, { key: "testWebsocketConnectivity", value: (S2 = h(function* () {
        var e2 = this;
        if (pc(this._testCallInProgress, "testWebsocketConnectivity()"), this.needsLoad())
          try {
            yield this.load();
          } catch (e3) {
            return Promise.reject(e3);
          }
        return new Promise(function(t2, n2) {
          e2.sendMessageToCallMachine({ action: "test-websocket-connectivity" }, function(e3) {
            e3.error ? n2(e3.error) : t2(e3.results);
          });
        });
      }), function() {
        return S2.apply(this, arguments);
      }) }, { key: "abortTestWebsocketConnectivity", value: function() {
        this.sendMessageToCallMachine({ action: "abort-test-websocket-connectivity" });
      } }, { key: "_validateVideoTrackForNetworkTests", value: function(e2) {
        return e2 ? e2 instanceof MediaStreamTrack ? !!Pa(e2, { isLocalScreenVideo: false }) || (console.error("Video track is not playable. This test needs a live video track."), false) : (console.error("Video track needs to be of type `MediaStreamTrack`."), false) : (console.error("Missing video track. You must provide a video track in order to run this test."), false);
      } }, { key: "testCallQuality", value: (w2 = h(function* () {
        var t2 = this;
        gc(), fc(this._callObjectMode, "testCallQuality()"), hc(this._callMachineInitialized, "testCallQuality()", null, true), dc(this._callState, this._isPreparingToJoin, "testCallQuality()");
        var n2 = this._testCallAlreadyInProgress, r3 = function(e2) {
          n2 || (t2._testCallInProgress = e2);
        };
        if (r3(true), this.needsLoad())
          try {
            var i3 = this._callState;
            yield this.load(), this._callState = i3;
          } catch (e2) {
            return r3(false), Promise.reject(e2);
          }
        return new Promise(function(n3) {
          t2.sendMessageToCallMachine({ action: "test-call-quality", dailyJsVersion: t2.properties.dailyJsVersion }, function(i4) {
            var o2 = i4.results, s2 = o2.result, a2 = e(o2, Va);
            if ("failed" === s2) {
              var c3, l2 = qa({}, a2);
              null !== (c3 = a2.error) && void 0 !== c3 && c3.details ? (a2.error.details = JSON.parse(a2.error.details), l2.error = qa(qa({}, l2.error), {}, { details: qa({}, l2.error.details) }), l2.error.details.duringTest = "testCallQuality") : (l2.error = l2.error ? qa({}, l2.error) : {}, l2.error.details = { duringTest: "testCallQuality" }), t2._maybeSendToSentry(l2);
            }
            r3(false), n3(qa({ result: s2 }, a2));
          });
        });
      }), function() {
        return w2.apply(this, arguments);
      }) }, { key: "stopTestCallQuality", value: function() {
        this.sendMessageToCallMachine({ action: "stop-test-call-quality" });
      } }, { key: "testConnectionQuality", value: (_2 = h(function* (e2) {
        var t2;
        ys() ? (console.warn("testConnectionQuality() is deprecated: use testPeerToPeerCallQuality() instead"), t2 = yield this.testPeerToPeerCallQuality(e2)) : (console.warn("testConnectionQuality() is deprecated: use testCallQuality() instead"), t2 = yield this.testCallQuality());
        var n2 = { result: t2.result, secondsElapsed: t2.secondsElapsed };
        return t2.data && (n2.data = { maxRTT: t2.data.maxRoundTripTime, packetLoss: t2.data.avgRecvPacketLoss }), n2;
      }), function(e2) {
        return _2.apply(this, arguments);
      }) }, { key: "testPeerToPeerCallQuality", value: (b2 = h(function* (e2) {
        var t2 = this;
        if (pc(this._testCallInProgress, "testPeerToPeerCallQuality()"), this.needsLoad())
          try {
            yield this.load();
          } catch (e3) {
            return Promise.reject(e3);
          }
        var n2 = e2.videoTrack, r3 = e2.duration;
        if (!this._validateVideoTrackForNetworkTests(n2))
          throw new Error("Video track error");
        return this._sharedTracks.videoTrackForConnectionQualityTest = n2, new Promise(function(e3, n3) {
          t2.sendMessageToCallMachine({ action: "test-p2p-call-quality", duration: r3 }, function(t3) {
            t3.error ? n3(t3.error) : e3(t3.results);
          });
        });
      }), function(e2) {
        return b2.apply(this, arguments);
      }) }, { key: "stopTestConnectionQuality", value: function() {
        ys() ? (console.warn("stopTestConnectionQuality() is deprecated: use testPeerToPeerCallQuality() and stopTestPeerToPeerCallQuality() instead"), this.stopTestPeerToPeerCallQuality()) : (console.warn("stopTestConnectionQuality() is deprecated: use testCallQuality() and stopTestCallQuality() instead"), this.stopTestCallQuality());
      } }, { key: "stopTestPeerToPeerCallQuality", value: function() {
        this.sendMessageToCallMachine({ action: "stop-test-p2p-call-quality" });
      } }, { key: "testNetworkConnectivity", value: (y2 = h(function* (e2) {
        var t2 = this;
        if (pc(this._testCallInProgress, "testNetworkConnectivity()"), this.needsLoad())
          try {
            yield this.load();
          } catch (e3) {
            return Promise.reject(e3);
          }
        if (!this._validateVideoTrackForNetworkTests(e2))
          throw new Error("Video track error");
        return this._sharedTracks.videoTrackForNetworkConnectivityTest = e2, new Promise(function(e3, n2) {
          t2.sendMessageToCallMachine({ action: "test-network-connectivity" }, function(t3) {
            t3.error ? n2(t3.error) : e3(t3.results);
          });
        });
      }), function(e2) {
        return y2.apply(this, arguments);
      }) }, { key: "abortTestNetworkConnectivity", value: function() {
        this.sendMessageToCallMachine({ action: "abort-test-network-connectivity" });
      } }, { key: "getCpuLoadStats", value: function() {
        var e2 = this;
        return new Promise(function(t2) {
          if (e2._callState === bi) {
            e2.sendMessageToCallMachine({ action: "get-cpu-load-stats" }, function(e3) {
              t2(e3.cpuStats);
            });
          } else
            t2({ cpuLoadState: void 0, cpuLoadStateReason: void 0, stats: {} });
        });
      } }, { key: "_validateEncodingLayerHasValidProperties", value: function(e2) {
        var t2;
        if (!((null === (t2 = Object.keys(e2)) || void 0 === t2 ? void 0 : t2.length) > 0))
          throw new Error("Empty encoding is not allowed. At least one of these valid keys should be specified:" + Object.values(ec));
      } }, { key: "_validateVideoSendSettings", value: function(e2, t2) {
        var r3 = "screenVideo" === e2 ? ["default-screen-video", "detail-optimized", "motion-optimized", "motion-and-detail-balanced"] : ["default-video", "bandwidth-optimized", "bandwidth-and-quality-balanced", "quality-optimized", "adaptive-2-layers", "adaptive-3-layers"], i3 = "Video send settings should be either an object or one of the supported presets: ".concat(r3.join());
        if ("string" == typeof t2) {
          if (!r3.includes(t2))
            throw new Error(i3);
        } else {
          if ("object" !== n(t2))
            throw new Error(i3);
          if (!t2.maxQuality && !t2.encodings && void 0 === t2.allowAdaptiveLayers)
            throw new Error("Video send settings must contain at least maxQuality, allowAdaptiveLayers or encodings attribute");
          if (t2.maxQuality && -1 === ["low", "medium", "high"].indexOf(t2.maxQuality))
            throw new Error("maxQuality must be either low, medium or high");
          if (t2.encodings) {
            var o2 = false;
            switch (Object.keys(t2.encodings).length) {
              case 1:
                o2 = !t2.encodings.low;
                break;
              case 2:
                o2 = !t2.encodings.low || !t2.encodings.medium;
                break;
              case 3:
                o2 = !t2.encodings.low || !t2.encodings.medium || !t2.encodings.high;
                break;
              default:
                o2 = true;
            }
            if (o2)
              throw new Error("Encodings must be defined as: low, low and medium, or low, medium and high.");
            t2.encodings.low && this._validateEncodingLayerHasValidProperties(t2.encodings.low), t2.encodings.medium && this._validateEncodingLayerHasValidProperties(t2.encodings.medium), t2.encodings.high && this._validateEncodingLayerHasValidProperties(t2.encodings.high);
          }
        }
      } }, { key: "validateUpdateSendSettings", value: function(e2) {
        var t2 = this;
        if (!e2 || 0 === Object.keys(e2).length)
          throw new Error("Send settings must contain at least information for one track!");
        Object.entries(e2).forEach(function(e3) {
          var n2 = f(e3, 2), r3 = n2[0], i3 = n2[1];
          t2._validateVideoSendSettings(r3, i3);
        });
      } }, { key: "updateSendSettings", value: function(e2) {
        var t2 = this;
        return this.validateUpdateSendSettings(e2), this.needsLoad() ? (this._preloadCache.sendSettings = e2, { sendSettings: this._preloadCache.sendSettings }) : new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine({ action: "update-send-settings", sendSettings: e2 }, function(e3) {
            e3.error ? r3(e3.error) : n2(e3.sendSettings);
          });
        });
      } }, { key: "getSendSettings", value: function() {
        return this._sendSettings || this._preloadCache.sendSettings;
      } }, { key: "getLocalAudioLevel", value: function() {
        return this._localAudioLevel;
      } }, { key: "getRemoteParticipantsAudioLevel", value: function() {
        return this._remoteParticipantsAudioLevel;
      } }, { key: "getActiveSpeaker", value: function() {
        return gc(), this._activeSpeaker;
      } }, { key: "setActiveSpeakerMode", value: function(e2) {
        return gc(), this.sendMessageToCallMachine({ action: "set-active-speaker-mode", enabled: e2 }), this;
      } }, { key: "activeSpeakerMode", value: function() {
        return gc(), this._activeSpeakerMode;
      } }, { key: "subscribeToTracksAutomatically", value: function() {
        return this._preloadCache.subscribeToTracksAutomatically;
      } }, { key: "setSubscribeToTracksAutomatically", value: function(e2) {
        return lc(this._callState, "setSubscribeToTracksAutomatically()", "Use the subscribeToTracksAutomatically configuration property."), this._preloadCache.subscribeToTracksAutomatically = e2, this.sendMessageToCallMachine({ action: "daily-method-subscribe-to-tracks-automatically", enabled: e2 }), this;
      } }, { key: "enumerateDevices", value: (m2 = h(function* () {
        var e2 = this;
        if (this._callObjectMode) {
          var t2 = yield navigator.mediaDevices.enumerateDevices();
          return "Firefox" === js() && Is().major > 115 && Is().major < 123 && (t2 = t2.filter(function(e3) {
            return "audiooutput" !== e3.kind;
          })), { devices: t2.map(function(e3) {
            var t3 = JSON.parse(JSON.stringify(e3));
            if (!ys() && "videoinput" === e3.kind && e3.getCapabilities) {
              var n2, r3 = e3.getCapabilities();
              t3.facing = (null == r3 || null === (n2 = r3.facingMode) || void 0 === n2 ? void 0 : n2.length) >= 1 ? r3.facingMode[0] : void 0;
            }
            return t3;
          }) };
        }
        return new Promise(function(t3) {
          e2.sendMessageToCallMachine({ action: "enumerate-devices" }, function(e3) {
            t3({ devices: e3.devices });
          });
        });
      }), function() {
        return m2.apply(this, arguments);
      }) }, { key: "sendAppMessage", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : "*";
        if (lc(this._callState, "sendAppMessage()"), JSON.stringify(e2).length > this._maxAppMessageSize)
          throw new Error("Message data too large. Max size is " + this._maxAppMessageSize);
        return this.sendMessageToCallMachine({ action: "app-msg", data: e2, to: t2 }), this;
      } }, { key: "addFakeParticipant", value: function(e2) {
        return gc(), lc(this._callState, "addFakeParticipant()"), this.sendMessageToCallMachine(qa({ action: "add-fake-participant" }, e2)), this;
      } }, { key: "setShowNamesMode", value: function(e2) {
        return vc(this._callObjectMode, "setShowNamesMode()"), gc(), e2 && "always" !== e2 && "never" !== e2 ? (console.error('setShowNamesMode argument should be "always", "never", or false'), this) : (this.sendMessageToCallMachine({ action: "set-show-names", mode: e2 }), this);
      } }, { key: "setShowLocalVideo", value: function() {
        var e2 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
        return vc(this._callObjectMode, "setShowLocalVideo()"), gc(), lc(this._callState, "setShowLocalVideo()"), "boolean" != typeof e2 ? (console.error("setShowLocalVideo only accepts a boolean value"), this) : (this.sendMessageToCallMachine({ action: "set-show-local-video", show: e2 }), this._showLocalVideo = e2, this);
      } }, { key: "showLocalVideo", value: function() {
        return vc(this._callObjectMode, "showLocalVideo()"), gc(), this._showLocalVideo;
      } }, { key: "setShowParticipantsBar", value: function() {
        var e2 = !(arguments.length > 0 && void 0 !== arguments[0]) || arguments[0];
        return vc(this._callObjectMode, "setShowParticipantsBar()"), gc(), lc(this._callState, "setShowParticipantsBar()"), "boolean" != typeof e2 ? (console.error("setShowParticipantsBar only accepts a boolean value"), this) : (this.sendMessageToCallMachine({ action: "set-show-participants-bar", show: e2 }), this._showParticipantsBar = e2, this);
      } }, { key: "showParticipantsBar", value: function() {
        return vc(this._callObjectMode, "showParticipantsBar()"), gc(), this._showParticipantsBar;
      } }, { key: "customIntegrations", value: function() {
        return gc(), vc(this._callObjectMode, "customIntegrations()"), this._customIntegrations;
      } }, { key: "setCustomIntegrations", value: function(e2) {
        return gc(), vc(this._callObjectMode, "setCustomIntegrations()"), lc(this._callState, "setCustomIntegrations()"), Pc(e2) ? (this.sendMessageToCallMachine({ action: "set-custom-integrations", integrations: e2 }), this._customIntegrations = e2, this) : this;
      } }, { key: "startCustomIntegrations", value: function(e2) {
        var t2 = this;
        if (gc(), vc(this._callObjectMode, "startCustomIntegrations()"), lc(this._callState, "startCustomIntegrations()"), Array.isArray(e2) && e2.some(function(e3) {
          return "string" != typeof e3;
        }) || !Array.isArray(e2) && "string" != typeof e2)
          return console.error("startCustomIntegrations() only accepts string | string[]"), this;
        var n2 = "string" == typeof e2 ? [e2] : e2, r3 = n2.filter(function(e3) {
          return !(e3 in t2._customIntegrations);
        });
        return r3.length ? (console.error(`Can't find custom integration(s): "`.concat(r3.join(", "), '"')), this) : (this.sendMessageToCallMachine({ action: "start-custom-integrations", ids: n2 }), this);
      } }, { key: "stopCustomIntegrations", value: function(e2) {
        var t2 = this;
        if (gc(), vc(this._callObjectMode, "stopCustomIntegrations()"), lc(this._callState, "stopCustomIntegrations()"), Array.isArray(e2) && e2.some(function(e3) {
          return "string" != typeof e3;
        }) || !Array.isArray(e2) && "string" != typeof e2)
          return console.error("stopCustomIntegrations() only accepts string | string[]"), this;
        var n2 = "string" == typeof e2 ? [e2] : e2, r3 = n2.filter(function(e3) {
          return !(e3 in t2._customIntegrations);
        });
        return r3.length ? (console.error(`Can't find custom integration(s): "`.concat(r3.join(", "), '"')), this) : (this.sendMessageToCallMachine({ action: "stop-custom-integrations", ids: n2 }), this);
      } }, { key: "customTrayButtons", value: function() {
        return vc(this._callObjectMode, "customTrayButtons()"), gc(), this._customTrayButtons;
      } }, { key: "updateCustomTrayButtons", value: function(e2) {
        return vc(this._callObjectMode, "updateCustomTrayButtons()"), gc(), lc(this._callState, "updateCustomTrayButtons()"), Oc(e2) ? (this.sendMessageToCallMachine({ action: "update-custom-tray-buttons", btns: e2 }), this._customTrayButtons = e2, this) : (console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(rc))), this);
      } }, { key: "theme", value: function() {
        return vc(this._callObjectMode, "theme()"), this.properties.theme;
      } }, { key: "setTheme", value: function(e2) {
        var t2 = this;
        return vc(this._callObjectMode, "setTheme()"), new Promise(function(n2, r3) {
          try {
            t2.validateProperties({ theme: e2 }), t2.properties.theme = qa({}, e2), t2.sendMessageToCallMachine({ action: "set-theme", theme: t2.properties.theme });
            try {
              t2.emitDailyJSEvent({ action: eo, theme: t2.properties.theme });
            } catch (e3) {
              console.log("could not emit 'theme-updated'", e3);
            }
            n2(t2.properties.theme);
          } catch (e3) {
            r3(e3);
          }
        });
      } }, { key: "requestFullscreen", value: (g2 = h(function* () {
        if (gc(), this._iframe && !document.fullscreenElement && ws())
          try {
            (yield this._iframe.requestFullscreen) ? this._iframe.requestFullscreen() : this._iframe.webkitRequestFullscreen();
          } catch (e2) {
            console.log("could not make video call fullscreen", e2);
          }
      }), function() {
        return g2.apply(this, arguments);
      }) }, { key: "exitFullscreen", value: function() {
        gc(), document.fullscreenElement ? document.exitFullscreen() : document.webkitFullscreenElement && document.webkitExitFullscreen();
      } }, { key: "getSidebarView", value: (v2 = h(function* () {
        var e2 = this;
        return this._callObjectMode ? (console.error("getSidebarView is not available in callObject mode"), Promise.resolve(null)) : new Promise(function(t2) {
          e2.sendMessageToCallMachine({ action: "get-sidebar-view" }, function(e3) {
            t2(e3.view);
          });
        });
      }), function() {
        return v2.apply(this, arguments);
      }) }, { key: "setSidebarView", value: function(e2) {
        return this._callObjectMode ? (console.error("setSidebarView is not available in callObject mode"), this) : (this.sendMessageToCallMachine({ action: "set-sidebar-view", view: e2 }), this);
      } }, { key: "room", value: (p2 = h(function* () {
        var e2 = this, t2 = (arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {}).includeRoomConfigDefaults, n2 = void 0 === t2 || t2;
        return this._accessState.access === Oi || this.needsLoad() ? this.properties.url ? { roomUrlPendingJoin: this.properties.url } : null : new Promise(function(t3) {
          e2.sendMessageToCallMachine({ action: "lib-room-info", includeRoomConfigDefaults: n2 }, function(e3) {
            delete e3.action, delete e3.callbackStamp, t3(e3);
          });
        });
      }), function() {
        return p2.apply(this, arguments);
      }) }, { key: "geo", value: (d2 = h(function* () {
        try {
          var e2 = yield fetch("https://gs.daily.co/_ks_/x-swsl/:");
          return { current: (yield e2.json()).geo };
        } catch (e3) {
          return console.error("geo lookup failed", e3), { current: "" };
        }
      }), function() {
        return d2.apply(this, arguments);
      }) }, { key: "setNetworkTopology", value: (c2 = h(function* (e2) {
        var t2 = this;
        return gc(), lc(this._callState, "setNetworkTopology()"), new Promise(function(n2, r3) {
          t2.sendMessageToCallMachine({ action: "set-network-topology", opts: e2 }, function(e3) {
            e3.error ? r3({ error: e3.error }) : n2({ workerId: e3.workerId });
          });
        });
      }), function(e2) {
        return c2.apply(this, arguments);
      }) }, { key: "getNetworkTopology", value: (i2 = h(function* () {
        var e2 = this;
        return new Promise(function(t2, n2) {
          e2.needsLoad() && t2({ topology: "none" }), e2.sendMessageToCallMachine({ action: "get-network-topology" }, function(e3) {
            e3.error ? n2({ error: e3.error }) : t2({ topology: e3.topology });
          });
        });
      }), function() {
        return i2.apply(this, arguments);
      }) }, { key: "setPlayNewParticipantSound", value: function(e2) {
        if (gc(), "number" != typeof e2 && true !== e2 && false !== e2)
          throw new Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(e2));
        this.sendMessageToCallMachine({ action: "daily-method-set-play-ding", arg: e2 });
      } }, { key: "on", value: function(e2, t2) {
        return x.prototype.on.call(this, e2, t2);
      } }, { key: "once", value: function(e2, t2) {
        return x.prototype.once.call(this, e2, t2);
      } }, { key: "off", value: function(e2, t2) {
        return x.prototype.off.call(this, e2, t2);
      } }, { key: "validateProperties", value: function(e2) {
        var t2, n2;
        if (null != e2 && null !== (t2 = e2.dailyConfig) && void 0 !== t2 && t2.userMediaAudioConstraints) {
          var r3, i3;
          ys() || console.warn("userMediaAudioConstraints is deprecated. You can override constraints with inputSettings.audio.settings, found in DailyCallOptions.");
          var o2 = e2.inputSettings || {};
          o2.audio = (null === (r3 = e2.inputSettings) || void 0 === r3 ? void 0 : r3.audio) || {}, o2.audio.settings = (null === (i3 = e2.inputSettings) || void 0 === i3 || null === (i3 = i3.audio) || void 0 === i3 ? void 0 : i3.settings) || {}, o2.audio.settings = qa(qa({}, o2.audio.settings), e2.dailyConfig.userMediaAudioConstraints), e2.inputSettings = o2, delete e2.dailyConfig.userMediaAudioConstraints;
        }
        if (null != e2 && null !== (n2 = e2.dailyConfig) && void 0 !== n2 && n2.userMediaVideoConstraints) {
          var s2, a2;
          ys() || console.warn("userMediaVideoConstraints is deprecated. You can override constraints with inputSettings.video.settings, found in DailyCallOptions.");
          var c3 = e2.inputSettings || {};
          c3.video = (null === (s2 = e2.inputSettings) || void 0 === s2 ? void 0 : s2.video) || {}, c3.video.settings = (null === (a2 = e2.inputSettings) || void 0 === a2 || null === (a2 = a2.video) || void 0 === a2 ? void 0 : a2.settings) || {}, c3.video.settings = qa(qa({}, c3.video.settings), e2.dailyConfig.userMediaVideoConstraints), e2.inputSettings = c3, delete e2.dailyConfig.userMediaVideoConstraints;
        }
        for (var l2 in e2) {
          if (!oc[l2])
            throw new Error("unrecognized property '".concat(l2, "'"));
          if (oc[l2].validate && !oc[l2].validate(e2[l2], this))
            throw new Error("property '".concat(l2, "': ").concat(oc[l2].help));
        }
      } }, { key: "assembleMeetingUrl", value: function() {
        var e2, t2, n2 = qa(qa({}, this.properties), {}, { emb: this.callClientId, embHref: encodeURIComponent(window.location.href), proxy: null !== (e2 = this.properties.dailyConfig) && void 0 !== e2 && e2.proxyUrl ? encodeURIComponent(null === (t2 = this.properties.dailyConfig) || void 0 === t2 ? void 0 : t2.proxyUrl) : void 0 }), r3 = n2.url.match(/\?/) ? "&" : "?";
        return n2.url + r3 + Object.keys(oc).filter(function(e3) {
          return oc[e3].queryString && void 0 !== n2[e3];
        }).map(function(e3) {
          return "".concat(oc[e3].queryString, "=").concat(n2[e3]);
        }).join("&");
      } }, { key: "needsLoad", value: function() {
        return [vi, gi, _i, wi].includes(this._callState);
      } }, { key: "sendMessageToCallMachine", value: function(e2, t2) {
        if (this._destroyed && (this._logUseAfterDestroy(), this.strictMode))
          throw new Error("Use after destroy");
        this._messageChannel.sendMessageToCallMachine(e2, t2, this.callClientId, this._iframe);
      } }, { key: "forwardPackagedMessageToCallMachine", value: function(e2) {
        this._messageChannel.forwardPackagedMessageToCallMachine(e2, this._iframe, this.callClientId);
      } }, { key: "addListenerForPackagedMessagesFromCallMachine", value: function(e2) {
        return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(e2, this.callClientId);
      } }, { key: "removeListenerForPackagedMessagesFromCallMachine", value: function(e2) {
        this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(e2);
      } }, { key: "handleMessageFromCallMachine", value: function(t2) {
        switch (t2.action) {
          case Xi:
            this.sendMessageToCallMachine(qa({ action: Zi }, this.properties));
            break;
          case "call-machine-initialized":
            this._callMachineInitialized = true;
            var n2 = { action: cs, level: "log", code: 1011, stats: { event: "bundle load", time: "no-op" === this._bundleLoadTime ? 0 : this._bundleLoadTime, preLoaded: "no-op" === this._bundleLoadTime, url: Z(this.properties.dailyConfig) } };
            this.sendMessageToCallMachine(n2), this._delayDuplicateInstanceLog && this._logDuplicateInstanceAttempt();
            break;
          case ro:
            this._loadedCallback && (this._loadedCallback(), this._loadedCallback = null), this.emitDailyJSEvent(t2);
            break;
          case ao:
            var r3, i3 = qa({}, t2);
            delete i3.internal, this._maxAppMessageSize = (null === (r3 = t2.internal) || void 0 === r3 ? void 0 : r3._maxAppMessageSize) || rs, this._joinedCallback && (this._joinedCallback(t2.participants), this._joinedCallback = null), this.emitDailyJSEvent(i3);
            break;
          case lo:
          case uo:
            if (this._callState === _i)
              return;
            if (t2.participant && t2.participant.session_id) {
              var o2 = t2.participant.local ? "local" : t2.participant.session_id;
              if (this._callObjectMode) {
                var s2 = this._callMachine().store;
                pa(t2.participant, s2), fa(t2.participant, s2), ga(t2.participant, this._participants[o2], s2);
              }
              try {
                this.maybeParticipantTracksStopped(this._participants[o2], t2.participant), this.maybeParticipantTracksStarted(this._participants[o2], t2.participant), this.maybeEventRecordingStopped(this._participants[o2], t2.participant), this.maybeEventRecordingStarted(this._participants[o2], t2.participant);
              } catch (e2) {
                console.error("track events error", e2);
              }
              this.compareEqualForParticipantUpdateEvent(t2.participant, this._participants[o2]) || (this._participants[o2] = qa({}, t2.participant), this.toggleParticipantAudioBasedOnNativeAudioFocus(), this.emitDailyJSEvent(t2));
            }
            break;
          case ho:
            if (t2.participant && t2.participant.session_id) {
              var a2 = this._participants[t2.participant.session_id];
              a2 && this.maybeParticipantTracksStopped(a2, null), delete this._participants[t2.participant.session_id], this.emitDailyJSEvent(t2);
            }
            break;
          case po:
            N(this._participantCounts, t2.participantCounts) || (this._participantCounts = t2.participantCounts, this.emitDailyJSEvent(t2));
            break;
          case fo:
            var c3 = { access: t2.access };
            t2.awaitingAccess && (c3.awaitingAccess = t2.awaitingAccess), N(this._accessState, c3) || (this._accessState = c3, this.emitDailyJSEvent(t2));
            break;
          case vo:
            if (t2.meetingSession) {
              this._meetingSessionSummary = t2.meetingSession, this.emitDailyJSEvent(t2);
              var l2 = qa(qa({}, t2), {}, { action: "meeting-session-updated" });
              this.emitDailyJSEvent(l2);
            }
            break;
          case ns:
            var u2;
            this._iframe && !t2.preserveIframe && (this._iframe.src = ""), this._updateCallState(wi), this.resetMeetingDependentVars(), this._loadedCallback && (this._loadedCallback(t2.errorMsg), this._loadedCallback = null), t2.preserveIframe;
            var d3 = e(t2, Ja);
            null != d3 && null !== (u2 = d3.error) && void 0 !== u2 && u2.details && (d3.error.details = JSON.parse(d3.error.details)), this._maybeSendToSentry(t2), this._joinedCallback && (this._joinedCallback(null, d3), this._joinedCallback = null), this.emitDailyJSEvent(d3);
            break;
          case co:
            this._callState !== wi && this._updateCallState(_i), this.resetMeetingDependentVars(), this._resolveLeave && (this._resolveLeave(), this._resolveLeave = null), this.emitDailyJSEvent(t2);
            break;
          case "selected-devices-updated":
            t2.devices && this.emitDailyJSEvent(t2);
            break;
          case Jo:
            var h2 = t2.state, p3 = t2.threshold, f2 = t2.quality, v3 = h2.state, g3 = h2.reasons;
            v3 === this._network.networkState && N(g3, this._network.networkStateReasons) && p3 === this._network.threshold && f2 === this._network.quality || (this._network.networkState = v3, this._network.networkStateReasons = g3, this._network.quality = f2, this._network.threshold = p3, t2.networkState = v3, g3.length && (t2.networkStateReasons = g3), delete t2.state, this.emitDailyJSEvent(t2));
            break;
          case qo:
            t2 && t2.cpuLoadState && this.emitDailyJSEvent(t2);
            break;
          case zo:
            t2 && void 0 !== t2.faceCounts && this.emitDailyJSEvent(t2);
            break;
          case Uo:
            var m3 = t2.activeSpeaker;
            this._activeSpeaker.peerId !== m3.peerId && (this._activeSpeaker.peerId = m3.peerId, this.emitDailyJSEvent({ action: t2.action, activeSpeaker: this._activeSpeaker }));
            break;
          case "show-local-video-changed":
            if (this._callObjectMode)
              return;
            var y3 = t2.show;
            this._showLocalVideo = y3, this.emitDailyJSEvent({ action: t2.action, show: y3 });
            break;
          case Vo:
            var b3 = t2.enabled;
            this._activeSpeakerMode !== b3 && (this._activeSpeakerMode = b3, this.emitDailyJSEvent({ action: t2.action, enabled: this._activeSpeakerMode }));
            break;
          case yo:
          case bo:
          case _o:
            this._waitingParticipants = t2.allWaitingParticipants, this.emitDailyJSEvent({ action: t2.action, participant: t2.participant });
            break;
          case Zo:
            N(this._receiveSettings, t2.receiveSettings) || (this._receiveSettings = t2.receiveSettings, this.emitDailyJSEvent({ action: t2.action, receiveSettings: t2.receiveSettings }));
            break;
          case es:
            this._maybeUpdateInputSettings(t2.inputSettings);
            break;
          case "send-settings-updated":
            N(this._sendSettings, t2.sendSettings) || (this._sendSettings = t2.sendSettings, this._preloadCache.sendSettings = null, this.emitDailyJSEvent({ action: t2.action, sendSettings: t2.sendSettings }));
            break;
          case "local-audio-level":
            this._localAudioLevel = t2.audioLevel, this._preloadCache.localAudioLevelObserver = null, this.emitDailyJSEvent(t2);
            break;
          case "remote-participants-audio-level":
            this._remoteParticipantsAudioLevel = t2.participantsAudioLevel, this._preloadCache.remoteParticipantsAudioLevelObserver = null, this.emitDailyJSEvent(t2);
            break;
          case Lo:
            var _3 = t2.session_id;
            this._rmpPlayerState[_3] = t2.playerState, this.emitDailyJSEvent(t2);
            break;
          case No:
            delete this._rmpPlayerState[t2.session_id], this.emitDailyJSEvent(t2);
            break;
          case Do:
            var w3 = t2.session_id, S3 = this._rmpPlayerState[w3];
            S3 && this.compareEqualForRMPUpdateEvent(S3, t2.remoteMediaPlayerState) || (this._rmpPlayerState[w3] = t2.remoteMediaPlayerState, this.emitDailyJSEvent(t2));
            break;
          case "custom-button-click":
          case "sidebar-view-changed":
            this.emitDailyJSEvent(t2);
            break;
          case go:
            var k3 = this._meetingSessionState.topology !== (t2.meetingSessionState && t2.meetingSessionState.topology);
            this._meetingSessionState = Lc(t2.meetingSessionState, this._callObjectMode), (this._callObjectMode || k3) && this.emitDailyJSEvent(t2);
            break;
          case Ro:
            this._isScreenSharing = true, this.emitDailyJSEvent(t2);
            break;
          case Fo:
          case Bo:
            this._isScreenSharing = false, this.emitDailyJSEvent(t2);
            break;
          case Eo:
          case To:
          case Oo:
          case Po:
          case Ao:
          case ko:
          case Mo:
          case Co:
          case io:
          case oo:
          case Io:
          case xo:
          case "test-completed":
          case $o:
          case jo:
          case Go:
          case Qo:
          case Ko:
          case Yo:
          case ts:
          case Xo:
          case "dialin-ready":
          case "dialin-connected":
          case "dialin-error":
          case "dialin-stopped":
          case "dialin-warning":
          case "dialout-connected":
          case "dialout-answered":
          case "dialout-error":
          case "dialout-stopped":
          case "dialout-warning":
            this.emitDailyJSEvent(t2);
            break;
          case "request-fullscreen":
            this.requestFullscreen();
            break;
          case "request-exit-fullscreen":
            this.exitFullscreen();
        }
      } }, { key: "maybeEventRecordingStopped", value: function(e2, t2) {
        var n2 = "record";
        e2 && (t2.local || false !== t2[n2] || e2[n2] === t2[n2] || this.emitDailyJSEvent({ action: To }));
      } }, { key: "maybeEventRecordingStarted", value: function(e2, t2) {
        var n2 = "record";
        e2 && (t2.local || true !== t2[n2] || e2[n2] === t2[n2] || this.emitDailyJSEvent({ action: Eo }));
      } }, { key: "_trackStatePlayable", value: function(e2) {
        return !(!e2 || e2.state !== Ti);
      } }, { key: "_trackChanged", value: function(e2, t2) {
        return !((null == e2 ? void 0 : e2.id) === (null == t2 ? void 0 : t2.id));
      } }, { key: "maybeEventTrackStopped", value: function(e2, t2, n2) {
        var r3, i3, o2 = null !== (r3 = null == t2 ? void 0 : t2.tracks[e2]) && void 0 !== r3 ? r3 : null, s2 = null !== (i3 = null == n2 ? void 0 : n2.tracks[e2]) && void 0 !== i3 ? i3 : null, a2 = null == o2 ? void 0 : o2.track;
        if (a2) {
          var c3 = this._trackStatePlayable(o2), l2 = this._trackStatePlayable(s2), u2 = this._trackChanged(a2, null == s2 ? void 0 : s2.track);
          c3 && (l2 && !u2 || this.emitDailyJSEvent({ action: So, track: a2, participant: null != n2 ? n2 : t2, type: e2 }));
        }
      } }, { key: "maybeEventTrackStarted", value: function(e2, t2, n2) {
        var r3, i3, o2 = null !== (r3 = null == t2 ? void 0 : t2.tracks[e2]) && void 0 !== r3 ? r3 : null, s2 = null !== (i3 = null == n2 ? void 0 : n2.tracks[e2]) && void 0 !== i3 ? i3 : null, a2 = null == s2 ? void 0 : s2.track;
        if (a2) {
          var c3 = this._trackStatePlayable(o2), l2 = this._trackStatePlayable(s2), u2 = this._trackChanged(null == o2 ? void 0 : o2.track, a2);
          l2 && (c3 && !u2 || this.emitDailyJSEvent({ action: wo, track: a2, participant: n2, type: e2 }));
        }
      } }, { key: "maybeParticipantTracksStopped", value: function(e2, t2) {
        if (e2)
          for (var n2 in e2.tracks)
            this.maybeEventTrackStopped(n2, e2, t2);
      } }, { key: "maybeParticipantTracksStarted", value: function(e2, t2) {
        if (t2)
          for (var n2 in t2.tracks)
            this.maybeEventTrackStarted(n2, e2, t2);
      } }, { key: "compareEqualForRMPUpdateEvent", value: function(e2, t2) {
        var n2, r3;
        return e2.state === t2.state && (null === (n2 = e2.settings) || void 0 === n2 ? void 0 : n2.volume) === (null === (r3 = t2.settings) || void 0 === r3 ? void 0 : r3.volume);
      } }, { key: "emitDailyJSEvent", value: function(e2) {
        try {
          e2.callClientId = this.callClientId, this.emit(e2.action, e2);
        } catch (t2) {
          console.log("could not emit", e2, t2);
        }
      } }, { key: "compareEqualForParticipantUpdateEvent", value: function(e2, t2) {
        return !!N(e2, t2) && ((!e2.videoTrack || !t2.videoTrack || e2.videoTrack.id === t2.videoTrack.id && e2.videoTrack.muted === t2.videoTrack.muted && e2.videoTrack.enabled === t2.videoTrack.enabled) && (!e2.audioTrack || !t2.audioTrack || e2.audioTrack.id === t2.audioTrack.id && e2.audioTrack.muted === t2.audioTrack.muted && e2.audioTrack.enabled === t2.audioTrack.enabled));
      } }, { key: "nativeUtils", value: function() {
        return ys() ? "undefined" == typeof DailyNativeUtils ? (console.warn("in React Native, DailyNativeUtils is expected to be available"), null) : DailyNativeUtils : null;
      } }, { key: "updateIsPreparingToJoin", value: function(e2) {
        this._updateCallState(this._callState, e2);
      } }, { key: "_updateCallState", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : this._isPreparingToJoin;
        if (e2 !== this._callState || t2 !== this._isPreparingToJoin) {
          var n2 = this._callState, r3 = this._isPreparingToJoin;
          this._callState = e2, this._isPreparingToJoin = t2;
          var i3 = this._callState === bi;
          this.updateShowAndroidOngoingMeetingNotification(i3);
          var o2 = uc(n2, r3), s2 = uc(this._callState, this._isPreparingToJoin);
          o2 !== s2 && (this.updateKeepDeviceAwake(s2), this.updateDeviceAudioMode(s2), this.updateNoOpRecordingEnsuringBackgroundContinuity(s2));
        }
      } }, { key: "resetMeetingDependentVars", value: function() {
        this._participants = {}, this._participantCounts = Xa, this._waitingParticipants = {}, this._activeSpeaker = {}, this._activeSpeakerMode = false, this._didPreAuth = false, this._accessState = { access: Oi }, this._finalSummaryOfPrevSession = this._meetingSessionSummary, this._meetingSessionSummary = {}, this._meetingSessionState = Lc(Ya, this._callObjectMode), this._isScreenSharing = false, this._receiveSettings = {}, this._inputSettings = void 0, this._sendSettings = {}, this._localAudioLevel = 0, this._isLocalAudioLevelObserverRunning = false, this._remoteParticipantsAudioLevel = {}, this._isRemoteParticipantsAudioLevelObserverRunning = false, this._maxAppMessageSize = rs, this._callMachineInitialized = false, this._bundleLoadTime = void 0, this._preloadCache;
      } }, { key: "updateKeepDeviceAwake", value: function(e2) {
        ys() && this.nativeUtils().setKeepDeviceAwake(e2, this.callClientId);
      } }, { key: "updateDeviceAudioMode", value: function(e2) {
        if (ys() && !this.disableReactNativeAutoDeviceManagement("audio")) {
          var t2 = e2 ? this._nativeInCallAudioMode : "idle";
          this.nativeUtils().setAudioMode(t2);
        }
      } }, { key: "updateShowAndroidOngoingMeetingNotification", value: function(e2) {
        if (ys() && this.nativeUtils().setShowOngoingMeetingNotification) {
          var t2, n2, r3, i3;
          if (this.properties.reactNativeConfig && this.properties.reactNativeConfig.androidInCallNotification) {
            var o2 = this.properties.reactNativeConfig.androidInCallNotification;
            t2 = o2.title, n2 = o2.subtitle, r3 = o2.iconName, i3 = o2.disableForCustomOverride;
          }
          i3 && (e2 = false), this.nativeUtils().setShowOngoingMeetingNotification(e2, t2, n2, r3, this.callClientId);
        }
      } }, { key: "updateNoOpRecordingEnsuringBackgroundContinuity", value: function(e2) {
        ys() && this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity && this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(e2);
      } }, { key: "toggleParticipantAudioBasedOnNativeAudioFocus", value: function() {
        var e2;
        if (ys()) {
          var t2 = null === (e2 = this._callMachine()) || void 0 === e2 || null === (e2 = e2.store) || void 0 === e2 ? void 0 : e2.getState();
          for (var n2 in null == t2 ? void 0 : t2.streams) {
            var r3 = t2.streams[n2];
            r3 && r3.pendingTrack && "audio" === r3.pendingTrack.kind && (r3.pendingTrack.enabled = this._hasNativeAudioFocus);
          }
        }
      } }, { key: "disableReactNativeAutoDeviceManagement", value: function(e2) {
        return this.properties.reactNativeConfig && this.properties.reactNativeConfig.disableAutoDeviceManagement && this.properties.reactNativeConfig.disableAutoDeviceManagement[e2];
      } }, { key: "absoluteUrl", value: function(e2) {
        if (void 0 !== e2) {
          var t2 = document.createElement("a");
          return t2.href = e2, t2.href;
        }
      } }, { key: "sayHello", value: function() {
        var e2 = "hello, world.";
        return console.log(e2), e2;
      } }, { key: "_logUseAfterDestroy", value: function() {
        var e2 = Object.values(Ga)[0];
        if (this.needsLoad()) {
          if (e2 && !e2.needsLoad()) {
            var t2 = { action: cs, level: "error", code: this.strictMode ? 9995 : 9997 };
            e2.sendMessageToCallMachine(t2);
          } else if (!this.strictMode) {
            console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.");
          }
        } else {
          var n2 = { action: cs, level: "error", code: this.strictMode ? 9995 : 9997 };
          this._messageChannel.sendMessageToCallMachine(n2, null, this.callClientId, this._iframe);
        }
      } }, { key: "_logDuplicateInstanceAttempt", value: function() {
        for (var e2 = 0, t2 = Object.values(Ga); e2 < t2.length; e2++) {
          var n2 = t2[e2];
          n2._callMachineInitialized ? (n2.sendMessageToCallMachine({ action: cs, level: "warn", code: this.allowMultipleCallInstances ? 9993 : 9992 }), n2._delayDuplicateInstanceLog = false) : n2._delayDuplicateInstanceLog = true;
        }
      } }, { key: "_maybeSendToSentry", value: function(e2) {
        var t2, n2, i3, o2;
        if (null !== (t2 = e2.error) && void 0 !== t2 && t2.type) {
          if (![$i, Vi, Bi].includes(e2.error.type))
            return;
          if (e2.error.type === Bi && e2.error.msg.includes("deleted"))
            return;
        }
        var s2 = null !== (n2 = this.properties) && void 0 !== n2 && n2.url ? new URL(this.properties.url) : void 0, a2 = "production";
        s2 && s2.host.includes(".staging.daily") && (a2 = "staging");
        var c3, l2, u2, d3, h2, p3 = function(e3) {
          const t3 = [Gn(), Wn(), ii(), ni(), ui(), fi(), rr(), pi()];
          return false !== e3.autoSessionTracking && t3.push(li()), t3;
        }({}).filter(function(e3) {
          return !["BrowserApiErrors", "Breadcrumbs", "GlobalHandlers"].includes(e3.name);
        }), f2 = new jr({ dsn: "https://<EMAIL>/168844", transport: Wr, stackParser: Zr, integrations: p3, environment: a2 }), v3 = new Mt();
        if (v3.setClient(f2), f2.init(), this.session_id && v3.setExtra("sessionId", this.session_id), this.properties) {
          var g3 = qa({}, this.properties);
          g3.userName = g3.userName ? "[Filtered]" : void 0, g3.userData = g3.userData ? "[Filtered]" : void 0, g3.token = g3.token ? "[Filtered]" : void 0, v3.setExtra("properties", g3);
        }
        if (s2) {
          var m3 = s2.searchParams.get("domain");
          if (!m3) {
            var y3 = s2.host.match(/(.*?)\./);
            m3 = y3 && y3[1] || "";
          }
          m3 && v3.setTag("domain", m3);
        }
        e2.error && (v3.setTag("fatalErrorType", e2.error.type), v3.setExtra("errorDetails", e2.error.details), (null === (c3 = e2.error.details) || void 0 === c3 ? void 0 : c3.uri) && v3.setTag("serverAddress", e2.error.details.uri), (null === (l2 = e2.error.details) || void 0 === l2 ? void 0 : l2.workerGroup) && v3.setTag("workerGroup", e2.error.details.workerGroup), (null === (u2 = e2.error.details) || void 0 === u2 ? void 0 : u2.geoGroup) && v3.setTag("geoGroup", e2.error.details.geoGroup), (null === (d3 = e2.error.details) || void 0 === d3 ? void 0 : d3.on) && v3.setTag("connectionAttempt", e2.error.details.on), null !== (h2 = e2.error.details) && void 0 !== h2 && h2.bundleUrl && (v3.setTag("bundleUrl", e2.error.details.bundleUrl), v3.setTag("bundleError", e2.error.details.sourceError.type)));
        v3.setTags({ callMode: this._callObjectMode ? ys() ? "reactNative" : null !== (i3 = this.properties) && void 0 !== i3 && null !== (i3 = i3.dailyConfig) && void 0 !== i3 && null !== (i3 = i3.callMode) && void 0 !== i3 && i3.includes("prebuilt") ? this.properties.dailyConfig.callMode : "custom" : "prebuilt-frame", version: r2.version() });
        var b3 = (null === (o2 = e2.error) || void 0 === o2 ? void 0 : o2.msg) || e2.errorMsg;
        v3.captureException(new Error(b3));
      } }, { key: "_callMachine", value: function() {
        var e2;
        return null === (e2 = window._daily) || void 0 === e2 || null === (e2 = e2.instances) || void 0 === e2 || null === (e2 = e2[this.callClientId]) || void 0 === e2 ? void 0 : e2.callMachine;
      } }, { key: "_maybeUpdateInputSettings", value: function(e2) {
        if (!N(this._inputSettings, e2)) {
          var t2 = this._getInputSettings();
          this._inputSettings = e2;
          var n2 = this._getInputSettings();
          N(t2, n2) || this.emitDailyJSEvent({ action: es, inputSettings: n2 });
        }
      } }], [{ key: "supportedBrowser", value: function() {
        if (ys())
          return { supported: true, mobile: true, name: "React Native", version: null, supportsScreenShare: true, supportsSfu: true, supportsVideoProcessing: false, supportsAudioProcessing: false };
        var e2 = Q.getParser(ms());
        return { supported: !!Os(), mobile: "mobile" === e2.getPlatformType(), name: e2.getBrowserName(), version: e2.getBrowserVersion(), supportsFullscreen: !!ws(), supportsScreenShare: !!_s(), supportsSfu: !!Os(), supportsVideoProcessing: Es(), supportsAudioProcessing: Ts() };
      } }, { key: "version", value: function() {
        return "0.79.0";
      } }, { key: "createCallObject", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        return e2.layout = "none", new r2(null, e2);
      } }, { key: "wrap", value: function(e2) {
        var t2 = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        if (gc(), !e2 || !e2.contentWindow || "string" != typeof e2.src)
          throw new Error("DailyIframe::Wrap needs an iframe-like first argument");
        return t2.layout || (t2.customLayout ? t2.layout = "custom-v1" : t2.layout = "browser"), new r2(e2, t2);
      } }, { key: "createFrame", value: function(e2, t2) {
        var n2, i3;
        gc(), e2 && t2 ? (n2 = e2, i3 = t2) : e2 && e2.append ? (n2 = e2, i3 = {}) : (n2 = document.body, i3 = e2 || {});
        var o2 = i3.iframeStyle;
        o2 || (o2 = n2 === document.body ? { position: "fixed", border: "1px solid black", backgroundColor: "white", width: "375px", height: "450px", right: "1em", bottom: "1em" } : { border: 0, width: "100%", height: "100%" });
        var s2 = document.createElement("iframe");
        window.navigator && window.navigator.userAgent.match(/Chrome\/61\./) ? s2.allow = "microphone, camera" : s2.allow = "microphone; camera; autoplay; display-capture; screen-wake-lock", s2.style.visibility = "hidden", n2.appendChild(s2), s2.style.visibility = null, Object.keys(o2).forEach(function(e3) {
          return s2.style[e3] = o2[e3];
        }), i3.layout || (i3.customLayout ? i3.layout = "custom-v1" : i3.layout = "browser");
        try {
          return new r2(s2, i3);
        } catch (e3) {
          throw n2.removeChild(s2), e3;
        }
      } }, { key: "createTransparentFrame", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        gc();
        var t2 = document.createElement("iframe");
        return t2.allow = "microphone; camera; autoplay", t2.style.cssText = "\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: 0;\n      pointer-events: none;\n    ", document.body.appendChild(t2), e2.layout || (e2.layout = "custom-v1"), r2.wrap(t2, e2);
      } }, { key: "getCallInstance", value: function() {
        var e2 = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : void 0;
        return e2 ? Ga[e2] : Object.values(Ga)[0];
      } }]);
      var i2, c2, d2, p2, v2, g2, m2, y2, b2, _2, w2, S2, k2, M2, C2, E2, T2, O2, P2, A2, j2, I2, L2, D2, R2, F2, B2, U2, V2, J2, $2, q2, z2, W2, H2, G2, Y2, ee2;
    }();
  }
});

// node_modules/events/events.js
var require_events = __commonJS({
  "node_modules/events/events.js"(exports, module) {
    "use strict";
    var R2 = typeof Reflect === "object" ? Reflect : null;
    var ReflectApply = R2 && typeof R2.apply === "function" ? R2.apply : function ReflectApply2(target, receiver, args) {
      return Function.prototype.apply.call(target, receiver, args);
    };
    var ReflectOwnKeys;
    if (R2 && typeof R2.ownKeys === "function") {
      ReflectOwnKeys = R2.ownKeys;
    } else if (Object.getOwnPropertySymbols) {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target).concat(Object.getOwnPropertySymbols(target));
      };
    } else {
      ReflectOwnKeys = function ReflectOwnKeys2(target) {
        return Object.getOwnPropertyNames(target);
      };
    }
    function ProcessEmitWarning(warning) {
      if (console && console.warn)
        console.warn(warning);
    }
    var NumberIsNaN = Number.isNaN || function NumberIsNaN2(value) {
      return value !== value;
    };
    function EventEmitter() {
      EventEmitter.init.call(this);
    }
    module.exports = EventEmitter;
    module.exports.once = once;
    EventEmitter.EventEmitter = EventEmitter;
    EventEmitter.prototype._events = void 0;
    EventEmitter.prototype._eventsCount = 0;
    EventEmitter.prototype._maxListeners = void 0;
    var defaultMaxListeners = 10;
    function checkListener(listener) {
      if (typeof listener !== "function") {
        throw new TypeError('The "listener" argument must be of type Function. Received type ' + typeof listener);
      }
    }
    Object.defineProperty(EventEmitter, "defaultMaxListeners", {
      enumerable: true,
      get: function() {
        return defaultMaxListeners;
      },
      set: function(arg) {
        if (typeof arg !== "number" || arg < 0 || NumberIsNaN(arg)) {
          throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received ' + arg + ".");
        }
        defaultMaxListeners = arg;
      }
    });
    EventEmitter.init = function() {
      if (this._events === void 0 || this._events === Object.getPrototypeOf(this)._events) {
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
      }
      this._maxListeners = this._maxListeners || void 0;
    };
    EventEmitter.prototype.setMaxListeners = function setMaxListeners(n2) {
      if (typeof n2 !== "number" || n2 < 0 || NumberIsNaN(n2)) {
        throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received ' + n2 + ".");
      }
      this._maxListeners = n2;
      return this;
    };
    function _getMaxListeners(that) {
      if (that._maxListeners === void 0)
        return EventEmitter.defaultMaxListeners;
      return that._maxListeners;
    }
    EventEmitter.prototype.getMaxListeners = function getMaxListeners() {
      return _getMaxListeners(this);
    };
    EventEmitter.prototype.emit = function emit(type) {
      var args = [];
      for (var i2 = 1; i2 < arguments.length; i2++)
        args.push(arguments[i2]);
      var doError = type === "error";
      var events = this._events;
      if (events !== void 0)
        doError = doError && events.error === void 0;
      else if (!doError)
        return false;
      if (doError) {
        var er2;
        if (args.length > 0)
          er2 = args[0];
        if (er2 instanceof Error) {
          throw er2;
        }
        var err = new Error("Unhandled error." + (er2 ? " (" + er2.message + ")" : ""));
        err.context = er2;
        throw err;
      }
      var handler = events[type];
      if (handler === void 0)
        return false;
      if (typeof handler === "function") {
        ReflectApply(handler, this, args);
      } else {
        var len = handler.length;
        var listeners = arrayClone(handler, len);
        for (var i2 = 0; i2 < len; ++i2)
          ReflectApply(listeners[i2], this, args);
      }
      return true;
    };
    function _addListener(target, type, listener, prepend) {
      var m2;
      var events;
      var existing;
      checkListener(listener);
      events = target._events;
      if (events === void 0) {
        events = target._events = /* @__PURE__ */ Object.create(null);
        target._eventsCount = 0;
      } else {
        if (events.newListener !== void 0) {
          target.emit(
            "newListener",
            type,
            listener.listener ? listener.listener : listener
          );
          events = target._events;
        }
        existing = events[type];
      }
      if (existing === void 0) {
        existing = events[type] = listener;
        ++target._eventsCount;
      } else {
        if (typeof existing === "function") {
          existing = events[type] = prepend ? [listener, existing] : [existing, listener];
        } else if (prepend) {
          existing.unshift(listener);
        } else {
          existing.push(listener);
        }
        m2 = _getMaxListeners(target);
        if (m2 > 0 && existing.length > m2 && !existing.warned) {
          existing.warned = true;
          var w2 = new Error("Possible EventEmitter memory leak detected. " + existing.length + " " + String(type) + " listeners added. Use emitter.setMaxListeners() to increase limit");
          w2.name = "MaxListenersExceededWarning";
          w2.emitter = target;
          w2.type = type;
          w2.count = existing.length;
          ProcessEmitWarning(w2);
        }
      }
      return target;
    }
    EventEmitter.prototype.addListener = function addListener(type, listener) {
      return _addListener(this, type, listener, false);
    };
    EventEmitter.prototype.on = EventEmitter.prototype.addListener;
    EventEmitter.prototype.prependListener = function prependListener(type, listener) {
      return _addListener(this, type, listener, true);
    };
    function onceWrapper() {
      if (!this.fired) {
        this.target.removeListener(this.type, this.wrapFn);
        this.fired = true;
        if (arguments.length === 0)
          return this.listener.call(this.target);
        return this.listener.apply(this.target, arguments);
      }
    }
    function _onceWrap(target, type, listener) {
      var state = { fired: false, wrapFn: void 0, target, type, listener };
      var wrapped = onceWrapper.bind(state);
      wrapped.listener = listener;
      state.wrapFn = wrapped;
      return wrapped;
    }
    EventEmitter.prototype.once = function once2(type, listener) {
      checkListener(listener);
      this.on(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.prependOnceListener = function prependOnceListener(type, listener) {
      checkListener(listener);
      this.prependListener(type, _onceWrap(this, type, listener));
      return this;
    };
    EventEmitter.prototype.removeListener = function removeListener(type, listener) {
      var list, events, position, i2, originalListener;
      checkListener(listener);
      events = this._events;
      if (events === void 0)
        return this;
      list = events[type];
      if (list === void 0)
        return this;
      if (list === listener || list.listener === listener) {
        if (--this._eventsCount === 0)
          this._events = /* @__PURE__ */ Object.create(null);
        else {
          delete events[type];
          if (events.removeListener)
            this.emit("removeListener", type, list.listener || listener);
        }
      } else if (typeof list !== "function") {
        position = -1;
        for (i2 = list.length - 1; i2 >= 0; i2--) {
          if (list[i2] === listener || list[i2].listener === listener) {
            originalListener = list[i2].listener;
            position = i2;
            break;
          }
        }
        if (position < 0)
          return this;
        if (position === 0)
          list.shift();
        else {
          spliceOne(list, position);
        }
        if (list.length === 1)
          events[type] = list[0];
        if (events.removeListener !== void 0)
          this.emit("removeListener", type, originalListener || listener);
      }
      return this;
    };
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.removeAllListeners = function removeAllListeners(type) {
      var listeners, events, i2;
      events = this._events;
      if (events === void 0)
        return this;
      if (events.removeListener === void 0) {
        if (arguments.length === 0) {
          this._events = /* @__PURE__ */ Object.create(null);
          this._eventsCount = 0;
        } else if (events[type] !== void 0) {
          if (--this._eventsCount === 0)
            this._events = /* @__PURE__ */ Object.create(null);
          else
            delete events[type];
        }
        return this;
      }
      if (arguments.length === 0) {
        var keys = Object.keys(events);
        var key;
        for (i2 = 0; i2 < keys.length; ++i2) {
          key = keys[i2];
          if (key === "removeListener")
            continue;
          this.removeAllListeners(key);
        }
        this.removeAllListeners("removeListener");
        this._events = /* @__PURE__ */ Object.create(null);
        this._eventsCount = 0;
        return this;
      }
      listeners = events[type];
      if (typeof listeners === "function") {
        this.removeListener(type, listeners);
      } else if (listeners !== void 0) {
        for (i2 = listeners.length - 1; i2 >= 0; i2--) {
          this.removeListener(type, listeners[i2]);
        }
      }
      return this;
    };
    function _listeners(target, type, unwrap) {
      var events = target._events;
      if (events === void 0)
        return [];
      var evlistener = events[type];
      if (evlistener === void 0)
        return [];
      if (typeof evlistener === "function")
        return unwrap ? [evlistener.listener || evlistener] : [evlistener];
      return unwrap ? unwrapListeners(evlistener) : arrayClone(evlistener, evlistener.length);
    }
    EventEmitter.prototype.listeners = function listeners(type) {
      return _listeners(this, type, true);
    };
    EventEmitter.prototype.rawListeners = function rawListeners(type) {
      return _listeners(this, type, false);
    };
    EventEmitter.listenerCount = function(emitter, type) {
      if (typeof emitter.listenerCount === "function") {
        return emitter.listenerCount(type);
      } else {
        return listenerCount.call(emitter, type);
      }
    };
    EventEmitter.prototype.listenerCount = listenerCount;
    function listenerCount(type) {
      var events = this._events;
      if (events !== void 0) {
        var evlistener = events[type];
        if (typeof evlistener === "function") {
          return 1;
        } else if (evlistener !== void 0) {
          return evlistener.length;
        }
      }
      return 0;
    }
    EventEmitter.prototype.eventNames = function eventNames() {
      return this._eventsCount > 0 ? ReflectOwnKeys(this._events) : [];
    };
    function arrayClone(arr, n2) {
      var copy = new Array(n2);
      for (var i2 = 0; i2 < n2; ++i2)
        copy[i2] = arr[i2];
      return copy;
    }
    function spliceOne(list, index) {
      for (; index + 1 < list.length; index++)
        list[index] = list[index + 1];
      list.pop();
    }
    function unwrapListeners(arr) {
      var ret = new Array(arr.length);
      for (var i2 = 0; i2 < ret.length; ++i2) {
        ret[i2] = arr[i2].listener || arr[i2];
      }
      return ret;
    }
    function once(emitter, name) {
      return new Promise(function(resolve, reject) {
        function errorListener(err) {
          emitter.removeListener(name, resolver);
          reject(err);
        }
        function resolver() {
          if (typeof emitter.removeListener === "function") {
            emitter.removeListener("error", errorListener);
          }
          resolve([].slice.call(arguments));
        }
        ;
        eventTargetAgnosticAddListener(emitter, name, resolver, { once: true });
        if (name !== "error") {
          addErrorHandlerIfEventEmitter(emitter, errorListener, { once: true });
        }
      });
    }
    function addErrorHandlerIfEventEmitter(emitter, handler, flags) {
      if (typeof emitter.on === "function") {
        eventTargetAgnosticAddListener(emitter, "error", handler, flags);
      }
    }
    function eventTargetAgnosticAddListener(emitter, name, listener, flags) {
      if (typeof emitter.on === "function") {
        if (flags.once) {
          emitter.once(name, listener);
        } else {
          emitter.on(name, listener);
        }
      } else if (typeof emitter.addEventListener === "function") {
        emitter.addEventListener(name, function wrapListener(arg) {
          if (flags.once) {
            emitter.removeEventListener(name, wrapListener);
          }
          listener(arg);
        });
      } else {
        throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type ' + typeof emitter);
      }
    }
  }
});

// node_modules/@vapi-ai/web/dist/api.js
var require_api = __commonJS({
  "node_modules/@vapi-ai/web/dist/api.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Api = exports.HttpClient = exports.ContentType = void 0;
    var ContentType;
    (function(ContentType2) {
      ContentType2["Json"] = "application/json";
      ContentType2["FormData"] = "multipart/form-data";
      ContentType2["UrlEncoded"] = "application/x-www-form-urlencoded";
      ContentType2["Text"] = "text/plain";
    })(ContentType || (exports.ContentType = ContentType = {}));
    var HttpClient = class {
      constructor(apiConfig = {}) {
        __publicField(this, "baseUrl", "https://api.vapi.ai");
        __publicField(this, "securityData", null);
        __publicField(this, "securityWorker");
        __publicField(this, "abortControllers", /* @__PURE__ */ new Map());
        __publicField(this, "customFetch", (...fetchParams) => fetch(...fetchParams));
        __publicField(this, "baseApiParams", {
          credentials: "same-origin",
          headers: {},
          redirect: "follow",
          referrerPolicy: "no-referrer"
        });
        __publicField(this, "setSecurityData", (data) => {
          this.securityData = data;
        });
        __publicField(this, "contentFormatters", {
          [ContentType.Json]: (input) => input !== null && (typeof input === "object" || typeof input === "string") ? JSON.stringify(input) : input,
          [ContentType.Text]: (input) => input !== null && typeof input !== "string" ? JSON.stringify(input) : input,
          [ContentType.FormData]: (input) => Object.keys(input || {}).reduce((formData, key) => {
            const property = input[key];
            formData.append(key, property instanceof Blob ? property : typeof property === "object" && property !== null ? JSON.stringify(property) : `${property}`);
            return formData;
          }, new FormData()),
          [ContentType.UrlEncoded]: (input) => this.toQueryString(input)
        });
        __publicField(this, "createAbortSignal", (cancelToken) => {
          if (this.abortControllers.has(cancelToken)) {
            const abortController2 = this.abortControllers.get(cancelToken);
            if (abortController2) {
              return abortController2.signal;
            }
            return void 0;
          }
          const abortController = new AbortController();
          this.abortControllers.set(cancelToken, abortController);
          return abortController.signal;
        });
        __publicField(this, "abortRequest", (cancelToken) => {
          const abortController = this.abortControllers.get(cancelToken);
          if (abortController) {
            abortController.abort();
            this.abortControllers.delete(cancelToken);
          }
        });
        __publicField(this, "request", async ({ body, secure, path, type, query, format, baseUrl, cancelToken, ...params }) => {
          const secureParams = (typeof secure === "boolean" ? secure : this.baseApiParams.secure) && this.securityWorker && await this.securityWorker(this.securityData) || {};
          const requestParams = this.mergeRequestParams(params, secureParams);
          const queryString = query && this.toQueryString(query);
          const payloadFormatter = this.contentFormatters[type || ContentType.Json];
          const responseFormat = format || requestParams.format;
          return this.customFetch(`${baseUrl || this.baseUrl || ""}${path}${queryString ? `?${queryString}` : ""}`, {
            ...requestParams,
            headers: {
              ...requestParams.headers || {},
              ...type && type !== ContentType.FormData ? { "Content-Type": type } : {}
            },
            signal: (cancelToken ? this.createAbortSignal(cancelToken) : requestParams.signal) || null,
            body: typeof body === "undefined" || body === null ? null : payloadFormatter(body)
          }).then(async (response) => {
            const r2 = response;
            r2.data = null;
            r2.error = null;
            const data = !responseFormat ? r2 : await response[responseFormat]().then((data2) => {
              if (r2.ok) {
                r2.data = data2;
              } else {
                r2.error = data2;
              }
              return r2;
            }).catch((e2) => {
              r2.error = e2;
              return r2;
            });
            if (cancelToken) {
              this.abortControllers.delete(cancelToken);
            }
            if (!response.ok)
              throw data;
            return data;
          });
        });
        Object.assign(this, apiConfig);
      }
      encodeQueryParam(key, value) {
        const encodedKey = encodeURIComponent(key);
        return `${encodedKey}=${encodeURIComponent(typeof value === "number" ? value : `${value}`)}`;
      }
      addQueryParam(query, key) {
        return this.encodeQueryParam(key, query[key]);
      }
      addArrayQueryParam(query, key) {
        const value = query[key];
        return value.map((v2) => this.encodeQueryParam(key, v2)).join("&");
      }
      toQueryString(rawQuery) {
        const query = rawQuery || {};
        const keys = Object.keys(query).filter((key) => "undefined" !== typeof query[key]);
        return keys.map((key) => Array.isArray(query[key]) ? this.addArrayQueryParam(query, key) : this.addQueryParam(query, key)).join("&");
      }
      addQueryParams(rawQuery) {
        const queryString = this.toQueryString(rawQuery);
        return queryString ? `?${queryString}` : "";
      }
      mergeRequestParams(params1, params2) {
        return {
          ...this.baseApiParams,
          ...params1,
          ...params2 || {},
          headers: {
            ...this.baseApiParams.headers || {},
            ...params1.headers || {},
            ...params2 && params2.headers || {}
          }
        };
      }
    };
    exports.HttpClient = HttpClient;
    var Api = class extends HttpClient {
      constructor() {
        super(...arguments);
        __publicField(this, "call", {
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerCreate
           * @summary Create Call
           * @request POST:/call
           * @secure
           */
          callControllerCreate: (data, params = {}) => this.request({
            path: `/call`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerFindAll
           * @summary List Calls
           * @request GET:/call
           * @secure
           */
          callControllerFindAll: (query, params = {}) => this.request({
            path: `/call`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerFindOne
           * @summary Get Call
           * @request GET:/call/{id}
           * @secure
           */
          callControllerFindOne: (id, params = {}) => this.request({
            path: `/call/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerUpdate
           * @summary Update Call
           * @request PATCH:/call/{id}
           * @secure
           */
          callControllerUpdate: (id, data, params = {}) => this.request({
            path: `/call/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerDeleteCallData
           * @summary Delete Call Data
           * @request DELETE:/call/{id}
           * @secure
           */
          callControllerDeleteCallData: (id, params = {}) => this.request({
            path: `/call/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerCreatePhoneCall
           * @summary Create Phone Call
           * @request POST:/call/phone
           * @deprecated
           * @secure
           */
          callControllerCreatePhoneCall: (data, params = {}) => this.request({
            path: `/call/phone`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerCreateWebCall
           * @summary Create Web Call
           * @request POST:/call/web
           * @secure
           */
          callControllerCreateWebCall: (data, params = {}) => this.request({
            path: `/call/web`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          })
        });
        __publicField(this, "v2", {
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerExportCalls
           * @summary Export Calls to CSV
           * @request GET:/v2/call/export
           * @secure
           */
          callControllerExportCalls: (query, params = {}) => this.request({
            path: `/v2/call/export`,
            method: "GET",
            query,
            secure: true,
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerFindAllPaginated
           * @summary List Calls
           * @request GET:/v2/call
           * @secure
           */
          callControllerFindAllPaginated: (query, params = {}) => this.request({
            path: `/v2/call`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Calls
           * @name CallControllerFindAllMetadataPaginated
           * @summary List Call Metadata
           * @request GET:/v2/call/metadata
           * @secure
           */
          callControllerFindAllMetadataPaginated: (query, params = {}) => this.request({
            path: `/v2/call/metadata`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerFindAllPaginated
           * @summary List Assistants with pagination
           * @request GET:/v2/assistant
           * @secure
           */
          assistantControllerFindAllPaginated: (query, params = {}) => this.request({
            path: `/v2/assistant`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerFindAllPaginated
           * @summary List Phone Numbers
           * @request GET:/v2/phone-number
           * @secure
           */
          phoneNumberControllerFindAllPaginated: (query, params = {}) => this.request({
            path: `/v2/phone-number`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "chat", {
          /**
           * No description
           *
           * @tags Chat
           * @name ChatControllerListChats
           * @summary List chats
           * @request GET:/chat
           * @secure
           */
          chatControllerListChats: (query, params = {}) => this.request({
            path: `/chat`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * @description Creates a new chat. Requires at least one of: assistantId/assistant, sessionId, or previousChatId. Note: sessionId and previousChatId are mutually exclusive.
           *
           * @tags Chat
           * @name ChatControllerCreateChat
           * @summary Create chat
           * @request POST:/chat
           * @secure
           */
          chatControllerCreateChat: (data, params = {}) => this.request({
            path: `/chat`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Chat
           * @name ChatControllerGetChat
           * @summary Get a specific chat by ID
           * @request GET:/chat/{id}
           * @secure
           */
          chatControllerGetChat: (id, params = {}) => this.request({
            path: `/chat/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Chat
           * @name ChatControllerDeleteChat
           * @summary Delete chat
           * @request DELETE:/chat/{id}
           * @secure
           */
          chatControllerDeleteChat: (id, params = {}) => this.request({
            path: `/chat/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Chat
           * @name ChatControllerCreateOpenAiChat
           * @summary Create chat using OpenAI Responses API format
           * @request POST:/chat/responses
           * @secure
           */
          chatControllerCreateOpenAiChat: (data, params = {}) => this.request({
            path: `/chat/responses`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          })
        });
        __publicField(this, "session", {
          /**
           * No description
           *
           * @tags Sessions
           * @name SessionControllerCreate
           * @summary Create Session
           * @request POST:/session
           * @secure
           */
          sessionControllerCreate: (data, params = {}) => this.request({
            path: `/session`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Sessions
           * @name SessionControllerFindAllPaginated
           * @summary List Sessions
           * @request GET:/session
           * @secure
           */
          sessionControllerFindAllPaginated: (query, params = {}) => this.request({
            path: `/session`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Sessions
           * @name SessionControllerFindOne
           * @summary Get Session
           * @request GET:/session/{id}
           * @secure
           */
          sessionControllerFindOne: (id, params = {}) => this.request({
            path: `/session/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Sessions
           * @name SessionControllerUpdate
           * @summary Update Session
           * @request PATCH:/session/{id}
           * @secure
           */
          sessionControllerUpdate: (id, data, params = {}) => this.request({
            path: `/session/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Sessions
           * @name SessionControllerRemove
           * @summary Delete Session
           * @request DELETE:/session/{id}
           * @secure
           */
          sessionControllerRemove: (id, params = {}) => this.request({
            path: `/session/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "assistant", {
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerCreate
           * @summary Create Assistant
           * @request POST:/assistant
           * @secure
           */
          assistantControllerCreate: (data, params = {}) => this.request({
            path: `/assistant`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerFindAll
           * @summary List Assistants
           * @request GET:/assistant
           * @secure
           */
          assistantControllerFindAll: (query, params = {}) => this.request({
            path: `/assistant`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerFindOne
           * @summary Get Assistant
           * @request GET:/assistant/{id}
           * @secure
           */
          assistantControllerFindOne: (id, params = {}) => this.request({
            path: `/assistant/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerUpdate
           * @summary Update Assistant
           * @request PATCH:/assistant/{id}
           * @secure
           */
          assistantControllerUpdate: (id, data, params = {}) => this.request({
            path: `/assistant/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerReplace
           * @summary Replace Assistant
           * @request PUT:/assistant/{id}
           * @secure
           */
          assistantControllerReplace: (id, data, params = {}) => this.request({
            path: `/assistant/${id}`,
            method: "PUT",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerRemove
           * @summary Delete Assistant
           * @request DELETE:/assistant/{id}
           * @secure
           */
          assistantControllerRemove: (id, params = {}) => this.request({
            path: `/assistant/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Assistants
           * @name AssistantControllerFindVersions
           * @summary List Assistant Versions
           * @request GET:/assistant/{id}/version
           * @secure
           */
          assistantControllerFindVersions: (id, query, params = {}) => this.request({
            path: `/assistant/${id}/version`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "phoneNumber", {
          /**
           * @description Use POST /phone-number instead.
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerImportTwilio
           * @summary Import Twilio Number
           * @request POST:/phone-number/import/twilio
           * @deprecated
           * @secure
           */
          phoneNumberControllerImportTwilio: (data, params = {}) => this.request({
            path: `/phone-number/import/twilio`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * @description Use POST /phone-number instead.
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerImportVonage
           * @summary Import Vonage Number
           * @request POST:/phone-number/import/vonage
           * @deprecated
           * @secure
           */
          phoneNumberControllerImportVonage: (data, params = {}) => this.request({
            path: `/phone-number/import/vonage`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerCreate
           * @summary Create Phone Number
           * @request POST:/phone-number
           * @secure
           */
          phoneNumberControllerCreate: (data, params = {}) => this.request({
            path: `/phone-number`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerFindAll
           * @summary List Phone Numbers
           * @request GET:/phone-number
           * @secure
           */
          phoneNumberControllerFindAll: (query, params = {}) => this.request({
            path: `/phone-number`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerFindOne
           * @summary Get Phone Number
           * @request GET:/phone-number/{id}
           * @secure
           */
          phoneNumberControllerFindOne: (id, params = {}) => this.request({
            path: `/phone-number/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerUpdate
           * @summary Update Phone Number
           * @request PATCH:/phone-number/{id}
           * @secure
           */
          phoneNumberControllerUpdate: (id, data, params = {}) => this.request({
            path: `/phone-number/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Phone Numbers
           * @name PhoneNumberControllerRemove
           * @summary Delete Phone Number
           * @request DELETE:/phone-number/{id}
           * @secure
           */
          phoneNumberControllerRemove: (id, params = {}) => this.request({
            path: `/phone-number/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "tool", {
          /**
           * No description
           *
           * @tags Tools
           * @name ToolControllerCreate
           * @summary Create Tool
           * @request POST:/tool
           * @secure
           */
          toolControllerCreate: (data, params = {}) => this.request({
            path: `/tool`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tools
           * @name ToolControllerFindAll
           * @summary List Tools
           * @request GET:/tool
           * @secure
           */
          toolControllerFindAll: (query, params = {}) => this.request({
            path: `/tool`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tools
           * @name ToolControllerFindOne
           * @summary Get Tool
           * @request GET:/tool/{id}
           * @secure
           */
          toolControllerFindOne: (id, params = {}) => this.request({
            path: `/tool/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tools
           * @name ToolControllerUpdate
           * @summary Update Tool
           * @request PATCH:/tool/{id}
           * @secure
           */
          toolControllerUpdate: (id, data, params = {}) => this.request({
            path: `/tool/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tools
           * @name ToolControllerRemove
           * @summary Delete Tool
           * @request DELETE:/tool/{id}
           * @secure
           */
          toolControllerRemove: (id, params = {}) => this.request({
            path: `/tool/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "file", {
          /**
           * @description Use POST /file instead.
           *
           * @tags Files
           * @name FileControllerCreateDeprecated
           * @summary Upload File
           * @request POST:/file/upload
           * @deprecated
           * @secure
           */
          fileControllerCreateDeprecated: (data, params = {}) => this.request({
            path: `/file/upload`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.FormData,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Files
           * @name FileControllerCreate
           * @summary Upload File
           * @request POST:/file
           * @secure
           */
          fileControllerCreate: (data, params = {}) => this.request({
            path: `/file`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.FormData,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Files
           * @name FileControllerFindAll
           * @summary List Files
           * @request GET:/file
           * @secure
           */
          fileControllerFindAll: (params = {}) => this.request({
            path: `/file`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Files
           * @name FileControllerFindOne
           * @summary Get File
           * @request GET:/file/{id}
           * @secure
           */
          fileControllerFindOne: (id, params = {}) => this.request({
            path: `/file/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Files
           * @name FileControllerUpdate
           * @summary Update File
           * @request PATCH:/file/{id}
           * @secure
           */
          fileControllerUpdate: (id, data, params = {}) => this.request({
            path: `/file/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Files
           * @name FileControllerRemove
           * @summary Delete File
           * @request DELETE:/file/{id}
           * @secure
           */
          fileControllerRemove: (id, params = {}) => this.request({
            path: `/file/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "knowledgeBase", {
          /**
           * No description
           *
           * @tags Knowledge Base
           * @name KnowledgeBaseControllerCreate
           * @summary Create Knowledge Base
           * @request POST:/knowledge-base
           * @secure
           */
          knowledgeBaseControllerCreate: (data, params = {}) => this.request({
            path: `/knowledge-base`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Knowledge Base
           * @name KnowledgeBaseControllerFindAll
           * @summary List Knowledge Bases
           * @request GET:/knowledge-base
           * @secure
           */
          knowledgeBaseControllerFindAll: (query, params = {}) => this.request({
            path: `/knowledge-base`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Knowledge Base
           * @name KnowledgeBaseControllerFindOne
           * @summary Get Knowledge Base
           * @request GET:/knowledge-base/{id}
           * @secure
           */
          knowledgeBaseControllerFindOne: (id, params = {}) => this.request({
            path: `/knowledge-base/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Knowledge Base
           * @name KnowledgeBaseControllerUpdate
           * @summary Update Knowledge Base
           * @request PATCH:/knowledge-base/{id}
           * @secure
           */
          knowledgeBaseControllerUpdate: (id, data, params = {}) => this.request({
            path: `/knowledge-base/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Knowledge Base
           * @name KnowledgeBaseControllerRemove
           * @summary Delete Knowledge Base
           * @request DELETE:/knowledge-base/{id}
           * @secure
           */
          knowledgeBaseControllerRemove: (id, params = {}) => this.request({
            path: `/knowledge-base/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "workflow", {
          /**
           * No description
           *
           * @tags Workflow
           * @name WorkflowControllerFindAll
           * @summary [BETA] Get Workflows
           * @request GET:/workflow
           * @secure
           */
          workflowControllerFindAll: (params = {}) => this.request({
            path: `/workflow`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Workflow
           * @name WorkflowControllerCreate
           * @summary [BETA] Create Workflow
           * @request POST:/workflow
           * @secure
           */
          workflowControllerCreate: (data, params = {}) => this.request({
            path: `/workflow`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Workflow
           * @name WorkflowControllerFindOne
           * @summary [BETA] Get Workflow
           * @request GET:/workflow/{id}
           * @secure
           */
          workflowControllerFindOne: (id, params = {}) => this.request({
            path: `/workflow/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Workflow
           * @name WorkflowControllerDelete
           * @summary [BETA] Delete Workflow
           * @request DELETE:/workflow/{id}
           * @secure
           */
          workflowControllerDelete: (id, params = {}) => this.request({
            path: `/workflow/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Workflow
           * @name WorkflowControllerUpdate
           * @summary [BETA] Update Workflow
           * @request PATCH:/workflow/{id}
           * @secure
           */
          workflowControllerUpdate: (id, data, params = {}) => this.request({
            path: `/workflow/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          })
        });
        __publicField(this, "squad", {
          /**
           * No description
           *
           * @tags Squads
           * @name SquadControllerCreate
           * @summary Create Squad
           * @request POST:/squad
           * @secure
           */
          squadControllerCreate: (data, params = {}) => this.request({
            path: `/squad`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Squads
           * @name SquadControllerFindAll
           * @summary List Squads
           * @request GET:/squad
           * @secure
           */
          squadControllerFindAll: (query, params = {}) => this.request({
            path: `/squad`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Squads
           * @name SquadControllerFindOne
           * @summary Get Squad
           * @request GET:/squad/{id}
           * @secure
           */
          squadControllerFindOne: (id, params = {}) => this.request({
            path: `/squad/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Squads
           * @name SquadControllerUpdate
           * @summary Update Squad
           * @request PATCH:/squad/{id}
           * @secure
           */
          squadControllerUpdate: (id, data, params = {}) => this.request({
            path: `/squad/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Squads
           * @name SquadControllerRemove
           * @summary Delete Squad
           * @request DELETE:/squad/{id}
           * @secure
           */
          squadControllerRemove: (id, params = {}) => this.request({
            path: `/squad/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "testSuite", {
          /**
           * No description
           *
           * @tags Test Suites
           * @name TestSuiteControllerFindAllPaginated
           * @summary List Test Suites
           * @request GET:/test-suite
           * @secure
           */
          testSuiteControllerFindAllPaginated: (query, params = {}) => this.request({
            path: `/test-suite`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suites
           * @name TestSuiteControllerCreate
           * @summary Create Test Suite
           * @request POST:/test-suite
           * @secure
           */
          testSuiteControllerCreate: (data, params = {}) => this.request({
            path: `/test-suite`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suites
           * @name TestSuiteControllerFindOne
           * @summary Get Test Suite
           * @request GET:/test-suite/{id}
           * @secure
           */
          testSuiteControllerFindOne: (id, params = {}) => this.request({
            path: `/test-suite/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suites
           * @name TestSuiteControllerUpdate
           * @summary Update Test Suite
           * @request PATCH:/test-suite/{id}
           * @secure
           */
          testSuiteControllerUpdate: (id, data, params = {}) => this.request({
            path: `/test-suite/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suites
           * @name TestSuiteControllerRemove
           * @summary Delete Test Suite
           * @request DELETE:/test-suite/{id}
           * @secure
           */
          testSuiteControllerRemove: (id, params = {}) => this.request({
            path: `/test-suite/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Tests
           * @name TestSuiteTestControllerFindAllPaginated
           * @summary List Tests
           * @request GET:/test-suite/{testSuiteId}/test
           * @secure
           */
          testSuiteTestControllerFindAllPaginated: (testSuiteId, query, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/test`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Tests
           * @name TestSuiteTestControllerCreate
           * @summary Create Test
           * @request POST:/test-suite/{testSuiteId}/test
           * @secure
           */
          testSuiteTestControllerCreate: (testSuiteId, data, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/test`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Tests
           * @name TestSuiteTestControllerFindOne
           * @summary Get Test
           * @request GET:/test-suite/{testSuiteId}/test/{id}
           * @secure
           */
          testSuiteTestControllerFindOne: (testSuiteId, id, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/test/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Tests
           * @name TestSuiteTestControllerUpdate
           * @summary Update Test
           * @request PATCH:/test-suite/{testSuiteId}/test/{id}
           * @secure
           */
          testSuiteTestControllerUpdate: (testSuiteId, id, data, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/test/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Tests
           * @name TestSuiteTestControllerRemove
           * @summary Delete Test
           * @request DELETE:/test-suite/{testSuiteId}/test/{id}
           * @secure
           */
          testSuiteTestControllerRemove: (testSuiteId, id, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/test/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Runs
           * @name TestSuiteRunControllerFindAllPaginated
           * @summary List Test Suite Runs
           * @request GET:/test-suite/{testSuiteId}/run
           * @secure
           */
          testSuiteRunControllerFindAllPaginated: (testSuiteId, query, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/run`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Runs
           * @name TestSuiteRunControllerCreate
           * @summary Create Test Suite Run
           * @request POST:/test-suite/{testSuiteId}/run
           * @secure
           */
          testSuiteRunControllerCreate: (testSuiteId, data, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/run`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Runs
           * @name TestSuiteRunControllerFindOne
           * @summary Get Test Suite Run
           * @request GET:/test-suite/{testSuiteId}/run/{id}
           * @secure
           */
          testSuiteRunControllerFindOne: (testSuiteId, id, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/run/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Runs
           * @name TestSuiteRunControllerUpdate
           * @summary Update Test Suite Run
           * @request PATCH:/test-suite/{testSuiteId}/run/{id}
           * @secure
           */
          testSuiteRunControllerUpdate: (testSuiteId, id, data, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/run/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Test Suite Runs
           * @name TestSuiteRunControllerRemove
           * @summary Delete Test Suite Run
           * @request DELETE:/test-suite/{testSuiteId}/run/{id}
           * @secure
           */
          testSuiteRunControllerRemove: (testSuiteId, id, params = {}) => this.request({
            path: `/test-suite/${testSuiteId}/run/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "metrics", {
          /**
           * @description Use GET /metric instead
           *
           * @tags Analytics
           * @name AnalyticsControllerFindAllDeprecated
           * @summary List Billing Metrics
           * @request GET:/metrics
           * @deprecated
           * @secure
           */
          analyticsControllerFindAllDeprecated: (query, params = {}) => this.request({
            path: `/metrics`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "analytics", {
          /**
           * No description
           *
           * @tags Analytics
           * @name AnalyticsControllerQuery
           * @summary Create Analytics Queries
           * @request POST:/analytics
           * @secure
           */
          analyticsControllerQuery: (data, params = {}) => this.request({
            path: `/analytics`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          })
        });
        __publicField(this, "log", {
          /**
           * No description
           *
           * @tags Logs
           * @name LoggingControllerCallLogsQuery
           * @summary Get Call Logs
           * @request GET:/log
           * @secure
           */
          loggingControllerCallLogsQuery: (query, params = {}) => this.request({
            path: `/log`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Logs
           * @name LoggingControllerCallLogsDeleteQuery
           * @summary Delete Call Logs
           * @request DELETE:/log
           * @secure
           */
          loggingControllerCallLogsDeleteQuery: (query, params = {}) => this.request({
            path: `/log`,
            method: "DELETE",
            query,
            secure: true,
            ...params
          })
        });
        __publicField(this, "logs", {
          /**
           * No description
           *
           * @tags Logs
           * @name LoggingControllerLogsQuery
           * @summary Get Logs
           * @request GET:/logs
           * @deprecated
           * @secure
           */
          loggingControllerLogsQuery: (query, params = {}) => this.request({
            path: `/logs`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Logs
           * @name LoggingControllerLogsDeleteQuery
           * @summary Delete Logs
           * @request DELETE:/logs
           * @deprecated
           * @secure
           */
          loggingControllerLogsDeleteQuery: (query, params = {}) => this.request({
            path: `/logs`,
            method: "DELETE",
            query,
            secure: true,
            ...params
          })
        });
        __publicField(this, "org", {
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerCreate
           * @summary Create Org
           * @request POST:/org
           * @secure
           */
          orgControllerCreate: (data, params = {}) => this.request({
            path: `/org`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerFindAll
           * @summary List Orgs
           * @request GET:/org
           * @secure
           */
          orgControllerFindAll: (params = {}) => this.request({
            path: `/org`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerFindOne
           * @summary Get Org
           * @request GET:/org/{id}
           * @secure
           */
          orgControllerFindOne: (id, params = {}) => this.request({
            path: `/org/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerUpdate
           * @summary Update Org
           * @request PATCH:/org/{id}
           * @secure
           */
          orgControllerUpdate: (id, data, params = {}) => this.request({
            path: `/org/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerDeleteOrg
           * @summary Delete Org
           * @request DELETE:/org/{id}
           * @secure
           */
          orgControllerDeleteOrg: (id, params = {}) => this.request({
            path: `/org/${id}`,
            method: "DELETE",
            secure: true,
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerFindAllUsers
           * @summary List Users
           * @request GET:/org/{id}/user
           * @secure
           */
          orgControllerFindAllUsers: (id, params = {}) => this.request({
            path: `/org/${id}/user`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerOrgLeave
           * @summary Leave Org
           * @request DELETE:/org/{id}/leave
           * @secure
           */
          orgControllerOrgLeave: (id, params = {}) => this.request({
            path: `/org/${id}/leave`,
            method: "DELETE",
            secure: true,
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerOrgRemoveUser
           * @summary Remove Org Member
           * @request DELETE:/org/{id}/member/{memberId}/leave
           * @secure
           */
          orgControllerOrgRemoveUser: (id, memberId, params = {}) => this.request({
            path: `/org/${id}/member/${memberId}/leave`,
            method: "DELETE",
            secure: true,
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerUsersInvite
           * @summary Invite User
           * @request POST:/org/{id}/invite
           * @secure
           */
          orgControllerUsersInvite: (id, data, params = {}) => this.request({
            path: `/org/${id}/invite`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerUserUpdate
           * @summary Update User Role
           * @request PATCH:/org/{id}/role
           * @secure
           */
          orgControllerUserUpdate: (id, data, params = {}) => this.request({
            path: `/org/${id}/role`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            ...params
          }),
          /**
           * No description
           *
           * @tags Orgs
           * @name OrgControllerOrgToken
           * @summary Generate User Org JWT
           * @request GET:/org/{id}/auth
           * @secure
           */
          orgControllerOrgToken: (id, params = {}) => this.request({
            path: `/org/${id}/auth`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "token", {
          /**
           * No description
           *
           * @tags Tokens
           * @name TokenControllerCreate
           * @summary Create Token
           * @request POST:/token
           * @secure
           */
          tokenControllerCreate: (data, params = {}) => this.request({
            path: `/token`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tokens
           * @name TokenControllerFindAll
           * @summary List Tokens
           * @request GET:/token
           * @secure
           */
          tokenControllerFindAll: (query, params = {}) => this.request({
            path: `/token`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tokens
           * @name TokenControllerFindOne
           * @summary Get Token
           * @request GET:/token/{id}
           * @secure
           */
          tokenControllerFindOne: (id, params = {}) => this.request({
            path: `/token/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tokens
           * @name TokenControllerUpdate
           * @summary Update Token
           * @request PATCH:/token/{id}
           * @secure
           */
          tokenControllerUpdate: (id, data, params = {}) => this.request({
            path: `/token/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Tokens
           * @name TokenControllerRemove
           * @summary Delete Token
           * @request DELETE:/token/{id}
           * @secure
           */
          tokenControllerRemove: (id, params = {}) => this.request({
            path: `/token/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "credential", {
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerCreate
           * @summary Create Credential
           * @request POST:/credential
           * @secure
           */
          credentialControllerCreate: (data, params = {}) => this.request({
            path: `/credential`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerFindAll
           * @summary List Credentials
           * @request GET:/credential
           * @secure
           */
          credentialControllerFindAll: (query, params = {}) => this.request({
            path: `/credential`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerFindOne
           * @summary Get Credential
           * @request GET:/credential/{id}
           * @secure
           */
          credentialControllerFindOne: (id, params = {}) => this.request({
            path: `/credential/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerUpdate
           * @summary Update Credential
           * @request PATCH:/credential/{id}
           * @secure
           */
          credentialControllerUpdate: (id, data, params = {}) => this.request({
            path: `/credential/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerRemove
           * @summary Delete Credential
           * @request DELETE:/credential/{id}
           * @secure
           */
          credentialControllerRemove: (id, params = {}) => this.request({
            path: `/credential/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerGenerateSession
           * @summary Generate a credential session
           * @request POST:/credential/session
           * @secure
           */
          credentialControllerGenerateSession: (data, params = {}) => this.request({
            path: `/credential/session`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerHandleWebhook
           * @summary Handle credential webhook
           * @request POST:/credential/webhook
           */
          credentialControllerHandleWebhook: (data, params = {}) => this.request({
            path: `/credential/webhook`,
            method: "POST",
            body: data,
            type: ContentType.Json,
            ...params
          }),
          /**
           * No description
           *
           * @tags Credentials
           * @name CredentialControllerTriggerCredentialAction
           * @summary Trigger a credential action
           * @request POST:/credential/trigger
           * @secure
           */
          credentialControllerTriggerCredentialAction: (data, params = {}) => this.request({
            path: `/credential/trigger`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            ...params
          })
        });
        __publicField(this, "template", {
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerCreate
           * @summary Create Template
           * @request POST:/template
           * @secure
           */
          templateControllerCreate: (data, params = {}) => this.request({
            path: `/template`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerFindAll
           * @summary List Templates
           * @request GET:/template
           * @secure
           */
          templateControllerFindAll: (query, params = {}) => this.request({
            path: `/template`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerFindAllPinned
           * @summary List Templates
           * @request GET:/template/pinned
           * @secure
           */
          templateControllerFindAllPinned: (params = {}) => this.request({
            path: `/template/pinned`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerFindOne
           * @summary Get Template
           * @request GET:/template/{id}
           * @secure
           */
          templateControllerFindOne: (id, params = {}) => this.request({
            path: `/template/${id}`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerUpdate
           * @summary Update Template
           * @request PATCH:/template/{id}
           * @secure
           */
          templateControllerUpdate: (id, data, params = {}) => this.request({
            path: `/template/${id}`,
            method: "PATCH",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Templates
           * @name TemplateControllerRemove
           * @summary Delete Template
           * @request DELETE:/template/{id}
           * @secure
           */
          templateControllerRemove: (id, params = {}) => this.request({
            path: `/template/${id}`,
            method: "DELETE",
            secure: true,
            format: "json",
            ...params
          })
        });
        __publicField(this, "voiceLibrary", {
          /**
           * No description
           *
           * @tags Voice Library
           * @name VoiceLibraryControllerVoiceGetByProvider
           * @summary Get voices in Voice Library by Provider
           * @request GET:/voice-library/{provider}
           * @secure
           */
          voiceLibraryControllerVoiceGetByProvider: (provider, query, params = {}) => this.request({
            path: `/voice-library/${provider}`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Voice Library
           * @name VoiceLibraryControllerVoiceGetAccentsByProvider
           * @summary Get accents in Voice Library by Provider
           * @request GET:/voice-library/{provider}/accents
           * @secure
           */
          voiceLibraryControllerVoiceGetAccentsByProvider: (provider, params = {}) => this.request({
            path: `/voice-library/${provider}/accents`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Voice Library
           * @name VoiceLibraryControllerVoiceLibrarySyncByProvider
           * @summary Sync Private voices in Voice Library by Provider
           * @request POST:/voice-library/sync/{provider}
           * @secure
           */
          voiceLibraryControllerVoiceLibrarySyncByProvider: (provider, params = {}) => this.request({
            path: `/voice-library/sync/${provider}`,
            method: "POST",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Voice Library
           * @name VoiceLibraryControllerVoiceLibrarySyncDefaultVoices
           * @summary Sync Default voices in Voice Library by Providers
           * @request POST:/voice-library/sync
           * @secure
           */
          voiceLibraryControllerVoiceLibrarySyncDefaultVoices: (data, params = {}) => this.request({
            path: `/voice-library/sync`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Voice Library
           * @name VoiceLibraryControllerVoiceLibraryCreateSesameVoice
           * @summary Create a new voice in the Voice Library using Sesame
           * @request POST:/voice-library/create-sesame-voice
           * @secure
           */
          voiceLibraryControllerVoiceLibraryCreateSesameVoice: (data, params = {}) => this.request({
            path: `/voice-library/create-sesame-voice`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            ...params
          })
        });
        __publicField(this, "provider", {
          /**
           * No description
           *
           * @tags Providers
           * @name ProviderControllerGetWorkflows
           * @request GET:/{provider}/workflows
           * @secure
           */
          providerControllerGetWorkflows: (provider, query, params = {}) => this.request({
            path: `/${provider}/workflows`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name ProviderControllerGetWorkflowTriggerHook
           * @request GET:/{provider}/workflows/{workflowId}/hooks
           * @secure
           */
          providerControllerGetWorkflowTriggerHook: (provider, workflowId, params = {}) => this.request({
            path: `/${provider}/workflows/${workflowId}/hooks`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name ProviderControllerGetLocations
           * @request GET:/{provider}/locations
           * @secure
           */
          providerControllerGetLocations: (provider, params = {}) => this.request({
            path: `/${provider}/locations`,
            method: "GET",
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name VoiceProviderControllerSearchVoices
           * @summary Search Voice from Provider Voice Library.
           * @request GET:/{provider}/voices/search
           * @deprecated
           * @secure
           */
          voiceProviderControllerSearchVoices: (provider, query, params = {}) => this.request({
            path: `/${provider}/voices/search`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name VoiceProviderControllerSearchVoice
           * @summary Search Voice from Provider Voice Library.
           * @request GET:/{provider}/voice/search
           * @secure
           */
          voiceProviderControllerSearchVoice: (provider, query, params = {}) => this.request({
            path: `/${provider}/voice/search`,
            method: "GET",
            query,
            secure: true,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name VoiceProviderControllerAddVoices
           * @summary Add Shared Voice to your Provider Account.
           * @request POST:/{provider}/voices/add
           * @deprecated
           * @secure
           */
          voiceProviderControllerAddVoices: (provider, data, params = {}) => this.request({
            path: `/${provider}/voices/add`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          }),
          /**
           * No description
           *
           * @tags Providers
           * @name VoiceProviderControllerAddVoice
           * @summary Add Shared Voice to your Provider Account.
           * @request POST:/{provider}/voice/add
           * @secure
           */
          voiceProviderControllerAddVoice: (provider, data, params = {}) => this.request({
            path: `/${provider}/voice/add`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.Json,
            format: "json",
            ...params
          })
        });
        __publicField(this, "v11Labs", {
          /**
           * No description
           *
           * @tags Providers
           * @name VoiceProviderControllerCloneVoices
           * @summary Clone a voice to the provider account and add to Vapi Voice Library.
           * @request POST:/11labs/voice/clone
           * @secure
           */
          voiceProviderControllerCloneVoices: (data, params = {}) => this.request({
            path: `/11labs/voice/clone`,
            method: "POST",
            body: data,
            secure: true,
            type: ContentType.FormData,
            ...params
          })
        });
      }
    };
    exports.Api = Api;
  }
});

// node_modules/@vapi-ai/web/dist/client.js
var require_client = __commonJS({
  "node_modules/@vapi-ai/web/dist/client.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.client = void 0;
    var api_1 = require_api();
    var api = new api_1.Api({
      baseUrl: "https://api.vapi.ai",
      baseApiParams: {
        secure: true
      },
      securityWorker: async (securityData) => {
        if (securityData) {
          return {
            headers: {
              Authorization: `Bearer ${securityData}`
            }
          };
        }
      }
    });
    exports.client = api;
  }
});

// node_modules/@vapi-ai/web/dist/vapi.js
var require_vapi = __commonJS({
  "node_modules/@vapi-ai/web/dist/vapi.js"(exports) {
    var __importDefault = exports && exports.__importDefault || function(mod) {
      return mod && mod.__esModule ? mod : { "default": mod };
    };
    Object.defineProperty(exports, "__esModule", { value: true });
    var daily_js_1 = __importDefault((init_daily_esm(), __toCommonJS(daily_esm_exports)));
    var events_1 = __importDefault(require_events());
    var client_1 = require_client();
    async function startAudioPlayer(player, track2) {
      player.muted = false;
      player.autoplay = true;
      if (track2 != null) {
        player.srcObject = new MediaStream([track2]);
        await player.play();
      }
    }
    async function buildAudioPlayer(track2, participantId) {
      const player = document.createElement("audio");
      player.dataset.participantId = participantId;
      document.body.appendChild(player);
      await startAudioPlayer(player, track2);
      return player;
    }
    function destroyAudioPlayer(participantId) {
      const player = document.querySelector(`audio[data-participant-id="${participantId}"]`);
      player == null ? void 0 : player.remove();
    }
    function subscribeToTracks(e2, call, isVideoRecordingEnabled, isVideoEnabled) {
      if (e2.participant.local)
        return;
      call.updateParticipant(e2.participant.session_id, {
        setSubscribedTracks: {
          audio: true,
          video: isVideoRecordingEnabled || isVideoEnabled
        }
      });
    }
    var VapiEventEmitter = class extends events_1.default {
      on(event, listener) {
        super.on(event, listener);
        return this;
      }
      once(event, listener) {
        super.once(event, listener);
        return this;
      }
      emit(event, ...args) {
        return super.emit(event, ...args);
      }
      removeListener(event, listener) {
        super.removeListener(event, listener);
        return this;
      }
      removeAllListeners(event) {
        super.removeAllListeners(event);
        return this;
      }
    };
    var Vapi = class extends VapiEventEmitter {
      constructor(apiToken, apiBaseUrl, dailyCallConfig, dailyCallObject) {
        super();
        __publicField(this, "started", false);
        __publicField(this, "call", null);
        __publicField(this, "speakingTimeout", null);
        __publicField(this, "dailyCallConfig", {});
        __publicField(this, "dailyCallObject", {});
        __publicField(this, "hasEmittedCallEndedStatus", false);
        client_1.client.baseUrl = apiBaseUrl ?? "https://api.vapi.ai";
        client_1.client.setSecurityData(apiToken);
        this.dailyCallConfig = dailyCallConfig ?? {};
        this.dailyCallObject = dailyCallObject ?? {};
      }
      cleanup() {
        var _a2;
        this.started = false;
        this.hasEmittedCallEndedStatus = false;
        (_a2 = this.call) == null ? void 0 : _a2.destroy();
        this.call = null;
        this.speakingTimeout = null;
      }
      isMobileDevice() {
        if (typeof navigator === "undefined") {
          return false;
        }
        const userAgent = navigator.userAgent;
        return /android|iphone|ipad|ipod|iemobile|blackberry|bada/i.test(userAgent.toLowerCase());
      }
      async sleep(ms2) {
        return new Promise((resolve) => setTimeout(resolve, ms2));
      }
      async start(assistant, assistantOverrides, squad, workflow) {
        var _a2, _b, _c2, _d;
        if (!assistant && !squad && !workflow) {
          throw new Error("Assistant or Squad or Workflow must be provided.");
        }
        if (this.started) {
          return null;
        }
        this.started = true;
        try {
          const webCall = (await client_1.client.call.callControllerCreateWebCall({
            assistant: typeof assistant === "string" ? void 0 : assistant,
            assistantId: typeof assistant === "string" ? assistant : void 0,
            assistantOverrides,
            squad: typeof squad === "string" ? void 0 : squad,
            squadId: typeof squad === "string" ? squad : void 0,
            workflow: typeof workflow === "string" ? void 0 : workflow,
            workflowId: typeof workflow === "string" ? workflow : void 0
          })).data;
          if (this.call) {
            this.cleanup();
          }
          const isVideoRecordingEnabled = ((_a2 = webCall == null ? void 0 : webCall.artifactPlan) == null ? void 0 : _a2.videoRecordingEnabled) ?? false;
          const isVideoEnabled = ((_c2 = (_b = webCall == null ? void 0 : webCall.assistant) == null ? void 0 : _b.voice) == null ? void 0 : _c2.provider) === "tavus";
          this.call = daily_js_1.default.createCallObject({
            audioSource: this.dailyCallObject.audioSource ?? true,
            videoSource: this.dailyCallObject.videoSource ?? isVideoRecordingEnabled,
            dailyConfig: this.dailyCallConfig
          });
          (_d = this.call.iframe()) == null ? void 0 : _d.style.setProperty("display", "none");
          this.call.on("left-meeting", () => {
            var _a3;
            this.emit("call-end");
            if (!this.hasEmittedCallEndedStatus) {
              this.emit("message", {
                type: "status-update",
                status: "ended",
                "endedReason": "customer-ended-call"
              });
              this.hasEmittedCallEndedStatus = true;
            }
            if (isVideoRecordingEnabled) {
              (_a3 = this.call) == null ? void 0 : _a3.stopRecording();
            }
            this.cleanup();
          });
          this.call.on("error", (error) => {
            var _a3;
            this.emit("error", error);
            if (isVideoRecordingEnabled) {
              (_a3 = this.call) == null ? void 0 : _a3.stopRecording();
            }
          });
          this.call.on("camera-error", (error) => {
            this.emit("error", error);
          });
          this.call.on("track-started", async (e2) => {
            var _a3, _b2, _c3;
            if (!e2 || !e2.participant) {
              return;
            }
            if ((_a3 = e2.participant) == null ? void 0 : _a3.local) {
              return;
            }
            if (((_b2 = e2.participant) == null ? void 0 : _b2.user_name) !== "Vapi Speaker") {
              return;
            }
            if (e2.track.kind === "video") {
              this.emit("video", e2.track);
            }
            if (e2.track.kind === "audio") {
              await buildAudioPlayer(e2.track, e2.participant.session_id);
            }
            (_c3 = this.call) == null ? void 0 : _c3.sendAppMessage("playable");
          });
          this.call.on("participant-joined", (e2) => {
            if (!e2 || !this.call)
              return;
            subscribeToTracks(e2, this.call, isVideoRecordingEnabled, isVideoEnabled);
          });
          this.call.on("participant-updated", (e2) => {
            if (!e2) {
              return;
            }
            this.emit("daily-participant-updated", e2.participant);
          });
          this.call.on("participant-left", (e2) => {
            if (!e2) {
              return;
            }
            destroyAudioPlayer(e2.participant.session_id);
          });
          if (this.isMobileDevice()) {
            await this.sleep(1e3);
          }
          await this.call.join({
            // @ts-expect-error This exists
            url: webCall.webCallUrl,
            subscribeToTracksAutomatically: false
          });
          if (isVideoRecordingEnabled) {
            const recordingRequestedTime = (/* @__PURE__ */ new Date()).getTime();
            this.call.startRecording({
              width: 1280,
              height: 720,
              backgroundColor: "#FF1F2D3D",
              layout: {
                preset: "default"
              }
            });
            this.call.on("recording-started", () => {
              this.send({
                type: "control",
                control: "say-first-message",
                videoRecordingStartDelaySeconds: ((/* @__PURE__ */ new Date()).getTime() - recordingRequestedTime) / 1e3
              });
            });
          }
          this.call.startRemoteParticipantsAudioLevelObserver(100);
          this.call.on("remote-participants-audio-level", (e2) => {
            if (e2)
              this.handleRemoteParticipantsAudioLevel(e2);
          });
          this.call.on("app-message", (e2) => this.onAppMessage(e2));
          this.call.on("nonfatal-error", (e2) => {
            var _a3;
            if ((e2 == null ? void 0 : e2.type) === "audio-processor-error") {
              (_a3 = this.call) == null ? void 0 : _a3.updateInputSettings({
                audio: {
                  processor: {
                    type: "none"
                  }
                }
              }).then(() => {
                var _a4;
                (_a4 = this.call) == null ? void 0 : _a4.setLocalAudio(true);
              });
            }
          });
          this.call.updateInputSettings({
            audio: {
              processor: {
                type: "noise-cancellation"
              }
            }
          });
          return webCall;
        } catch (e2) {
          console.error(e2);
          this.emit("error", e2);
          this.cleanup();
          return null;
        }
      }
      onAppMessage(e2) {
        if (!e2) {
          return;
        }
        try {
          if (e2.data === "listening") {
            return this.emit("call-start");
          } else {
            try {
              const parsedMessage = JSON.parse(e2.data);
              this.emit("message", parsedMessage);
              if (parsedMessage && "type" in parsedMessage && "status" in parsedMessage && parsedMessage.type === "status-update" && parsedMessage.status === "ended") {
                this.hasEmittedCallEndedStatus = true;
              }
            } catch (parseError) {
              console.log("Error parsing message data: ", parseError);
            }
          }
        } catch (e3) {
          console.error(e3);
        }
      }
      handleRemoteParticipantsAudioLevel(e2) {
        const speechLevel = Object.values(e2.participantsAudioLevel).reduce((a2, b2) => a2 + b2, 0);
        this.emit("volume-level", Math.min(1, speechLevel / 0.15));
        const isSpeaking = speechLevel > 0.01;
        if (!isSpeaking) {
          return;
        }
        if (this.speakingTimeout) {
          clearTimeout(this.speakingTimeout);
          this.speakingTimeout = null;
        } else {
          this.emit("speech-start");
        }
        this.speakingTimeout = setTimeout(() => {
          this.emit("speech-end");
          this.speakingTimeout = null;
        }, 1e3);
      }
      stop() {
        var _a2;
        this.started = false;
        (_a2 = this.call) == null ? void 0 : _a2.destroy();
        this.call = null;
      }
      send(message) {
        var _a2;
        (_a2 = this.call) == null ? void 0 : _a2.sendAppMessage(JSON.stringify(message));
      }
      setMuted(mute) {
        if (!this.call) {
          throw new Error("Call object is not available.");
        }
        this.call.setLocalAudio(!mute);
      }
      isMuted() {
        if (!this.call) {
          return false;
        }
        return this.call.localAudio() === false;
      }
      say(message, endCallAfterSpoken, interruptionsEnabled) {
        this.send({
          type: "say",
          message,
          endCallAfterSpoken,
          interruptionsEnabled: interruptionsEnabled ?? false
        });
      }
      setInputDevicesAsync(options) {
        var _a2;
        (_a2 = this.call) == null ? void 0 : _a2.setInputDevicesAsync(options);
      }
      async increaseMicLevel(gain) {
        if (!this.call) {
          throw new Error("Call object is not available.");
        }
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
          const audioContext = new AudioContext();
          const source = audioContext.createMediaStreamSource(stream);
          const gainNode = audioContext.createGain();
          gainNode.gain.value = gain;
          source.connect(gainNode);
          const destination = audioContext.createMediaStreamDestination();
          gainNode.connect(destination);
          const [boostedTrack] = destination.stream.getAudioTracks();
          await this.call.setInputDevicesAsync({ audioSource: boostedTrack });
        } catch (error) {
          console.error("Error adjusting microphone level:", error);
        }
      }
      setOutputDeviceAsync(options) {
        var _a2;
        (_a2 = this.call) == null ? void 0 : _a2.setOutputDeviceAsync(options);
      }
      getDailyCallObject() {
        return this.call;
      }
      startScreenSharing(displayMediaOptions, screenVideoSendSettings) {
        var _a2;
        (_a2 = this.call) == null ? void 0 : _a2.startScreenShare({
          displayMediaOptions,
          screenVideoSendSettings
        });
      }
      stopScreenSharing() {
        var _a2;
        (_a2 = this.call) == null ? void 0 : _a2.stopScreenShare();
      }
    };
    exports.default = Vapi;
  }
});
export default require_vapi();
/*! Bundled license information:

@daily-co/daily-js/dist/daily-esm.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
//# sourceMappingURL=@vapi-ai_web.js.map
