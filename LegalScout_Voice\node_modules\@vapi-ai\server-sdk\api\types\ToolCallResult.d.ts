/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ToolCallResult {
    /**
     * This is the message that will be spoken to the user.
     *
     * If this is not returned, assistant will speak:
     * 1. a `request-complete` or `request-failed` message from `tool.messages`, if it exists
     * 2. a response generated by the model, if not
     */
    message?: Vapi.ToolCallResultMessageItem[];
    /** This is the name of the function the model called. */
    name: string;
    /** This is the unique identifier for the tool call. */
    toolCallId: string;
    /**
     * This is the result if the tool call was successful. This is added to the conversation history.
     *
     * Further, if this is returned, assistant will speak:
     * 1. the `message`, if it exists and is of type `request-complete`
     * 2. a `request-complete` message from `tool.messages`, if it exists
     * 3. a response generated by the model, if neither exist
     */
    result?: string;
    /**
     * This is the error if the tool call was not successful. This is added to the conversation history.
     *
     * Further, if this is returned, assistant will speak:
     * 1. the `message`, if it exists and is of type `request-failed`
     * 2. a `request-failed` message from `tool.messages`, if it exists
     * 3. a response generated by the model, if neither exist
     */
    error?: string;
}
