/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ApiRequest {
    type: "apiRequest";
    method: Vapi.ApiRequestMethod;
    /** Api endpoint to send requests to. */
    url: string;
    /**
     * These are the custom headers to include in the Api Request sent.
     *
     * Each key-value pair represents a header name and its value.
     */
    headers?: Vapi.JsonSchema;
    /**
     * This defined the JSON body of your Api Request. For example, if `body_schema`
     * included "my_field": "my_gather_statement.user_age", then the json body sent to the server would have that particular value assign to it.
     * Right now, only data from gather statements are supported.
     */
    body?: Vapi.JsonSchema;
    /**
     * This is the mode of the Api Request.
     * We only support BLOCKING and BACKGROUND for now.
     */
    mode: Vapi.ApiRequestMode;
    /**
     * This is a list of hooks for a task.
     * Each hook is a list of tasks to run on a trigger (such as on start, on failure, etc).
     * Only Say is supported for now.
     */
    hooks?: Vapi.Hook[];
    /** This is the schema for the outputs of the Api Request. */
    output?: Vapi.JsonSchema;
    name: string;
    /** This is for metadata you want to store on the task. */
    metadata?: Record<string, unknown>;
}
