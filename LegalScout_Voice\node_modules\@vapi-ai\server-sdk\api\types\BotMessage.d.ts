/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface BotMessage {
    /** The role of the bot in the conversation. */
    role: string;
    /** The message content from the bot. */
    message: string;
    /** The timestamp when the message was sent. */
    time: number;
    /** The timestamp when the message ended. */
    endTime: number;
    /** The number of seconds from the start of the conversation. */
    secondsFromStart: number;
    /** The source of the message. */
    source?: string;
    /** The duration of the message in seconds. */
    duration?: number;
}
