/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface AzureBlobStorageBucketPlan {
    /** This is the blob storage connection string for the Azure resource. */
    connectionString: string;
    /** This is the container name for the Azure blob storage. */
    containerName: string;
    /**
     * This is the path where call artifacts will be stored.
     *
     * Usage:
     * - To store call artifacts in a specific folder, set this to the full path. Eg. "/folder-name1/folder-name2".
     * - To store call artifacts in the root of the bucket, leave this blank.
     *
     * @default "/"
     */
    path?: string;
}
