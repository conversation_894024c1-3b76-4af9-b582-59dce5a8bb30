/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export type AssistantOverridesServerMessagesItem = "conversation-update" | "end-of-call-report" | "function-call" | "hang" | "language-changed" | "language-change-detected" | "model-output" | "phone-call-control" | "speech-update" | "status-update" | "transcript" | "transcript[transcriptType='final']" | "tool-calls" | "transfer-destination-request" | "transfer-update" | "user-interrupted" | "voice-input";
export declare const AssistantOverridesServerMessagesItem: {
    readonly ConversationUpdate: "conversation-update";
    readonly EndOfCallReport: "end-of-call-report";
    readonly FunctionCall: "function-call";
    readonly Hang: "hang";
    readonly LanguageChanged: "language-changed";
    readonly LanguageChangeDetected: "language-change-detected";
    readonly ModelOutput: "model-output";
    readonly PhoneCallControl: "phone-call-control";
    readonly SpeechUpdate: "speech-update";
    readonly StatusUpdate: "status-update";
    readonly Transcript: "transcript";
    readonly TranscriptTranscriptTypeFinal: "transcript[transcriptType='final']";
    readonly ToolCalls: "tool-calls";
    readonly TransferDestinationRequest: "transfer-destination-request";
    readonly TransferUpdate: "transfer-update";
    readonly UserInterrupted: "user-interrupted";
    readonly VoiceInput: "voice-input";
};
