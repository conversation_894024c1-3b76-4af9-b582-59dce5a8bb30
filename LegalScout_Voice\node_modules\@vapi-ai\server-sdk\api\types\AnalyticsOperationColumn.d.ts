/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This is the columns you want to perform the aggregation operation on.
 */
export type AnalyticsOperationColumn = "id" | "cost" | "costBreakdown.llm" | "costBreakdown.stt" | "costBreakdown.tts" | "costBreakdown.vapi" | "costBreakdown.ttsCharacters" | "costBreakdown.llmPromptTokens" | "costBreakdown.llmCompletionTokens" | "duration" | "concurrency" | "minutesUsed";
export declare const AnalyticsOperationColumn: {
    readonly Id: "id";
    readonly Cost: "cost";
    readonly CostBreakdownLlm: "costBreakdown.llm";
    readonly CostBreakdownStt: "costBreakdown.stt";
    readonly CostBreakdownTts: "costBreakdown.tts";
    readonly CostBreakdownVapi: "costBreakdown.vapi";
    readonly CostBreakdownTtsCharacters: "costBreakdown.ttsCharacters";
    readonly CostBreakdownLlmPromptTokens: "costBreakdown.llmPromptTokens";
    readonly CostBreakdownLlmCompletionTokens: "costBreakdown.llmCompletionTokens";
    readonly Duration: "duration";
    readonly Concurrency: "concurrency";
    readonly MinutesUsed: "minutesUsed";
};
