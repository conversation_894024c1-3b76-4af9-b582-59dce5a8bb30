/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface Analysis {
    /** This is the summary of the call. Customize by setting `assistant.analysisPlan.summaryPrompt`. */
    summary?: string;
    /** This is the structured data extracted from the call. Customize by setting `assistant.analysisPlan.structuredDataPrompt` and/or `assistant.analysisPlan.structuredDataSchema`. */
    structuredData?: Record<string, unknown>;
    /** This is the structured data catalog of the call. Customize by setting `assistant.analysisPlan.structuredDataMultiPlan`. */
    structuredDataMulti?: Record<string, unknown>[];
    /** This is the evaluation of the call. Customize by setting `assistant.analysisPlan.successEvaluationPrompt` and/or `assistant.analysisPlan.successEvaluationRubric`. */
    successEvaluation?: string;
}
