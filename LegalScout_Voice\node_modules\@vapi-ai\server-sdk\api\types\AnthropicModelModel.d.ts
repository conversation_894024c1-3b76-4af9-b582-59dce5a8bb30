/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
/**
 * The specific Anthropic/Claude model that will be used.
 */
export type AnthropicModelModel = "claude-3-opus-20240229" | "claude-3-sonnet-20240229" | "claude-3-haiku-20240307" | "claude-3-5-sonnet-20240620" | "claude-3-5-sonnet-20241022" | "claude-3-5-haiku-20241022" | "claude-3-7-sonnet-20250219";
export declare const AnthropicModelModel: {
    readonly <PERSON><PERSON><PERSON>20240229: "claude-3-opus-20240229";
    readonly <PERSON><PERSON><PERSON>20240229: "claude-3-sonnet-20240229";
    readonly <PERSON><PERSON><PERSON>20240307: "claude-3-haiku-20240307";
    readonly <PERSON>onnet20240620: "claude-3-5-sonnet-20240620";
    readonly <PERSON><PERSON>net20241022: "claude-3-5-sonnet-20241022";
    readonly <PERSON>20241022: "claude-3-5-haiku-20241022";
    readonly <PERSON><PERSON>20250219: "claude-3-7-sonnet-20250219";
};
