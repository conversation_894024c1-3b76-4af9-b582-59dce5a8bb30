/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface AnthropicModel {
    /** This is the starting state for the conversation. */
    messages?: Vapi.OpenAiMessage[];
    /**
     * These are the tools that the assistant can use during the call. To use existing tools, use `toolIds`.
     *
     * Both `tools` and `toolIds` can be used together.
     */
    tools?: Vapi.AnthropicModelToolsItem[];
    /**
     * These are the tools that the assistant can use during the call. To use transient tools, use `tools`.
     *
     * Both `tools` and `toolIds` can be used together.
     */
    toolIds?: string[];
    /** These are the options for the knowledge base. */
    knowledgeBase?: Vapi.CreateCustomKnowledgeBaseDto;
    /** This is the ID of the knowledge base the model will use. */
    knowledgeBaseId?: string;
    /** The specific Anthropic/Claude model that will be used. */
    model: Vapi.AnthropicModelModel;
    /** The provider identifier for Anthropic. */
    provider: "anthropic";
    /**
     * Optional configuration for Anthrop<PERSON>'s thinking feature.
     * Only applicable for claude-3-7-sonnet-20250219 model.
     * If provided, maxTokens must be greater than thinking.budgetTokens.
     */
    thinking?: Vapi.AnthropicThinkingConfig;
    /** This is the temperature that will be used for calls. Default is 0 to leverage caching for lower latency. */
    temperature?: number;
    /** This is the max number of tokens that the assistant will be allowed to generate in each turn of the conversation. Default is 250. */
    maxTokens?: number;
    /**
     * This determines whether we detect user's emotion while they speak and send it as an additional info to model.
     *
     * Default `false` because the model is usually are good at understanding the user's emotion from text.
     *
     * @default false
     */
    emotionRecognitionEnabled?: boolean;
    /**
     * This sets how many turns at the start of the conversation to use a smaller, faster model from the same provider before switching to the primary model. Example, gpt-3.5-turbo if provider is openai.
     *
     * Default is 0.
     *
     * @default 0
     */
    numFastTurns?: number;
}
