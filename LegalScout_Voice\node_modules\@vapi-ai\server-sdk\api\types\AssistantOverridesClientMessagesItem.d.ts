/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export type AssistantOverridesClientMessagesItem = "conversation-update" | "function-call" | "function-call-result" | "hang" | "language-changed" | "metadata" | "model-output" | "speech-update" | "status-update" | "transcript" | "tool-calls" | "tool-calls-result" | "transfer-update" | "user-interrupted" | "voice-input" | "workflow.node.started";
export declare const AssistantOverridesClientMessagesItem: {
    readonly ConversationUpdate: "conversation-update";
    readonly FunctionCall: "function-call";
    readonly FunctionCallResult: "function-call-result";
    readonly Hang: "hang";
    readonly LanguageChanged: "language-changed";
    readonly Metadata: "metadata";
    readonly ModelOutput: "model-output";
    readonly SpeechUpdate: "speech-update";
    readonly StatusUpdate: "status-update";
    readonly Transcript: "transcript";
    readonly ToolCalls: "tool-calls";
    readonly ToolCallsResult: "tool-calls-result";
    readonly TransferUpdate: "transfer-update";
    readonly UserInterrupted: "user-interrupted";
    readonly VoiceInput: "voice-input";
    readonly WorkflowNodeStarted: "workflow.node.started";
};
