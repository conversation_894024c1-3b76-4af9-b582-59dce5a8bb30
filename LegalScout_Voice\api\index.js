/**
 * Consolidated API Router for Vercel Hobby Plan
 * Routes all API requests to appropriate handlers to stay under 12 function limit
 */

// Import all your existing API handlers
import aiMetaMcp from './ai-meta-mcp.js';
import bugReport from './bug-report.js';
import env from './env.js';
import firecrawlSearch from './firecrawl-search.js';
import webSearch from './web-search.js';

// Import directory-based handlers
import vapiProxy from './vapi-proxy/index.js';
import assistantHandler from './assistant/[id].js';
// import vapiMcpServer from './vapi-mcp-server/index.js';
// import vapiMcpPath from './vapi-mcp-server/[path].js';
// import vapiCallWebhook from './webhook/vapi-call/index.js';

// Import sync tools
import checkPreviewConsistency from './sync-tools/check-preview-consistency.js';
import manageAuthState from './sync-tools/manage-auth-state.js';
import syncAttorneyProfile from './sync-tools/sync-attorney-profile.js';
import validateConfiguration from './sync-tools/validate-configuration.js';

// Import auth handlers
import authCallback from './auth/callback.js';

// Import website import handlers
import websiteImport from './website-import.js';
import debugWebsiteImport from './debug-website-import.js';

// Import subdomain utilities
import { extractSubdomainFromHost } from './utils/subdomainUtils.js';

export default async function handler(req, res) {
  const { url, method } = req;
  const path = new URL(url, `http://${req.headers.host}`).pathname;

  // Extract subdomain from host header for server-side operations
  const subdomain = extractSubdomainFromHost(req.headers.host);
  req.subdomain = subdomain;

  // Add CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, apikey, X-Client-Info, Prefer, Accept');
  res.setHeader('Access-Control-Max-Age', '86400');

  if (method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    // Route to appropriate handler based on path
    if (path.startsWith('/api/ai-meta-mcp')) {
      if (path === '/api/ai-meta-mcp') {
        return aiMetaMcp(req, res);
      } else {
        // Handle ai-meta-mcp sub-paths
        const subPath = path.replace('/api/ai-meta-mcp/', '');
        req.query = { ...req.query, path: subPath };
        return aiMetaMcp(req, res);
      }
    }

    if (path.startsWith('/api/assistant/')) {
      const id = path.replace('/api/assistant/', '');
      req.query = { ...req.query, id };
      return assistantHandler(req, res);
    }

    // if (path.startsWith('/api/vapi-mcp-server')) {
    //   if (path === '/api/vapi-mcp-server') {
    //     return vapiMcpServer(req, res);
    //   } else {
    //     const subPath = path.replace('/api/vapi-mcp-server/', '');
    //     req.query = { ...req.query, path: subPath };
    //     return vapiMcpPath(req, res);
    //   }
    // }

    if (path.startsWith('/api/vapi-proxy')) {
      return vapiProxy(req, res);
    }

    // if (path.startsWith('/api/webhook/vapi-call')) {
    //   return vapiCallWebhook(req, res);
    // }

    if (path.startsWith('/api/sync-tools/')) {
      const tool = path.replace('/api/sync-tools/', '');
      switch (tool) {
        case 'check-preview-consistency':
          return checkPreviewConsistency(req, res);
        case 'manage-auth-state':
          return manageAuthState(req, res);
        case 'sync-attorney-profile':
          return syncAttorneyProfile(req, res);
        case 'validate-configuration':
          return validateConfiguration(req, res);
        default:
          return res.status(404).json({ error: 'Sync tool not found' });
      }
    }

    // Direct API routes
    switch (path) {
      case '/api/bug-report':
        return bugReport(req, res);
      case '/api/env':
        return env(req, res);
      case '/api/firecrawl-search':
        return firecrawlSearch(req, res);
      case '/api/web-search':
        return webSearch(req, res);
      case '/api/auth/callback':
        return authCallback(req, res);
      case '/api/website-import':
        return websiteImport(req, res);
      case '/api/debug-website-import':
        return debugWebsiteImport(req, res);
      default:
        return res.status(404).json({ error: 'API endpoint not found' });
    }

  } catch (error) {
    console.error('API Router Error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
