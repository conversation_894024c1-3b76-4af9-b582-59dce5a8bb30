/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface BothCustomEndpointingRule {
    /**
     * This endpointing rule is based on both the last assistant message and the current customer message as they are speaking.
     *
     * Flow:
     * - Assistant speaks
     * - Customer starts speaking
     * - Customer transcription comes in
     * - This rule is evaluated on the last assistant message and the current customer transcription
     * - If assistant message matches `assistantRegex` AND customer message matches `customerRegex`, the endpointing timeout is set to `timeoutSeconds`
     *
     * Usage:
     * - If you want to wait longer while customer is speaking numbers, you can set a longer timeout.
     */
    type: "both";
    /**
     * This is the regex pattern to match the assistant's message.
     *
     * Note:
     * - This works by using the `RegExp.test` method in Node.JS. Eg. `/hello/.test("hello there")` will return `true`.
     *
     * Hot tip:
     * - In JavaScript, escape `\` when sending the regex pattern. Eg. `"hello\sthere"` will be sent over the wire as `"hellosthere"`. Send `"hello\\sthere"` instead.
     * - `RegExp.test` does substring matching, so `/cat/.test("I love cats")` will return `true`. To do full string matching, send "^cat$".
     */
    assistantRegex: string;
    /**
     * These are the options for the assistant's message regex match. Defaults to all disabled.
     *
     * @default []
     */
    assistantRegexOptions?: Vapi.RegexOption[];
    customerRegex: string;
    /**
     * These are the options for the customer's message regex match. Defaults to all disabled.
     *
     * @default []
     */
    customerRegexOptions?: Vapi.RegexOption[];
    /** This is the endpointing timeout in seconds, if the rule is matched. */
    timeoutSeconds: number;
}
