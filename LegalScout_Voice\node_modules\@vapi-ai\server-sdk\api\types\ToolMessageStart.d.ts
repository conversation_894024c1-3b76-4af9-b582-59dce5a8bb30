/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Vapi from "../index";
export interface ToolMessageStart {
    /**
     * This is an alternative to the `content` property. It allows to specify variants of the same content, one per language.
     *
     * Usage:
     * - If your assistants are multilingual, you can provide content for each language.
     * - If you don't provide content for a language, the first item in the array will be automatically translated to the active language at that moment.
     *
     * This will override the `content` property.
     */
    contents?: Vapi.TextContent[];
    /**
     * This message is triggered when the tool call starts.
     *
     * This message is never triggered for async tools.
     *
     * If this message is not provided, one of the default filler messages "Hold on a sec", "One moment", "Just a sec", "Give me a moment" or "This'll just take a sec" will be used.
     */
    type: "request-start";
    /**
     * This is an optional boolean that if true, the tool call will only trigger after the message is spoken. Default is false.
     *
     * @default false
     */
    blocking?: boolean;
    /** This is the content that the assistant says when this message is triggered. */
    content?: string;
    /** This is an optional array of conditions that the tool call arguments must meet in order for this message to be triggered. */
    conditions?: Vapi.Condition[];
}
