/**
 * Development Server for Local API Testing
 * Simulates Vercel's serverless function environment locally
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
dotenv.config();
try {
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('No .env.local file found, using only .env');
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.DEV_API_PORT || 3001;

// Enable CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept'],
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Handle preflight requests
app.options('*', (req, res) => {
  res.status(200).end();
});

// Import the main API handler
import apiHandler from './api/index.js';

// Route all /api requests to the consolidated handler
app.use('/api', async (req, res) => {
  try {
    // Create a mock Vercel request/response environment
    const mockReq = {
      ...req,
      url: req.url,
      method: req.method,
      headers: req.headers,
      body: req.body,
      query: req.query
    };

    const mockRes = {
      status: (code) => {
        res.status(code);
        return mockRes;
      },
      json: (data) => {
        res.json(data);
        return mockRes;
      },
      end: () => {
        res.end();
        return mockRes;
      },
      setHeader: (key, value) => {
        res.setHeader(key, value);
        return mockRes;
      }
    };

    await apiHandler(mockReq, mockRes);
  } catch (error) {
    console.error('Dev server API error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Development API server running on http://localhost:${PORT}`);
  console.log(`📡 API endpoints available at http://localhost:${PORT}/api/*`);
  console.log(`🔍 Health check: http://localhost:${PORT}/health`);
});
