/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface AssistantCustomEndpointingRule {
    /**
     * This endpointing rule is based on the last assistant message before customer started speaking.
     *
     * Flow:
     * - Assistant speaks
     * - Customer starts speaking
     * - Customer transcription comes in
     * - This rule is evaluated on the last assistant message
     * - If a match is found based on `regex`, the endpointing timeout is set to `timeoutSeconds`
     *
     * Usage:
     * - If you have yes/no questions in your use case like "are you interested in a loan?", you can set a shorter timeout.
     * - If you have questions where the customer may pause to look up information like "what's my account number?", you can set a longer timeout.
     */
    type: "assistant";
    /**
     * This is the regex pattern to match.
     *
     * Note:
     * - This works by using the `RegExp.test` method in Node.JS. Eg. `/hello/.test("hello there")` will return `true`.
     *
     * Hot tip:
     * - In JavaScript, escape `\` when sending the regex pattern. Eg. `"hello\sthere"` will be sent over the wire as `"hellosthere"`. Send `"hello\\sthere"` instead.
     * - `RegExp.test` does substring matching, so `/cat/.test("I love cats")` will return `true`. To do full string matching, send "^cat$".
     */
    regex: string;
    /**
     * These are the options for the regex match. Defaults to all disabled.
     *
     * @default []
     */
    regexOptions?: Vapi.RegexOption[];
    /** This is the endpointing timeout in seconds, if the rule is matched. */
    timeoutSeconds: number;
}
