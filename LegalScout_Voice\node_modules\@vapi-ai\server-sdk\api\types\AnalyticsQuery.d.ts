/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface AnalyticsQuery {
    /** This is the table you want to query. */
    table: Vapi.AnalyticsQueryTable;
    /** This is the list of columns you want to group by. */
    groupBy?: Vapi.AnalyticsQueryGroupByItem[];
    /** This is the name of the query. This will be used to identify the query in the response. */
    name: string;
    /** This is the time range for the query. */
    timeRange?: Vapi.TimeRange;
    /** This is the list of operations you want to perform. */
    operations: Vapi.AnalyticsOperation[];
}
