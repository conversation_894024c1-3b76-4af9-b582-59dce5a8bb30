{"name": "@vapi-ai/server-sdk", "version": "0.5.2", "private": false, "repository": "https://github.com/VapiAI/server-sdk-typescript", "main": "./index.js", "types": "./index.d.ts", "scripts": {"format": "prettier . --write --ignore-unknown", "build": "tsc", "prepack": "cp -rv dist/. .", "test": "jest"}, "dependencies": {"url-join": "4.0.1", "form-data": "^4.0.0", "formdata-node": "^6.0.3", "node-fetch": "^2.7.0", "qs": "^6.13.1", "readable-stream": "^4.5.2", "js-base64": "3.7.7", "form-data-encoder": "^4.0.2"}, "devDependencies": {"@types/url-join": "4.0.1", "@types/qs": "^6.9.17", "@types/node-fetch": "^2.6.12", "@types/readable-stream": "^4.0.18", "webpack": "^5.97.1", "ts-loader": "^9.5.1", "jest": "^29.7.0", "@types/jest": "^29.5.14", "ts-jest": "^29.1.1", "jest-environment-jsdom": "^29.7.0", "@types/node": "^18.19.70", "prettier": "^3.4.2", "typescript": "~5.7.2"}, "browser": {"fs": false, "os": false, "path": false}}