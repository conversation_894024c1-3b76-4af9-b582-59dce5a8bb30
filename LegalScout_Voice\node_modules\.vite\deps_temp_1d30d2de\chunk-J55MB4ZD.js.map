{"version": 3, "sources": ["../../pkce-challenge/dist/index.browser.js", "../../@modelcontextprotocol/sdk/src/shared/auth.ts", "../../@modelcontextprotocol/sdk/src/client/auth.ts", "../../eventsource-parser/src/errors.ts", "../../eventsource-parser/src/parse.ts"], "sourcesContent": ["let crypto;\ncrypto = globalThis.crypto; // web browsers\n/**\n * Creates an array of length `size` of random bytes\n * @param size\n * @returns Array of random ints (0 to 255)\n */\nasync function getRandomValues(size) {\n    return (await crypto).getRandomValues(new Uint8Array(size));\n}\n/** Generate cryptographically strong random string\n * @param size The desired length of the string\n * @returns The random string\n */\nasync function random(size) {\n    const mask = \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._~\";\n    let result = \"\";\n    const randomUints = await getRandomValues(size);\n    for (let i = 0; i < size; i++) {\n        // cap the value of the randomIndex to mask.length - 1\n        const randomIndex = randomUints[i] % mask.length;\n        result += mask[randomIndex];\n    }\n    return result;\n}\n/** Generate a PKCE challenge verifier\n * @param length Length of the verifier\n * @returns A random verifier `length` characters long\n */\nasync function generateVerifier(length) {\n    return await random(length);\n}\n/** Generate a PKCE code challenge from a code verifier\n * @param code_verifier\n * @returns The base64 url encoded code challenge\n */\nexport async function generateChallenge(code_verifier) {\n    const buffer = await (await crypto).subtle.digest(\"SHA-256\", new TextEncoder().encode(code_verifier));\n    // Generate base64url string\n    // btoa is deprecated in Node.js but is used here for web browser compatibility\n    // (which has no good replacement yet, see also https://github.com/whatwg/html/issues/6811)\n    return btoa(String.fromCharCode(...new Uint8Array(buffer)))\n        .replace(/\\//g, '_')\n        .replace(/\\+/g, '-')\n        .replace(/=/g, '');\n}\n/** Generate a PKCE challenge pair\n * @param length Length of the verifer (between 43-128). Defaults to 43.\n * @returns PKCE challenge pair\n */\nexport default async function pkceChallenge(length) {\n    if (!length)\n        length = 43;\n    if (length < 43 || length > 128) {\n        throw `Expected a length between 43 and 128. Received ${length}.`;\n    }\n    const verifier = await generateVerifier(length);\n    const challenge = await generateChallenge(verifier);\n    return {\n        code_verifier: verifier,\n        code_challenge: challenge,\n    };\n}\n/** Verify that a code_verifier produces the expected code challenge\n * @param code_verifier\n * @param expectedChallenge The code challenge to verify\n * @returns True if challenges are equal. False otherwise.\n */\nexport async function verifyChallenge(code_verifier, expectedChallenge) {\n    const actualChallenge = await generateChallenge(code_verifier);\n    return actualChallenge === expectedChallenge;\n}\n", null, null, "/**\n * The type of error that occurred.\n * @public\n */\nexport type ErrorType = 'invalid-retry' | 'unknown-field'\n\n/**\n * Error thrown when encountering an issue during parsing.\n *\n * @public\n */\nexport class ParseError extends Error {\n  /**\n   * The type of error that occurred.\n   */\n  type: ErrorType\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the field name.\n   */\n  field?: string\n\n  /**\n   * In the case of an unknown field encountered in the stream, this will be the value of the field.\n   */\n  value?: string\n\n  /**\n   * The line that caused the error, if available.\n   */\n  line?: string\n\n  constructor(\n    message: string,\n    options: {type: ErrorType; field?: string; value?: string; line?: string},\n  ) {\n    super(message)\n    this.name = 'ParseError'\n    this.type = options.type\n    this.field = options.field\n    this.value = options.value\n    this.line = options.line\n  }\n}\n", "/**\n * EventSource/Server-Sent Events parser\n * @see https://html.spec.whatwg.org/multipage/server-sent-events.html\n */\nimport {ParseError} from './errors.ts'\nimport type {EventSourceParser, ParserCallbacks} from './types.ts'\n\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction noop(_arg: unknown) {\n  // intentional noop\n}\n\n/**\n * Creates a new EventSource parser.\n *\n * @param callbacks - Callbacks to invoke on different parsing events:\n *   - `onEvent` when a new event is parsed\n *   - `onError` when an error occurs\n *   - `onRetry` when a new reconnection interval has been sent from the server\n *   - `onComment` when a comment is encountered in the stream\n *\n * @returns A new EventSource parser, with `parse` and `reset` methods.\n * @public\n */\nexport function createParser(callbacks: ParserCallbacks): EventSourceParser {\n  if (typeof callbacks === 'function') {\n    throw new TypeError(\n      '`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?',\n    )\n  }\n\n  const {onEvent = noop, onError = noop, onRetry = noop, onComment} = callbacks\n\n  let incompleteLine = ''\n\n  let isFirstChunk = true\n  let id: string | undefined\n  let data = ''\n  let eventType = ''\n\n  function feed(newChunk: string) {\n    // Strip any UTF8 byte order mark (BOM) at the start of the stream\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, '') : newChunk\n\n    // If there was a previous incomplete line, append it to the new chunk,\n    // so we may process it together as a new (hopefully complete) chunk.\n    const [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`)\n\n    for (const line of complete) {\n      parseLine(line)\n    }\n\n    incompleteLine = incomplete\n    isFirstChunk = false\n  }\n\n  function parseLine(line: string) {\n    // If the line is empty (a blank line), dispatch the event\n    if (line === '') {\n      dispatchEvent()\n      return\n    }\n\n    // If the line starts with a U+003A COLON character (:), ignore the line.\n    if (line.startsWith(':')) {\n      if (onComment) {\n        onComment(line.slice(line.startsWith(': ') ? 2 : 1))\n      }\n      return\n    }\n\n    // If the line contains a U+003A COLON character (:)\n    const fieldSeparatorIndex = line.indexOf(':')\n    if (fieldSeparatorIndex !== -1) {\n      // Collect the characters on the line before the first U+003A COLON character (:),\n      // and let `field` be that string.\n      const field = line.slice(0, fieldSeparatorIndex)\n\n      // Collect the characters on the line after the first U+003A COLON character (:),\n      // and let `value` be that string. If value starts with a U+0020 SPACE character,\n      // remove it from value.\n      const offset = line[fieldSeparatorIndex + 1] === ' ' ? 2 : 1\n      const value = line.slice(fieldSeparatorIndex + offset)\n\n      processField(field, value, line)\n      return\n    }\n\n    // Otherwise, the string is not empty but does not contain a U+003A COLON character (:)\n    // Process the field using the whole line as the field name, and an empty string as the field value.\n    // 👆 This is according to spec. That means that a line that has the value `data` will result in\n    // a newline being added to the current `data` buffer, for instance.\n    processField(line, '', line)\n  }\n\n  function processField(field: string, value: string, line: string) {\n    // Field names must be compared literally, with no case folding performed.\n    switch (field) {\n      case 'event':\n        // Set the `event type` buffer to field value\n        eventType = value\n        break\n      case 'data':\n        // Append the field value to the `data` buffer, then append a single U+000A LINE FEED(LF)\n        // character to the `data` buffer.\n        data = `${data}${value}\\n`\n        break\n      case 'id':\n        // If the field value does not contain U+0000 NULL, then set the `ID` buffer to\n        // the field value. Otherwise, ignore the field.\n        id = value.includes('\\0') ? undefined : value\n        break\n      case 'retry':\n        // If the field value consists of only ASCII digits, then interpret the field value as an\n        // integer in base ten, and set the event stream's reconnection time to that integer.\n        // Otherwise, ignore the field.\n        if (/^\\d+$/.test(value)) {\n          onRetry(parseInt(value, 10))\n        } else {\n          onError(\n            new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n              type: 'invalid-retry',\n              value,\n              line,\n            }),\n          )\n        }\n        break\n      default:\n        // Otherwise, the field is ignored.\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}…` : field}\"`,\n            {type: 'unknown-field', field, value, line},\n          ),\n        )\n        break\n    }\n  }\n\n  function dispatchEvent() {\n    const shouldDispatch = data.length > 0\n    if (shouldDispatch) {\n      onEvent({\n        id,\n        event: eventType || undefined,\n        // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n        // then remove the last character from the data buffer.\n        data: data.endsWith('\\n') ? data.slice(0, -1) : data,\n      })\n    }\n\n    // Reset for the next event\n    id = undefined\n    data = ''\n    eventType = ''\n  }\n\n  function reset(options: {consume?: boolean} = {}) {\n    if (incompleteLine && options.consume) {\n      parseLine(incompleteLine)\n    }\n\n    isFirstChunk = true\n    id = undefined\n    data = ''\n    eventType = ''\n    incompleteLine = ''\n  }\n\n  return {feed, reset}\n}\n\n/**\n * For the given `chunk`, split it into lines according to spec, and return any remaining incomplete line.\n *\n * @param chunk - The chunk to split into lines\n * @returns A tuple containing an array of complete lines, and any remaining incomplete line\n * @internal\n */\nfunction splitLines(chunk: string): [complete: Array<string>, incomplete: string] {\n  /**\n   * According to the spec, a line is terminated by either:\n   * - U+000D CARRIAGE RETURN U+000A LINE FEED (CRLF) character pair\n   * - a single U+000A LINE FEED(LF) character not preceded by a U+000D CARRIAGE RETURN(CR) character\n   * - a single U+000D CARRIAGE RETURN(CR) character not followed by a U+000A LINE FEED(LF) character\n   */\n  const lines: Array<string> = []\n  let incompleteLine = ''\n  let searchIndex = 0\n\n  while (searchIndex < chunk.length) {\n    // Find next line terminator\n    const crIndex = chunk.indexOf('\\r', searchIndex)\n    const lfIndex = chunk.indexOf('\\n', searchIndex)\n\n    // Determine line end\n    let lineEnd = -1\n    if (crIndex !== -1 && lfIndex !== -1) {\n      // CRLF case\n      lineEnd = Math.min(crIndex, lfIndex)\n    } else if (crIndex !== -1) {\n      lineEnd = crIndex\n    } else if (lfIndex !== -1) {\n      lineEnd = lfIndex\n    }\n\n    // Extract line if terminator found\n    if (lineEnd === -1) {\n      // No terminator found, rest is incomplete\n      incompleteLine = chunk.slice(searchIndex)\n      break\n    } else {\n      const line = chunk.slice(searchIndex, lineEnd)\n      lines.push(line)\n\n      // Move past line terminator\n      searchIndex = lineEnd + 1\n      if (chunk[searchIndex - 1] === '\\r' && chunk[searchIndex] === '\\n') {\n        searchIndex++\n      }\n    }\n  }\n\n  return [lines, incompleteLine]\n}\n"], "mappings": ";;;;;;AAAA,IAAI;AACJ,SAAS,WAAW;AAMpB,eAAe,gBAAgB,MAAM;AACjC,UAAQ,MAAM,QAAQ,gBAAgB,IAAI,WAAW,IAAI,CAAC;AAC9D;AAKA,eAAe,OAAO,MAAM;AACxB,QAAM,OAAO;AACb,MAAI,SAAS;AACb,QAAM,cAAc,MAAM,gBAAgB,IAAI;AAC9C,WAAS,IAAI,GAAG,IAAI,MAAM,KAAK;AAE3B,UAAM,cAAc,YAAY,CAAC,IAAI,KAAK;AAC1C,cAAU,KAAK,WAAW;AAAA,EAC9B;AACA,SAAO;AACX;AAKA,eAAe,iBAAiB,QAAQ;AACpC,SAAO,MAAM,OAAO,MAAM;AAC9B;AAKA,eAAsB,kBAAkB,eAAe;AACnD,QAAM,SAAS,OAAO,MAAM,QAAQ,OAAO,OAAO,WAAW,IAAI,YAAY,EAAE,OAAO,aAAa,CAAC;AAIpG,SAAO,KAAK,OAAO,aAAa,GAAG,IAAI,WAAW,MAAM,CAAC,CAAC,EACrD,QAAQ,OAAO,GAAG,EAClB,QAAQ,OAAO,GAAG,EAClB,QAAQ,MAAM,EAAE;AACzB;AAKA,eAAO,cAAqC,QAAQ;AAChD,MAAI,CAAC;AACD,aAAS;AACb,MAAI,SAAS,MAAM,SAAS,KAAK;AAC7B,UAAM,kDAAkD,MAAM;AAAA,EAClE;AACA,QAAM,WAAW,MAAM,iBAAiB,MAAM;AAC9C,QAAM,YAAY,MAAM,kBAAkB,QAAQ;AAClD,SAAO;AAAA,IACH,eAAe;AAAA,IACf,gBAAgB;AAAA,EACpB;AACJ;;;ACzDO,IAAM,uCAAuC,EACjD,OAAO;EACN,UAAU,EAAE,OAAM,EAAG,IAAG;EACxB,uBAAuB,EAAE,MAAM,EAAE,OAAM,EAAG,IAAG,CAAE,EAAE,SAAQ;EACzD,UAAU,EAAE,OAAM,EAAG,IAAG,EAAG,SAAQ;EACnC,kBAAkB,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EAC9C,0BAA0B,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACtD,uCAAuC,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACnE,eAAe,EAAE,OAAM,EAAG,SAAQ;EAClC,wBAAwB,EAAE,OAAM,EAAG,SAAQ;EAC3C,qBAAqB,EAAE,OAAM,EAAG,IAAG,EAAG,SAAQ;EAC9C,kBAAkB,EAAE,OAAM,EAAG,IAAG,EAAG,SAAQ;EAC3C,4CAA4C,EAAE,QAAO,EAAG,SAAQ;EAChE,uCAAuC,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACnE,mCAAmC,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EAC/D,mCAAmC,EAAE,QAAO,EAAG,SAAQ;CACxD,EACA,YAAW;AAKP,IAAM,sBAAsB,EAChC,OAAO;EACN,QAAQ,EAAE,OAAM;EAChB,wBAAwB,EAAE,OAAM;EAChC,gBAAgB,EAAE,OAAM;EACxB,uBAAuB,EAAE,OAAM,EAAG,SAAQ;EAC1C,kBAAkB,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EAC9C,0BAA0B,EAAE,MAAM,EAAE,OAAM,CAAE;EAC5C,0BAA0B,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACtD,uBAAuB,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACnD,uCAAuC,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACnE,kDAAkD,EAC/C,MAAM,EAAE,OAAM,CAAE,EAChB,SAAQ;EACX,uBAAuB,EAAE,OAAM,EAAG,SAAQ;EAC1C,qBAAqB,EAAE,OAAM,EAAG,SAAQ;EACxC,4CAA4C,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACxE,uDAAuD,EACpD,MAAM,EAAE,OAAM,CAAE,EAChB,SAAQ;EACX,wBAAwB,EAAE,OAAM,EAAG,SAAQ;EAC3C,+CAA+C,EAC5C,MAAM,EAAE,OAAM,CAAE,EAChB,SAAQ;EACX,0DAA0D,EACvD,MAAM,EAAE,OAAM,CAAE,EAChB,SAAQ;EACX,kCAAkC,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;CAC/D,EACA,YAAW;AAKP,IAAM,oBAAoB,EAC9B,OAAO;EACN,cAAc,EAAE,OAAM;EACtB,YAAY,EAAE,OAAM;EACpB,YAAY,EAAE,OAAM,EAAG,SAAQ;EAC/B,OAAO,EAAE,OAAM,EAAG,SAAQ;EAC1B,eAAe,EAAE,OAAM,EAAG,SAAQ;CACnC,EACA,MAAK;AAKD,IAAM,2BAA2B,EACrC,OAAO;EACN,OAAO,EAAE,OAAM;EACf,mBAAmB,EAAE,OAAM,EAAG,SAAQ;EACtC,WAAW,EAAE,OAAM,EAAG,SAAQ;CAC/B;AAKI,IAAM,4BAA4B,EAAE,OAAO;EAChD,eAAe,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,OAAO,CAAC,SAAS,KAAK,MAAM,CAAC,QAAQ,IAAI,SAAS,GAAG,CAAC,GAAG,EAAE,SAAS,wCAAuC,CAAE;EAChJ,4BAA4B,EAAE,OAAM,EAAG,SAAQ;EAC/C,aAAa,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACzC,gBAAgB,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EAC5C,aAAa,EAAE,OAAM,EAAG,SAAQ;EAChC,YAAY,EAAE,OAAM,EAAG,SAAQ;EAC/B,UAAU,EAAE,OAAM,EAAG,SAAQ;EAC7B,OAAO,EAAE,OAAM,EAAG,SAAQ;EAC1B,UAAU,EAAE,MAAM,EAAE,OAAM,CAAE,EAAE,SAAQ;EACtC,SAAS,EAAE,OAAM,EAAG,SAAQ;EAC5B,YAAY,EAAE,OAAM,EAAG,SAAQ;EAC/B,UAAU,EAAE,OAAM,EAAG,SAAQ;EAC7B,MAAM,EAAE,IAAG,EAAG,SAAQ;EACtB,aAAa,EAAE,OAAM,EAAG,SAAQ;EAChC,kBAAkB,EAAE,OAAM,EAAG,SAAQ;CACtC,EAAE,MAAK;AAKD,IAAM,+BAA+B,EAAE,OAAO;EACnD,WAAW,EAAE,OAAM;EACnB,eAAe,EAAE,OAAM,EAAG,SAAQ;EAClC,qBAAqB,EAAE,OAAM,EAAG,SAAQ;EACxC,0BAA0B,EAAE,OAAM,EAAG,SAAQ;CAC9C,EAAE,MAAK;AAKD,IAAM,mCAAmC,0BAA0B,MAAM,4BAA4B;AAKrG,IAAM,qCAAqC,EAAE,OAAO;EACzD,OAAO,EAAE,OAAM;EACf,mBAAmB,EAAE,OAAM,EAAG,SAAQ;CACvC,EAAE,MAAK;AAKD,IAAM,oCAAoC,EAAE,OAAO;EACxD,OAAO,EAAE,OAAM;EACf,iBAAiB,EAAE,OAAM,EAAG,SAAQ;CACrC,EAAE,MAAK;;;ACtDF,IAAO,oBAAP,cAAiC,MAAK;EAC1C,YAAY,SAAgB;AAC1B,UAAM,YAAO,QAAP,YAAO,SAAP,UAAW,cAAc;EACjC;;AASF,eAAsB,KACpB,UACA,EAAE,WACA,mBACA,OACA,oBAAmB,GAKQ;AAE7B,MAAI,yBAAyB;AAC7B,MAAI;AACF,UAAM,mBAAmB,MAAM,uCAC7B,uBAAuB,SAAS;AAElC,QAAI,iBAAiB,yBAAyB,iBAAiB,sBAAsB,SAAS,GAAG;AAC/F,+BAAyB,iBAAiB,sBAAsB,CAAC;IACnE;EACF,SAAS,OAAO;AACd,YAAQ,KAAK,6GAA6G,KAAK;EACjI;AAEA,QAAM,WAAW,MAAM,sBAAsB,sBAAsB;AAGnE,MAAI,oBAAoB,MAAM,QAAQ,QAAQ,SAAS,kBAAiB,CAAE;AAC1E,MAAI,CAAC,mBAAmB;AACtB,QAAI,sBAAsB,QAAW;AACnC,YAAM,IAAI,MAAM,qFAAqF;IACvG;AAEA,QAAI,CAAC,SAAS,uBAAuB;AACnC,YAAM,IAAI,MAAM,oEAAoE;IACtF;AAEA,UAAM,kBAAkB,MAAM,eAAe,wBAAwB;MACnE;MACA,gBAAgB,SAAS;KAC1B;AAED,UAAM,SAAS,sBAAsB,eAAe;AACpD,wBAAoB;EACtB;AAGA,MAAI,sBAAsB,QAAW;AACnC,UAAMA,gBAAe,MAAM,SAAS,aAAY;AAChD,UAAMC,UAAS,MAAM,sBAAsB,wBAAwB;MACjE;MACA;MACA;MACA,cAAAD;MACA,aAAa,SAAS;KACvB;AAED,UAAM,SAAS,WAAWC,OAAM;AAChC,WAAO;EACT;AAEA,QAAM,SAAS,MAAM,SAAS,OAAM;AAGpC,MAAI,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,eAAe;AACzB,QAAI;AAEF,YAAM,YAAY,MAAM,qBAAqB,wBAAwB;QACnE;QACA;QACA,cAAc,OAAO;OACtB;AAED,YAAM,SAAS,WAAW,SAAS;AACnC,aAAO;IACT,SAAS,OAAO;AACd,cAAQ,MAAM,mCAAmC,KAAK;IACxD;EACF;AAEA,QAAM,QAAQ,SAAS,QAAQ,MAAM,SAAS,MAAK,IAAK;AAGxD,QAAM,EAAE,kBAAkB,aAAY,IAAK,MAAM,mBAAmB,wBAAwB;IAC1F;IACA;IACA;IACA,aAAa,SAAS;IACtB,OAAO,SAAS,SAAS,eAAe;GACzC;AAED,QAAM,SAAS,iBAAiB,YAAY;AAC5C,QAAM,SAAS,wBAAwB,gBAAgB;AACvD,SAAO;AACT;AAKM,SAAU,2BAA2B,KAAa;AAEtD,QAAM,qBAAqB,IAAI,QAAQ,IAAI,kBAAkB;AAC7D,MAAI,CAAC,oBAAoB;AACvB,WAAO;EACT;AAEA,QAAM,CAAC,MAAM,MAAM,IAAI,mBAAmB,MAAM,GAAG;AACnD,MAAI,KAAK,YAAW,MAAO,YAAY,CAAC,QAAQ;AAC9C,YAAQ,IAAI,2DAA2D;AACvE,WAAO;EACT;AACA,QAAM,QAAQ;AACd,QAAM,QAAQ,MAAM,KAAK,kBAAkB;AAE3C,MAAI,CAAC,OAAO;AACV,WAAO;EACT;AAEA,MAAI;AACF,WAAO,IAAI,IAAI,MAAM,CAAC,CAAC;EACzB,SAAE,IAAM;AACN,YAAQ,IAAI,mCAAmC,MAAM,CAAC,CAAC;AACvD,WAAO;EACT;AACF;AAQA,eAAsB,uCACpB,WACA,MAAuE;;AAGvE,MAAI;AACJ,MAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,qBAAqB;AAC7B,UAAM,IAAI,IAAI,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,mBAAmB;EACzC,OAAO;AACL,UAAM,IAAI,IAAI,yCAAyC,SAAS;EAClE;AAEA,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,MAAM,KAAK;MAC1B,SAAS;QACP,yBAAwB,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,qBAAe,QAAA,OAAA,SAAA,KAAI;;KAEpD;EACH,SAAS,OAAO;AAEd,QAAI,iBAAiB,WAAW;AAC9B,iBAAW,MAAM,MAAM,GAAG;IAC5B,OAAO;AACL,YAAM;IACR;EACF;AAEA,MAAI,SAAS,WAAW,KAAK;AAC3B,UAAM,IAAI,MAAM,2EAA2E;EAC7F;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,QAAQ,SAAS,MAAM,+DAA+D;EAE1F;AACA,SAAO,qCAAqC,MAAM,MAAM,SAAS,KAAI,CAAE;AACzE;AAQA,eAAsB,sBACpB,wBACA,MAAmC;;AAEnC,QAAM,MAAM,IAAI,IAAI,2CAA2C,sBAAsB;AACrF,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,MAAM,KAAK;MAC1B,SAAS;QACP,yBAAwB,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,qBAAe,QAAA,OAAA,SAAA,KAAI;;KAEpD;EACH,SAAS,OAAO;AAEd,QAAI,iBAAiB,WAAW;AAC9B,iBAAW,MAAM,MAAM,GAAG;IAC5B,OAAO;AACL,YAAM;IACR;EACF;AAEA,MAAI,SAAS,WAAW,KAAK;AAC3B,WAAO;EACT;AAEA,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MACR,QAAQ,SAAS,MAAM,2CAA2C;EAEtE;AAEA,SAAO,oBAAoB,MAAM,MAAM,SAAS,KAAI,CAAE;AACxD;AAKA,eAAsB,mBACpB,wBACA,EACE,UACA,mBACA,aACA,OACA,MAAK,GAON;AAED,QAAM,eAAe;AACrB,QAAM,sBAAsB;AAE5B,MAAI;AACJ,MAAI,UAAU;AACZ,uBAAmB,IAAI,IAAI,SAAS,sBAAsB;AAE1D,QAAI,CAAC,SAAS,yBAAyB,SAAS,YAAY,GAAG;AAC7D,YAAM,IAAI,MACR,4DAA4D,YAAY,EAAE;IAE9E;AAEA,QACE,CAAC,SAAS,oCACV,CAAC,SAAS,iCAAiC,SAAS,mBAAmB,GACvE;AACA,YAAM,IAAI,MACR,oEAAoE,mBAAmB,EAAE;IAE7F;EACF,OAAO;AACL,uBAAmB,IAAI,IAAI,cAAc,sBAAsB;EACjE;AAGA,QAAM,YAAY,MAAM,cAAa;AACrC,QAAM,eAAe,UAAU;AAC/B,QAAM,gBAAgB,UAAU;AAEhC,mBAAiB,aAAa,IAAI,iBAAiB,YAAY;AAC/D,mBAAiB,aAAa,IAAI,aAAa,kBAAkB,SAAS;AAC1E,mBAAiB,aAAa,IAAI,kBAAkB,aAAa;AACjE,mBAAiB,aAAa,IAC5B,yBACA,mBAAmB;AAErB,mBAAiB,aAAa,IAAI,gBAAgB,OAAO,WAAW,CAAC;AAErE,MAAI,OAAO;AACT,qBAAiB,aAAa,IAAI,SAAS,KAAK;EAClD;AAEA,MAAI,OAAO;AACT,qBAAiB,aAAa,IAAI,SAAS,KAAK;EAClD;AAEA,SAAO,EAAE,kBAAkB,aAAY;AACzC;AAKA,eAAsB,sBACpB,wBACA,EACE,UACA,mBACA,mBACA,cACA,YAAW,GAOZ;AAED,QAAM,YAAY;AAElB,MAAI;AACJ,MAAI,UAAU;AACZ,eAAW,IAAI,IAAI,SAAS,cAAc;AAE1C,QACE,SAAS,yBACT,CAAC,SAAS,sBAAsB,SAAS,SAAS,GAClD;AACA,YAAM,IAAI,MACR,yDAAyD,SAAS,EAAE;IAExE;EACF,OAAO;AACL,eAAW,IAAI,IAAI,UAAU,sBAAsB;EACrD;AAGA,QAAM,SAAS,IAAI,gBAAgB;IACjC,YAAY;IACZ,WAAW,kBAAkB;IAC7B,MAAM;IACN,eAAe;IACf,cAAc,OAAO,WAAW;GACjC;AAED,MAAI,kBAAkB,eAAe;AACnC,WAAO,IAAI,iBAAiB,kBAAkB,aAAa;EAC7D;AAEA,QAAM,WAAW,MAAM,MAAM,UAAU;IACrC,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM;GACP;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,+BAA+B,SAAS,MAAM,EAAE;EAClE;AAEA,SAAO,kBAAkB,MAAM,MAAM,SAAS,KAAI,CAAE;AACtD;AAKA,eAAsB,qBACpB,wBACA,EACE,UACA,mBACA,aAAY,GAKb;AAED,QAAM,YAAY;AAElB,MAAI;AACJ,MAAI,UAAU;AACZ,eAAW,IAAI,IAAI,SAAS,cAAc;AAE1C,QACE,SAAS,yBACT,CAAC,SAAS,sBAAsB,SAAS,SAAS,GAClD;AACA,YAAM,IAAI,MACR,yDAAyD,SAAS,EAAE;IAExE;EACF,OAAO;AACL,eAAW,IAAI,IAAI,UAAU,sBAAsB;EACrD;AAGA,QAAM,SAAS,IAAI,gBAAgB;IACjC,YAAY;IACZ,WAAW,kBAAkB;IAC7B,eAAe;GAChB;AAED,MAAI,kBAAkB,eAAe;AACnC,WAAO,IAAI,iBAAiB,kBAAkB,aAAa;EAC7D;AAEA,QAAM,WAAW,MAAM,MAAM,UAAU;IACrC,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM;GACP;AACD,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,8BAA8B,SAAS,MAAM,EAAE;EACjE;AAEA,SAAO,kBAAkB,MAAM,EAAE,eAAe,cAAc,GAAI,MAAM,SAAS,KAAI,EAAG,CAAE;AAC5F;AAKA,eAAsB,eACpB,wBACA,EACE,UACA,eAAc,GAIf;AAED,MAAI;AAEJ,MAAI,UAAU;AACZ,QAAI,CAAC,SAAS,uBAAuB;AACnC,YAAM,IAAI,MAAM,wEAAwE;IAC1F;AAEA,sBAAkB,IAAI,IAAI,SAAS,qBAAqB;EAC1D,OAAO;AACL,sBAAkB,IAAI,IAAI,aAAa,sBAAsB;EAC/D;AAEA,QAAM,WAAW,MAAM,MAAM,iBAAiB;IAC5C,QAAQ;IACR,SAAS;MACP,gBAAgB;;IAElB,MAAM,KAAK,UAAU,cAAc;GACpC;AAED,MAAI,CAAC,SAAS,IAAI;AAChB,UAAM,IAAI,MAAM,4CAA4C,SAAS,MAAM,EAAE;EAC/E;AAEA,SAAO,iCAAiC,MAAM,MAAM,SAAS,KAAI,CAAE;AACrE;;;ACxgBO,IAAM,aAAN,cAAyB,MAAM;EAqBpC,YACE,SACA,SACA;AACA,UAAM,OAAO,GACb,KAAK,OAAO,cACZ,KAAK,OAAO,QAAQ,MACpB,KAAK,QAAQ,QAAQ,OACrB,KAAK,QAAQ,QAAQ,OACrB,KAAK,OAAO,QAAQ;EAAA;AAExB;ACnCA,SAAS,KAAK,MAAe;AAE7B;AAcO,SAAS,aAAa,WAA+C;AAC1E,MAAI,OAAO,aAAc;AACvB,UAAM,IAAI;MACR;IACF;AAGI,QAAA,EAAC,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,UAAA,IAAa;AAEpE,MAAI,iBAAiB,IAEjB,eAAe,MACf,IACA,OAAO,IACP,YAAY;AAEhB,WAAS,KAAK,UAAkB;AAE9B,UAAM,QAAQ,eAAe,SAAS,QAAQ,iBAAiB,EAAE,IAAI,UAI/D,CAAC,UAAU,UAAU,IAAI,WAAW,GAAG,cAAc,GAAG,KAAK,EAAE;AAErE,eAAW,QAAQ;AACjB,gBAAU,IAAI;AAGhB,qBAAiB,YACjB,eAAe;EAAA;AAGjB,WAAS,UAAU,MAAc;AAE/B,QAAI,SAAS,IAAI;AACD,oBAAA;AACd;IAAA;AAIE,QAAA,KAAK,WAAW,GAAG,GAAG;AACpB,mBACF,UAAU,KAAK,MAAM,KAAK,WAAW,IAAI,IAAI,IAAI,CAAC,CAAC;AAErD;IAAA;AAII,UAAA,sBAAsB,KAAK,QAAQ,GAAG;AAC5C,QAAI,wBAAwB,IAAI;AAG9B,YAAM,QAAQ,KAAK,MAAM,GAAG,mBAAmB,GAKzC,SAAS,KAAK,sBAAsB,CAAC,MAAM,MAAM,IAAI,GACrD,QAAQ,KAAK,MAAM,sBAAsB,MAAM;AAExC,mBAAA,OAAO,OAAO,IAAI;AAC/B;IAAA;AAOW,iBAAA,MAAM,IAAI,IAAI;EAAA;AAGpB,WAAA,aAAa,OAAe,OAAe,MAAc;AAEhE,YAAQ,OAAO;MACb,KAAK;AAES,oBAAA;AACZ;MACF,KAAK;AAGI,eAAA,GAAG,IAAI,GAAG,KAAK;;AACtB;MACF,KAAK;AAGH,aAAK,MAAM,SAAS,IAAI,IAAI,SAAY;AACxC;MACF,KAAK;AAIC,gBAAQ,KAAK,KAAK,IACpB,QAAQ,SAAS,OAAO,EAAE,CAAC,IAE3B;UACE,IAAI,WAAW,6BAA6B,KAAK,KAAK;YACpD,MAAM;YACN;YACA;UACD,CAAA;QACH;AAEF;MACF;AAEE;UACE,IAAI;YACF,kBAAkB,MAAM,SAAS,KAAK,GAAG,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,KAAK;YACtE,EAAC,MAAM,iBAAiB,OAAO,OAAO,KAAI;UAAA;QAE9C;AACA;IAAA;EACJ;AAGF,WAAS,gBAAgB;AACA,SAAK,SAAS,KAEnC,QAAQ;MACN;MACA,OAAO,aAAa;;;MAGpB,MAAM,KAAK,SAAS;CAAI,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI;IAAA,CACjD,GAIH,KAAK,QACL,OAAO,IACP,YAAY;EAAA;AAGL,WAAA,MAAM,UAA+B,CAAA,GAAI;AAC5C,sBAAkB,QAAQ,WAC5B,UAAU,cAAc,GAG1B,eAAe,MACf,KAAK,QACL,OAAO,IACP,YAAY,IACZ,iBAAiB;EAAA;AAGZ,SAAA,EAAC,MAAM,MAAK;AACrB;AASA,SAAS,WAAW,OAA8D;AAOhF,QAAM,QAAuB,CAAC;AAC1B,MAAA,iBAAiB,IACjB,cAAc;AAEX,SAAA,cAAc,MAAM,UAAQ;AAE3B,UAAA,UAAU,MAAM,QAAQ,MAAM,WAAW,GACzC,UAAU,MAAM,QAAQ;GAAM,WAAW;AAG/C,QAAI,UAAU;AAWd,QAVI,YAAY,MAAM,YAAY,KAEhC,UAAU,KAAK,IAAI,SAAS,OAAO,IAC1B,YAAY,KACrB,UAAU,UACD,YAAY,OACrB,UAAU,UAIR,YAAY,IAAI;AAED,uBAAA,MAAM,MAAM,WAAW;AACxC;IAAA,OACK;AACL,YAAM,OAAO,MAAM,MAAM,aAAa,OAAO;AAC7C,YAAM,KAAK,IAAI,GAGf,cAAc,UAAU,GACpB,MAAM,cAAc,CAAC,MAAM,QAAQ,MAAM,WAAW,MAAM;KAC5D;IAAA;EAEJ;AAGK,SAAA,CAAC,OAAO,cAAc;AAC/B;", "names": ["codeVerifier", "tokens"]}