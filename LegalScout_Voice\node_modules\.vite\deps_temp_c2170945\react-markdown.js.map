{"version": 3, "sources": ["../../inline-style-parser/index.js", "../../style-to-object/src/index.ts", "../../style-to-js/src/utilities.ts", "../../style-to-js/src/index.ts", "../../ms/index.js", "../../debug/src/common.js", "../../debug/src/browser.js", "../../extend/index.js", "../../estree-util-is-identifier-name/lib/index.js", "../../hast-util-whitespace/lib/index.js", "../../hast-util-to-jsx-runtime/lib/index.js", "../../unist-util-stringify-position/lib/index.js", "../../vfile-message/lib/index.js", "../../html-url-attributes/lib/index.js", "../../react-markdown/lib/index.js", "../../micromark/dev/lib/compile.js", "../../micromark/dev/lib/initialize/content.js", "../../micromark/dev/lib/initialize/document.js", "../../micromark/dev/lib/initialize/flow.js", "../../micromark/dev/lib/initialize/text.js", "../../micromark/dev/lib/constructs.js", "../../micromark/dev/lib/create-tokenizer.js", "../../micromark/dev/lib/parse.js", "../../micromark/dev/lib/postprocess.js", "../../micromark/dev/lib/preprocess.js", "../../mdast-util-from-markdown/dev/lib/index.js", "../../remark-parse/lib/index.js", "../../mdast-util-to-hast/lib/handlers/blockquote.js", "../../mdast-util-to-hast/lib/handlers/break.js", "../../mdast-util-to-hast/lib/handlers/code.js", "../../mdast-util-to-hast/lib/handlers/delete.js", "../../mdast-util-to-hast/lib/handlers/emphasis.js", "../../mdast-util-to-hast/lib/handlers/footnote-reference.js", "../../mdast-util-to-hast/lib/handlers/heading.js", "../../mdast-util-to-hast/lib/handlers/html.js", "../../mdast-util-to-hast/lib/revert.js", "../../mdast-util-to-hast/lib/handlers/image-reference.js", "../../mdast-util-to-hast/lib/handlers/image.js", "../../mdast-util-to-hast/lib/handlers/inline-code.js", "../../mdast-util-to-hast/lib/handlers/link-reference.js", "../../mdast-util-to-hast/lib/handlers/link.js", "../../mdast-util-to-hast/lib/handlers/list-item.js", "../../mdast-util-to-hast/lib/handlers/list.js", "../../mdast-util-to-hast/lib/handlers/paragraph.js", "../../mdast-util-to-hast/lib/handlers/root.js", "../../mdast-util-to-hast/lib/handlers/strong.js", "../../mdast-util-to-hast/lib/handlers/table.js", "../../mdast-util-to-hast/lib/handlers/table-row.js", "../../mdast-util-to-hast/lib/handlers/table-cell.js", "../../trim-lines/index.js", "../../mdast-util-to-hast/lib/handlers/text.js", "../../mdast-util-to-hast/lib/handlers/thematic-break.js", "../../mdast-util-to-hast/lib/handlers/index.js", "../../mdast-util-to-hast/lib/footer.js", "../../mdast-util-to-hast/lib/state.js", "../../mdast-util-to-hast/lib/index.js", "../../remark-rehype/lib/index.js", "../../bail/index.js", "../../unified/lib/index.js", "../../is-plain-obj/index.js", "../../trough/lib/index.js", "../../vfile/lib/minpath.browser.js", "../../vfile/lib/minproc.browser.js", "../../vfile/lib/minurl.shared.js", "../../vfile/lib/minurl.browser.js", "../../vfile/lib/index.js", "../../unified/lib/callable-instance.js"], "sourcesContent": ["// http://www.w3.org/TR/CSS21/grammar.html\n// https://github.com/visionmedia/css-parse/pull/49#issuecomment-30088027\nvar COMMENT_REGEX = /\\/\\*[^*]*\\*+([^/*][^*]*\\*+)*\\//g;\n\nvar NEWLINE_REGEX = /\\n/g;\nvar WHITESPACE_REGEX = /^\\s*/;\n\n// declaration\nvar PROPERTY_REGEX = /^(\\*?[-#/*\\\\\\w]+(\\[[0-9a-z_-]+\\])?)\\s*/;\nvar COLON_REGEX = /^:\\s*/;\nvar VALUE_REGEX = /^((?:'(?:\\\\'|.)*?'|\"(?:\\\\\"|.)*?\"|\\([^)]*?\\)|[^};])+)/;\nvar SEMICOLON_REGEX = /^[;\\s]*/;\n\n// https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String/Trim#Polyfill\nvar TRIM_REGEX = /^\\s+|\\s+$/g;\n\n// strings\nvar NEWLINE = '\\n';\nvar FORWARD_SLASH = '/';\nvar ASTERISK = '*';\nvar EMPTY_STRING = '';\n\n// types\nvar TYPE_COMMENT = 'comment';\nvar TYPE_DECLARATION = 'declaration';\n\n/**\n * @param {String} style\n * @param {Object} [options]\n * @return {Object[]}\n * @throws {TypeError}\n * @throws {Error}\n */\nmodule.exports = function (style, options) {\n  if (typeof style !== 'string') {\n    throw new TypeError('First argument must be a string');\n  }\n\n  if (!style) return [];\n\n  options = options || {};\n\n  /**\n   * Positional.\n   */\n  var lineno = 1;\n  var column = 1;\n\n  /**\n   * Update lineno and column based on `str`.\n   *\n   * @param {String} str\n   */\n  function updatePosition(str) {\n    var lines = str.match(NEWLINE_REGEX);\n    if (lines) lineno += lines.length;\n    var i = str.lastIndexOf(NEWLINE);\n    column = ~i ? str.length - i : column + str.length;\n  }\n\n  /**\n   * Mark position and patch `node.position`.\n   *\n   * @return {Function}\n   */\n  function position() {\n    var start = { line: lineno, column: column };\n    return function (node) {\n      node.position = new Position(start);\n      whitespace();\n      return node;\n    };\n  }\n\n  /**\n   * Store position information for a node.\n   *\n   * @constructor\n   * @property {Object} start\n   * @property {Object} end\n   * @property {undefined|String} source\n   */\n  function Position(start) {\n    this.start = start;\n    this.end = { line: lineno, column: column };\n    this.source = options.source;\n  }\n\n  /**\n   * Non-enumerable source string.\n   */\n  Position.prototype.content = style;\n\n  var errorsList = [];\n\n  /**\n   * Error `msg`.\n   *\n   * @param {String} msg\n   * @throws {Error}\n   */\n  function error(msg) {\n    var err = new Error(\n      options.source + ':' + lineno + ':' + column + ': ' + msg\n    );\n    err.reason = msg;\n    err.filename = options.source;\n    err.line = lineno;\n    err.column = column;\n    err.source = style;\n\n    if (options.silent) {\n      errorsList.push(err);\n    } else {\n      throw err;\n    }\n  }\n\n  /**\n   * Match `re` and return captures.\n   *\n   * @param {RegExp} re\n   * @return {undefined|Array}\n   */\n  function match(re) {\n    var m = re.exec(style);\n    if (!m) return;\n    var str = m[0];\n    updatePosition(str);\n    style = style.slice(str.length);\n    return m;\n  }\n\n  /**\n   * Parse whitespace.\n   */\n  function whitespace() {\n    match(WHITESPACE_REGEX);\n  }\n\n  /**\n   * Parse comments.\n   *\n   * @param {Object[]} [rules]\n   * @return {Object[]}\n   */\n  function comments(rules) {\n    var c;\n    rules = rules || [];\n    while ((c = comment())) {\n      if (c !== false) {\n        rules.push(c);\n      }\n    }\n    return rules;\n  }\n\n  /**\n   * Parse comment.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function comment() {\n    var pos = position();\n    if (FORWARD_SLASH != style.charAt(0) || ASTERISK != style.charAt(1)) return;\n\n    var i = 2;\n    while (\n      EMPTY_STRING != style.charAt(i) &&\n      (ASTERISK != style.charAt(i) || FORWARD_SLASH != style.charAt(i + 1))\n    ) {\n      ++i;\n    }\n    i += 2;\n\n    if (EMPTY_STRING === style.charAt(i - 1)) {\n      return error('End of comment missing');\n    }\n\n    var str = style.slice(2, i - 2);\n    column += 2;\n    updatePosition(str);\n    style = style.slice(i);\n    column += 2;\n\n    return pos({\n      type: TYPE_COMMENT,\n      comment: str\n    });\n  }\n\n  /**\n   * Parse declaration.\n   *\n   * @return {Object}\n   * @throws {Error}\n   */\n  function declaration() {\n    var pos = position();\n\n    // prop\n    var prop = match(PROPERTY_REGEX);\n    if (!prop) return;\n    comment();\n\n    // :\n    if (!match(COLON_REGEX)) return error(\"property missing ':'\");\n\n    // val\n    var val = match(VALUE_REGEX);\n\n    var ret = pos({\n      type: TYPE_DECLARATION,\n      property: trim(prop[0].replace(COMMENT_REGEX, EMPTY_STRING)),\n      value: val\n        ? trim(val[0].replace(COMMENT_REGEX, EMPTY_STRING))\n        : EMPTY_STRING\n    });\n\n    // ;\n    match(SEMICOLON_REGEX);\n\n    return ret;\n  }\n\n  /**\n   * Parse declarations.\n   *\n   * @return {Object[]}\n   */\n  function declarations() {\n    var decls = [];\n\n    comments(decls);\n\n    // declarations\n    var decl;\n    while ((decl = declaration())) {\n      if (decl !== false) {\n        decls.push(decl);\n        comments(decls);\n      }\n    }\n\n    return decls;\n  }\n\n  whitespace();\n  return declarations();\n};\n\n/**\n * Trim `str`.\n *\n * @param {String} str\n * @return {String}\n */\nfunction trim(str) {\n  return str ? str.replace(TRIM_REGEX, EMPTY_STRING) : EMPTY_STRING;\n}\n", "import type { Declaration } from 'inline-style-parser';\nimport parse from 'inline-style-parser';\n\nexport { Declaration };\n\ninterface StyleObject {\n  [name: string]: string;\n}\n\ntype Iterator = (\n  property: string,\n  value: string,\n  declaration: Declaration,\n) => void;\n\n/**\n * Parses inline style to object.\n *\n * @param style - Inline style.\n * @param iterator - Iterator.\n * @returns - Style object or null.\n *\n * @example Parsing inline style to object:\n *\n * ```js\n * import parse from 'style-to-object';\n * parse('line-height: 42;'); // { 'line-height': '42' }\n * ```\n */\nexport default function StyleToObject(\n  style: string,\n  iterator?: Iterator,\n): StyleObject | null {\n  let styleObject: StyleObject | null = null;\n\n  if (!style || typeof style !== 'string') {\n    return styleObject;\n  }\n\n  const declarations = parse(style);\n  const hasIterator = typeof iterator === 'function';\n\n  declarations.forEach((declaration) => {\n    if (declaration.type !== 'declaration') {\n      return;\n    }\n\n    const { property, value } = declaration;\n\n    if (hasIterator) {\n      iterator(property, value, declaration);\n    } else if (value) {\n      styleObject = styleObject || {};\n      styleObject[property] = value;\n    }\n  });\n\n  return styleObject;\n}\n", "const CUSTOM_PROPERTY_REGEX = /^--[a-zA-Z0-9_-]+$/;\nconst HYPHEN_REGEX = /-([a-z])/g;\nconst NO_HYPHEN_REGEX = /^[^-]+$/;\nconst VENDOR_PREFIX_REGEX = /^-(webkit|moz|ms|o|khtml)-/;\nconst MS_VENDOR_PREFIX_REGEX = /^-(ms)-/;\n\n/**\n * Checks whether to skip camelCase.\n */\nconst skipCamelCase = (property: string) =>\n  !property ||\n  NO_HYPHEN_REGEX.test(property) ||\n  CUSTOM_PROPERTY_REGEX.test(property);\n\n/**\n * Replacer that capitalizes first character.\n */\nconst capitalize = (match: string, character: string) =>\n  character.toUpperCase();\n\n/**\n * Replacer that removes beginning hyphen of vendor prefix property.\n */\nconst trimHyphen = (match: string, prefix: string) => `${prefix}-`;\n\n/**\n * CamelCase options.\n */\nexport interface CamelCaseOptions {\n  reactCompat?: boolean;\n}\n\n/**\n * CamelCases a CSS property.\n */\nexport const camelCase = (property: string, options: CamelCaseOptions = {}) => {\n  if (skipCamelCase(property)) {\n    return property;\n  }\n\n  property = property.toLowerCase();\n\n  if (options.reactCompat) {\n    // `-ms` vendor prefix should not be capitalized\n    property = property.replace(MS_VENDOR_PREFIX_REGEX, trimHyphen);\n  } else {\n    // for non-React, remove first hyphen so vendor prefix is not capitalized\n    property = property.replace(VENDOR_PREFIX_REGEX, trimHyphen);\n  }\n\n  return property.replace(HYPHEN_REGEX, capitalize);\n};\n", "import StyleToObject from 'style-to-object';\n\nimport { camelCase, CamelCaseOptions } from './utilities';\n\ntype StyleObject = Record<string, string>;\n\ninterface StyleToJSOptions extends CamelCaseOptions {}\n\n/**\n * Parses CSS inline style to JavaScript object (camelCased).\n */\nfunction StyleToJS(style: string, options?: StyleToJSOptions): StyleObject {\n  const output: StyleObject = {};\n\n  if (!style || typeof style !== 'string') {\n    return output;\n  }\n\n  StyleToObject(style, (property, value) => {\n    // skip CSS comment\n    if (property && value) {\n      output[camelCase(property, options)] = value;\n    }\n  });\n\n  return output;\n}\n\nStyleToJS.default = StyleToJS;\n\nexport = StyleToJS;\n", "/**\n * Helpers.\n */\n\nvar s = 1000;\nvar m = s * 60;\nvar h = m * 60;\nvar d = h * 24;\nvar w = d * 7;\nvar y = d * 365.25;\n\n/**\n * Parse or format the given `val`.\n *\n * Options:\n *\n *  - `long` verbose formatting [false]\n *\n * @param {String|Number} val\n * @param {Object} [options]\n * @throws {Error} throw an error if val is not a non-empty string or a number\n * @return {String|Number}\n * @api public\n */\n\nmodule.exports = function (val, options) {\n  options = options || {};\n  var type = typeof val;\n  if (type === 'string' && val.length > 0) {\n    return parse(val);\n  } else if (type === 'number' && isFinite(val)) {\n    return options.long ? fmtLong(val) : fmtShort(val);\n  }\n  throw new Error(\n    'val is not a non-empty string or a valid number. val=' +\n      JSON.stringify(val)\n  );\n};\n\n/**\n * Parse the given `str` and return milliseconds.\n *\n * @param {String} str\n * @return {Number}\n * @api private\n */\n\nfunction parse(str) {\n  str = String(str);\n  if (str.length > 100) {\n    return;\n  }\n  var match = /^(-?(?:\\d+)?\\.?\\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(\n    str\n  );\n  if (!match) {\n    return;\n  }\n  var n = parseFloat(match[1]);\n  var type = (match[2] || 'ms').toLowerCase();\n  switch (type) {\n    case 'years':\n    case 'year':\n    case 'yrs':\n    case 'yr':\n    case 'y':\n      return n * y;\n    case 'weeks':\n    case 'week':\n    case 'w':\n      return n * w;\n    case 'days':\n    case 'day':\n    case 'd':\n      return n * d;\n    case 'hours':\n    case 'hour':\n    case 'hrs':\n    case 'hr':\n    case 'h':\n      return n * h;\n    case 'minutes':\n    case 'minute':\n    case 'mins':\n    case 'min':\n    case 'm':\n      return n * m;\n    case 'seconds':\n    case 'second':\n    case 'secs':\n    case 'sec':\n    case 's':\n      return n * s;\n    case 'milliseconds':\n    case 'millisecond':\n    case 'msecs':\n    case 'msec':\n    case 'ms':\n      return n;\n    default:\n      return undefined;\n  }\n}\n\n/**\n * Short format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtShort(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return Math.round(ms / d) + 'd';\n  }\n  if (msAbs >= h) {\n    return Math.round(ms / h) + 'h';\n  }\n  if (msAbs >= m) {\n    return Math.round(ms / m) + 'm';\n  }\n  if (msAbs >= s) {\n    return Math.round(ms / s) + 's';\n  }\n  return ms + 'ms';\n}\n\n/**\n * Long format for `ms`.\n *\n * @param {Number} ms\n * @return {String}\n * @api private\n */\n\nfunction fmtLong(ms) {\n  var msAbs = Math.abs(ms);\n  if (msAbs >= d) {\n    return plural(ms, msAbs, d, 'day');\n  }\n  if (msAbs >= h) {\n    return plural(ms, msAbs, h, 'hour');\n  }\n  if (msAbs >= m) {\n    return plural(ms, msAbs, m, 'minute');\n  }\n  if (msAbs >= s) {\n    return plural(ms, msAbs, s, 'second');\n  }\n  return ms + ' ms';\n}\n\n/**\n * Pluralization helper.\n */\n\nfunction plural(ms, msAbs, n, name) {\n  var isPlural = msAbs >= n * 1.5;\n  return Math.round(ms / n) + ' ' + name + (isPlural ? 's' : '');\n}\n", "\n/**\n * This is the common logic for both the Node.js and web browser\n * implementations of `debug()`.\n */\n\nfunction setup(env) {\n\tcreateDebug.debug = createDebug;\n\tcreateDebug.default = createDebug;\n\tcreateDebug.coerce = coerce;\n\tcreateDebug.disable = disable;\n\tcreateDebug.enable = enable;\n\tcreateDebug.enabled = enabled;\n\tcreateDebug.humanize = require('ms');\n\tcreateDebug.destroy = destroy;\n\n\tObject.keys(env).forEach(key => {\n\t\tcreateDebug[key] = env[key];\n\t});\n\n\t/**\n\t* The currently active debug mode names, and names to skip.\n\t*/\n\n\tcreateDebug.names = [];\n\tcreateDebug.skips = [];\n\n\t/**\n\t* Map of special \"%n\" handling functions, for the debug \"format\" argument.\n\t*\n\t* Valid key names are a single, lower or upper-case letter, i.e. \"n\" and \"N\".\n\t*/\n\tcreateDebug.formatters = {};\n\n\t/**\n\t* Selects a color for a debug namespace\n\t* @param {String} namespace The namespace string for the debug instance to be colored\n\t* @return {Number|String} An ANSI color code for the given namespace\n\t* @api private\n\t*/\n\tfunction selectColor(namespace) {\n\t\tlet hash = 0;\n\n\t\tfor (let i = 0; i < namespace.length; i++) {\n\t\t\thash = ((hash << 5) - hash) + namespace.charCodeAt(i);\n\t\t\thash |= 0; // Convert to 32bit integer\n\t\t}\n\n\t\treturn createDebug.colors[Math.abs(hash) % createDebug.colors.length];\n\t}\n\tcreateDebug.selectColor = selectColor;\n\n\t/**\n\t* Create a debugger with the given `namespace`.\n\t*\n\t* @param {String} namespace\n\t* @return {Function}\n\t* @api public\n\t*/\n\tfunction createDebug(namespace) {\n\t\tlet prevTime;\n\t\tlet enableOverride = null;\n\t\tlet namespacesCache;\n\t\tlet enabledCache;\n\n\t\tfunction debug(...args) {\n\t\t\t// Disabled?\n\t\t\tif (!debug.enabled) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst self = debug;\n\n\t\t\t// Set `diff` timestamp\n\t\t\tconst curr = Number(new Date());\n\t\t\tconst ms = curr - (prevTime || curr);\n\t\t\tself.diff = ms;\n\t\t\tself.prev = prevTime;\n\t\t\tself.curr = curr;\n\t\t\tprevTime = curr;\n\n\t\t\targs[0] = createDebug.coerce(args[0]);\n\n\t\t\tif (typeof args[0] !== 'string') {\n\t\t\t\t// Anything else let's inspect with %O\n\t\t\t\targs.unshift('%O');\n\t\t\t}\n\n\t\t\t// Apply any `formatters` transformations\n\t\t\tlet index = 0;\n\t\t\targs[0] = args[0].replace(/%([a-zA-Z%])/g, (match, format) => {\n\t\t\t\t// If we encounter an escaped % then don't increase the array index\n\t\t\t\tif (match === '%%') {\n\t\t\t\t\treturn '%';\n\t\t\t\t}\n\t\t\t\tindex++;\n\t\t\t\tconst formatter = createDebug.formatters[format];\n\t\t\t\tif (typeof formatter === 'function') {\n\t\t\t\t\tconst val = args[index];\n\t\t\t\t\tmatch = formatter.call(self, val);\n\n\t\t\t\t\t// Now we need to remove `args[index]` since it's inlined in the `format`\n\t\t\t\t\targs.splice(index, 1);\n\t\t\t\t\tindex--;\n\t\t\t\t}\n\t\t\t\treturn match;\n\t\t\t});\n\n\t\t\t// Apply env-specific formatting (colors, etc.)\n\t\t\tcreateDebug.formatArgs.call(self, args);\n\n\t\t\tconst logFn = self.log || createDebug.log;\n\t\t\tlogFn.apply(self, args);\n\t\t}\n\n\t\tdebug.namespace = namespace;\n\t\tdebug.useColors = createDebug.useColors();\n\t\tdebug.color = createDebug.selectColor(namespace);\n\t\tdebug.extend = extend;\n\t\tdebug.destroy = createDebug.destroy; // XXX Temporary. Will be removed in the next major release.\n\n\t\tObject.defineProperty(debug, 'enabled', {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: false,\n\t\t\tget: () => {\n\t\t\t\tif (enableOverride !== null) {\n\t\t\t\t\treturn enableOverride;\n\t\t\t\t}\n\t\t\t\tif (namespacesCache !== createDebug.namespaces) {\n\t\t\t\t\tnamespacesCache = createDebug.namespaces;\n\t\t\t\t\tenabledCache = createDebug.enabled(namespace);\n\t\t\t\t}\n\n\t\t\t\treturn enabledCache;\n\t\t\t},\n\t\t\tset: v => {\n\t\t\t\tenableOverride = v;\n\t\t\t}\n\t\t});\n\n\t\t// Env-specific initialization logic for debug instances\n\t\tif (typeof createDebug.init === 'function') {\n\t\t\tcreateDebug.init(debug);\n\t\t}\n\n\t\treturn debug;\n\t}\n\n\tfunction extend(namespace, delimiter) {\n\t\tconst newDebug = createDebug(this.namespace + (typeof delimiter === 'undefined' ? ':' : delimiter) + namespace);\n\t\tnewDebug.log = this.log;\n\t\treturn newDebug;\n\t}\n\n\t/**\n\t* Enables a debug mode by namespaces. This can include modes\n\t* separated by a colon and wildcards.\n\t*\n\t* @param {String} namespaces\n\t* @api public\n\t*/\n\tfunction enable(namespaces) {\n\t\tcreateDebug.save(namespaces);\n\t\tcreateDebug.namespaces = namespaces;\n\n\t\tcreateDebug.names = [];\n\t\tcreateDebug.skips = [];\n\n\t\tconst split = (typeof namespaces === 'string' ? namespaces : '')\n\t\t\t.trim()\n\t\t\t.replace(' ', ',')\n\t\t\t.split(',')\n\t\t\t.filter(Boolean);\n\n\t\tfor (const ns of split) {\n\t\t\tif (ns[0] === '-') {\n\t\t\t\tcreateDebug.skips.push(ns.slice(1));\n\t\t\t} else {\n\t\t\t\tcreateDebug.names.push(ns);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Checks if the given string matches a namespace template, honoring\n\t * asterisks as wildcards.\n\t *\n\t * @param {String} search\n\t * @param {String} template\n\t * @return {Boolean}\n\t */\n\tfunction matchesTemplate(search, template) {\n\t\tlet searchIndex = 0;\n\t\tlet templateIndex = 0;\n\t\tlet starIndex = -1;\n\t\tlet matchIndex = 0;\n\n\t\twhile (searchIndex < search.length) {\n\t\t\tif (templateIndex < template.length && (template[templateIndex] === search[searchIndex] || template[templateIndex] === '*')) {\n\t\t\t\t// Match character or proceed with wildcard\n\t\t\t\tif (template[templateIndex] === '*') {\n\t\t\t\t\tstarIndex = templateIndex;\n\t\t\t\t\tmatchIndex = searchIndex;\n\t\t\t\t\ttemplateIndex++; // Skip the '*'\n\t\t\t\t} else {\n\t\t\t\t\tsearchIndex++;\n\t\t\t\t\ttemplateIndex++;\n\t\t\t\t}\n\t\t\t} else if (starIndex !== -1) { // eslint-disable-line no-negated-condition\n\t\t\t\t// Backtrack to the last '*' and try to match more characters\n\t\t\t\ttemplateIndex = starIndex + 1;\n\t\t\t\tmatchIndex++;\n\t\t\t\tsearchIndex = matchIndex;\n\t\t\t} else {\n\t\t\t\treturn false; // No match\n\t\t\t}\n\t\t}\n\n\t\t// Handle trailing '*' in template\n\t\twhile (templateIndex < template.length && template[templateIndex] === '*') {\n\t\t\ttemplateIndex++;\n\t\t}\n\n\t\treturn templateIndex === template.length;\n\t}\n\n\t/**\n\t* Disable debug output.\n\t*\n\t* @return {String} namespaces\n\t* @api public\n\t*/\n\tfunction disable() {\n\t\tconst namespaces = [\n\t\t\t...createDebug.names,\n\t\t\t...createDebug.skips.map(namespace => '-' + namespace)\n\t\t].join(',');\n\t\tcreateDebug.enable('');\n\t\treturn namespaces;\n\t}\n\n\t/**\n\t* Returns true if the given mode name is enabled, false otherwise.\n\t*\n\t* @param {String} name\n\t* @return {Boolean}\n\t* @api public\n\t*/\n\tfunction enabled(name) {\n\t\tfor (const skip of createDebug.skips) {\n\t\t\tif (matchesTemplate(name, skip)) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\tfor (const ns of createDebug.names) {\n\t\t\tif (matchesTemplate(name, ns)) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\t/**\n\t* Coerce `val`.\n\t*\n\t* @param {Mixed} val\n\t* @return {Mixed}\n\t* @api private\n\t*/\n\tfunction coerce(val) {\n\t\tif (val instanceof Error) {\n\t\t\treturn val.stack || val.message;\n\t\t}\n\t\treturn val;\n\t}\n\n\t/**\n\t* XXX DO NOT USE. This is a temporary stub function.\n\t* XXX It WILL be removed in the next major release.\n\t*/\n\tfunction destroy() {\n\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t}\n\n\tcreateDebug.enable(createDebug.load());\n\n\treturn createDebug;\n}\n\nmodule.exports = setup;\n", "/* eslint-env browser */\n\n/**\n * This is the web browser implementation of `debug()`.\n */\n\nexports.formatArgs = formatArgs;\nexports.save = save;\nexports.load = load;\nexports.useColors = useColors;\nexports.storage = localstorage();\nexports.destroy = (() => {\n\tlet warned = false;\n\n\treturn () => {\n\t\tif (!warned) {\n\t\t\twarned = true;\n\t\t\tconsole.warn('Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.');\n\t\t}\n\t};\n})();\n\n/**\n * Colors.\n */\n\nexports.colors = [\n\t'#0000CC',\n\t'#0000FF',\n\t'#0033CC',\n\t'#0033FF',\n\t'#0066CC',\n\t'#0066FF',\n\t'#0099CC',\n\t'#0099FF',\n\t'#00CC00',\n\t'#00CC33',\n\t'#00CC66',\n\t'#00CC99',\n\t'#00CCCC',\n\t'#00CCFF',\n\t'#3300CC',\n\t'#3300FF',\n\t'#3333CC',\n\t'#3333FF',\n\t'#3366CC',\n\t'#3366FF',\n\t'#3399CC',\n\t'#3399FF',\n\t'#33CC00',\n\t'#33CC33',\n\t'#33CC66',\n\t'#33CC99',\n\t'#33CCCC',\n\t'#33CCFF',\n\t'#6600CC',\n\t'#6600FF',\n\t'#6633CC',\n\t'#6633FF',\n\t'#66CC00',\n\t'#66CC33',\n\t'#9900CC',\n\t'#9900FF',\n\t'#9933CC',\n\t'#9933FF',\n\t'#99CC00',\n\t'#99CC33',\n\t'#CC0000',\n\t'#CC0033',\n\t'#CC0066',\n\t'#CC0099',\n\t'#CC00CC',\n\t'#CC00FF',\n\t'#CC3300',\n\t'#CC3333',\n\t'#CC3366',\n\t'#CC3399',\n\t'#CC33CC',\n\t'#CC33FF',\n\t'#CC6600',\n\t'#CC6633',\n\t'#CC9900',\n\t'#CC9933',\n\t'#CCCC00',\n\t'#CCCC33',\n\t'#FF0000',\n\t'#FF0033',\n\t'#FF0066',\n\t'#FF0099',\n\t'#FF00CC',\n\t'#FF00FF',\n\t'#FF3300',\n\t'#FF3333',\n\t'#FF3366',\n\t'#FF3399',\n\t'#FF33CC',\n\t'#FF33FF',\n\t'#FF6600',\n\t'#FF6633',\n\t'#FF9900',\n\t'#FF9933',\n\t'#FFCC00',\n\t'#FFCC33'\n];\n\n/**\n * Currently only WebKit-based Web Inspectors, Firefox >= v31,\n * and the Firebug extension (any Firefox version) are known\n * to support \"%c\" CSS customizations.\n *\n * TODO: add a `localStorage` variable to explicitly enable/disable colors\n */\n\n// eslint-disable-next-line complexity\nfunction useColors() {\n\t// NB: In an Electron preload script, document will be defined but not fully\n\t// initialized. Since we know we're in Chrome, we'll just detect this case\n\t// explicitly\n\tif (typeof window !== 'undefined' && window.process && (window.process.type === 'renderer' || window.process.__nwjs)) {\n\t\treturn true;\n\t}\n\n\t// Internet Explorer and Edge do not support colors.\n\tif (typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/(edge|trident)\\/(\\d+)/)) {\n\t\treturn false;\n\t}\n\n\tlet m;\n\n\t// Is webkit? http://stackoverflow.com/a/16459606/376773\n\t// document is undefined in react-native: https://github.com/facebook/react-native/pull/1632\n\t// eslint-disable-next-line no-return-assign\n\treturn (typeof document !== 'undefined' && document.documentElement && document.documentElement.style && document.documentElement.style.WebkitAppearance) ||\n\t\t// Is firebug? http://stackoverflow.com/a/398120/376773\n\t\t(typeof window !== 'undefined' && window.console && (window.console.firebug || (window.console.exception && window.console.table))) ||\n\t\t// Is firefox >= v31?\n\t\t// https://developer.mozilla.org/en-US/docs/Tools/Web_Console#Styling_messages\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && (m = navigator.userAgent.toLowerCase().match(/firefox\\/(\\d+)/)) && parseInt(m[1], 10) >= 31) ||\n\t\t// Double check webkit in userAgent just in case we are in a worker\n\t\t(typeof navigator !== 'undefined' && navigator.userAgent && navigator.userAgent.toLowerCase().match(/applewebkit\\/(\\d+)/));\n}\n\n/**\n * Colorize log arguments if enabled.\n *\n * @api public\n */\n\nfunction formatArgs(args) {\n\targs[0] = (this.useColors ? '%c' : '') +\n\t\tthis.namespace +\n\t\t(this.useColors ? ' %c' : ' ') +\n\t\targs[0] +\n\t\t(this.useColors ? '%c ' : ' ') +\n\t\t'+' + module.exports.humanize(this.diff);\n\n\tif (!this.useColors) {\n\t\treturn;\n\t}\n\n\tconst c = 'color: ' + this.color;\n\targs.splice(1, 0, c, 'color: inherit');\n\n\t// The final \"%c\" is somewhat tricky, because there could be other\n\t// arguments passed either before or after the %c, so we need to\n\t// figure out the correct index to insert the CSS into\n\tlet index = 0;\n\tlet lastC = 0;\n\targs[0].replace(/%[a-zA-Z%]/g, match => {\n\t\tif (match === '%%') {\n\t\t\treturn;\n\t\t}\n\t\tindex++;\n\t\tif (match === '%c') {\n\t\t\t// We only are interested in the *last* %c\n\t\t\t// (the user may have provided their own)\n\t\t\tlastC = index;\n\t\t}\n\t});\n\n\targs.splice(lastC, 0, c);\n}\n\n/**\n * Invokes `console.debug()` when available.\n * No-op when `console.debug` is not a \"function\".\n * If `console.debug` is not available, falls back\n * to `console.log`.\n *\n * @api public\n */\nexports.log = console.debug || console.log || (() => {});\n\n/**\n * Save `namespaces`.\n *\n * @param {String} namespaces\n * @api private\n */\nfunction save(namespaces) {\n\ttry {\n\t\tif (namespaces) {\n\t\t\texports.storage.setItem('debug', namespaces);\n\t\t} else {\n\t\t\texports.storage.removeItem('debug');\n\t\t}\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\n/**\n * Load `namespaces`.\n *\n * @return {String} returns the previously persisted debug modes\n * @api private\n */\nfunction load() {\n\tlet r;\n\ttry {\n\t\tr = exports.storage.getItem('debug');\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n\n\t// If debug isn't set in LS, and we're in Electron, try to load $DEBUG\n\tif (!r && typeof process !== 'undefined' && 'env' in process) {\n\t\tr = process.env.DEBUG;\n\t}\n\n\treturn r;\n}\n\n/**\n * Localstorage attempts to return the localstorage.\n *\n * This is necessary because safari throws\n * when a user disables cookies/localstorage\n * and you attempt to access it.\n *\n * @return {LocalStorage}\n * @api private\n */\n\nfunction localstorage() {\n\ttry {\n\t\t// TVMLKit (Apple TV JS Runtime) does not have a window object, just localStorage in the global context\n\t\t// The Browser also has localStorage in the global context.\n\t\treturn localStorage;\n\t} catch (error) {\n\t\t// Swallow\n\t\t// XXX (@Qix-) should we be logging these?\n\t}\n}\n\nmodule.exports = require('./common')(exports);\n\nconst {formatters} = module.exports;\n\n/**\n * Map %j to `JSON.stringify()`, since no Web Inspectors do that by default.\n */\n\nformatters.j = function (v) {\n\ttry {\n\t\treturn JSON.stringify(v);\n\t} catch (error) {\n\t\treturn '[UnexpectedJSONParseError]: ' + error.message;\n\t}\n};\n", "'use strict';\n\nvar hasOwn = Object.prototype.hasOwnProperty;\nvar toStr = Object.prototype.toString;\nvar defineProperty = Object.defineProperty;\nvar gOPD = Object.getOwnPropertyDescriptor;\n\nvar isArray = function isArray(arr) {\n\tif (typeof Array.isArray === 'function') {\n\t\treturn Array.isArray(arr);\n\t}\n\n\treturn toStr.call(arr) === '[object Array]';\n};\n\nvar isPlainObject = function isPlainObject(obj) {\n\tif (!obj || toStr.call(obj) !== '[object Object]') {\n\t\treturn false;\n\t}\n\n\tvar hasOwnConstructor = hasOwn.call(obj, 'constructor');\n\tvar hasIsPrototypeOf = obj.constructor && obj.constructor.prototype && hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');\n\t// Not own constructor property must be Object\n\tif (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {\n\t\treturn false;\n\t}\n\n\t// Own properties are enumerated firstly, so to speed up,\n\t// if last one is own, then all properties are own.\n\tvar key;\n\tfor (key in obj) { /**/ }\n\n\treturn typeof key === 'undefined' || hasOwn.call(obj, key);\n};\n\n// If name is '__proto__', and Object.defineProperty is available, define __proto__ as an own property on target\nvar setProperty = function setProperty(target, options) {\n\tif (defineProperty && options.name === '__proto__') {\n\t\tdefineProperty(target, options.name, {\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t\tvalue: options.newValue,\n\t\t\twritable: true\n\t\t});\n\t} else {\n\t\ttarget[options.name] = options.newValue;\n\t}\n};\n\n// Return undefined instead of __proto__ if '__proto__' is not an own property\nvar getProperty = function getProperty(obj, name) {\n\tif (name === '__proto__') {\n\t\tif (!hasOwn.call(obj, name)) {\n\t\t\treturn void 0;\n\t\t} else if (gOPD) {\n\t\t\t// In early versions of node, obj['__proto__'] is buggy when obj has\n\t\t\t// __proto__ as an own property. Object.getOwnPropertyDescriptor() works.\n\t\t\treturn gOPD(obj, name).value;\n\t\t}\n\t}\n\n\treturn obj[name];\n};\n\nmodule.exports = function extend() {\n\tvar options, name, src, copy, copyIsArray, clone;\n\tvar target = arguments[0];\n\tvar i = 1;\n\tvar length = arguments.length;\n\tvar deep = false;\n\n\t// Handle a deep copy situation\n\tif (typeof target === 'boolean') {\n\t\tdeep = target;\n\t\ttarget = arguments[1] || {};\n\t\t// skip the boolean and the target\n\t\ti = 2;\n\t}\n\tif (target == null || (typeof target !== 'object' && typeof target !== 'function')) {\n\t\ttarget = {};\n\t}\n\n\tfor (; i < length; ++i) {\n\t\toptions = arguments[i];\n\t\t// Only deal with non-null/undefined values\n\t\tif (options != null) {\n\t\t\t// Extend the base object\n\t\t\tfor (name in options) {\n\t\t\t\tsrc = getProperty(target, name);\n\t\t\t\tcopy = getProperty(options, name);\n\n\t\t\t\t// Prevent never-ending loop\n\t\t\t\tif (target !== copy) {\n\t\t\t\t\t// Recurse if we're merging plain objects or arrays\n\t\t\t\t\tif (deep && copy && (isPlainObject(copy) || (copyIsArray = isArray(copy)))) {\n\t\t\t\t\t\tif (copyIsArray) {\n\t\t\t\t\t\t\tcopyIsArray = false;\n\t\t\t\t\t\t\tclone = src && isArray(src) ? src : [];\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tclone = src && isPlainObject(src) ? src : {};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Never move original objects, clone them\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: extend(deep, clone, copy) });\n\n\t\t\t\t\t// Don't bring in undefined values\n\t\t\t\t\t} else if (typeof copy !== 'undefined') {\n\t\t\t\t\t\tsetProperty(target, { name: name, newValue: copy });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t// Return the modified object\n\treturn target;\n};\n", "/**\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [jsx=false]\n *   Support JSX identifiers (default: `false`).\n */\n\nconst startRe = /[$_\\p{ID_Start}]/u\nconst contRe = /[$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u\nconst contReJsx = /[-$_\\u{200C}\\u{200D}\\p{ID_Continue}]/u\nconst nameRe = /^[$_\\p{ID_Start}][$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u\nconst nameReJsx = /^[$_\\p{ID_Start}][-$_\\u{200C}\\u{200D}\\p{ID_Continue}]*$/u\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Checks if the given code point can start an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @returns {boolean}\n *   Whether `code` can start an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function start(code) {\n  return code ? startRe.test(String.fromCodePoint(code)) : false\n}\n\n/**\n * Checks if the given code point can continue an identifier.\n *\n * @param {number | undefined} code\n *   Code point to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `code` can continue an identifier.\n */\n// Note: `undefined` is supported so you can pass the result from `''.codePointAt`.\nexport function cont(code, options) {\n  const settings = options || emptyOptions\n  const re = settings.jsx ? contReJsx : contRe\n  return code ? re.test(String.fromCodePoint(code)) : false\n}\n\n/**\n * Checks if the given value is a valid identifier name.\n *\n * @param {string} name\n *   Identifier to check.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {boolean}\n *   Whether `name` can be an identifier.\n */\nexport function name(name, options) {\n  const settings = options || emptyOptions\n  const re = settings.jsx ? nameReJsx : nameRe\n  return re.test(name)\n}\n", "/**\n * @typedef {import('hast').Nodes} Nodes\n */\n\n// HTML whitespace expression.\n// See <https://infra.spec.whatwg.org/#ascii-whitespace>.\nconst re = /[ \\t\\n\\f\\r]/g\n\n/**\n * Check if the given value is *inter-element whitespace*.\n *\n * @param {Nodes | string} thing\n *   Thing to check (`Node` or `string`).\n * @returns {boolean}\n *   Whether the `value` is inter-element whitespace (`boolean`): consisting of\n *   zero or more of space, tab (`\\t`), line feed (`\\n`), carriage return\n *   (`\\r`), or form feed (`\\f`); if a node is passed it must be a `Text` node,\n *   whose `value` field is checked.\n */\nexport function whitespace(thing) {\n  return typeof thing === 'object'\n    ? thing.type === 'text'\n      ? empty(thing.value)\n      : false\n    : empty(thing)\n}\n\n/**\n * @param {string} value\n * @returns {boolean}\n */\nfunction empty(value) {\n  return value.replace(re, '') === ''\n}\n", "/**\n * @import {Identifier, Literal, MemberExpression} from 'estree'\n * @import {Jsx, JsxDev, <PERSON><PERSON>, Props} from 'hast-util-to-jsx-runtime'\n * @import {Element, Nodes, Parents, Root, Text} from 'hast'\n * @import {MdxFlowExpressionHast, MdxTextExpressionHast} from 'mdast-util-mdx-expression'\n * @import {MdxJsxFlowElementHast, MdxJsxTextElementHast} from 'mdast-util-mdx-jsx'\n * @import {MdxjsEsmHast} from 'mdast-util-mdxjs-esm'\n * @import {Position} from 'unist'\n * @import {Child, Create, Field, JsxElement, State, Style} from './types.js'\n */\n\nimport {stringify as commas} from 'comma-separated-tokens'\nimport {ok as assert} from 'devlop'\nimport {name as isIdentifierName} from 'estree-util-is-identifier-name'\nimport {whitespace} from 'hast-util-whitespace'\nimport {find, hastToReact, html, svg} from 'property-information'\nimport {stringify as spaces} from 'space-separated-tokens'\nimport styleToJs from 'style-to-js'\nimport {pointStart} from 'unist-util-position'\nimport {VFileMessage} from 'vfile-message'\n\n// To do: next major: `Object.hasOwn`.\nconst own = {}.hasOwnProperty\n\n/** @type {Map<string, number>} */\nconst emptyMap = new Map()\n\nconst cap = /[A-Z]/g\n\n// `react-dom` triggers a warning for *any* white space in tables.\n// To follow GFM, `mdast-util-to-hast` injects line endings between elements.\n// Other tools might do so too, but they don’t do here, so we remove all of\n// that.\n\n// See: <https://github.com/facebook/react/pull/7081>.\n// See: <https://github.com/facebook/react/pull/7515>.\n// See: <https://github.com/remarkjs/remark-react/issues/64>.\n// See: <https://github.com/rehypejs/rehype-react/pull/29>.\n// See: <https://github.com/rehypejs/rehype-react/pull/32>.\n// See: <https://github.com/rehypejs/rehype-react/pull/45>.\nconst tableElements = new Set(['table', 'tbody', 'thead', 'tfoot', 'tr'])\n\nconst tableCellElement = new Set(['td', 'th'])\n\nconst docs = 'https://github.com/syntax-tree/hast-util-to-jsx-runtime'\n\n/**\n * Transform a hast tree to preact, react, solid, svelte, vue, etc.,\n * with an automatic JSX runtime.\n *\n * @param {Nodes} tree\n *   Tree to transform.\n * @param {Options} options\n *   Configuration (required).\n * @returns {JsxElement}\n *   JSX element.\n */\n\nexport function toJsxRuntime(tree, options) {\n  if (!options || options.Fragment === undefined) {\n    throw new TypeError('Expected `Fragment` in options')\n  }\n\n  const filePath = options.filePath || undefined\n  /** @type {Create} */\n  let create\n\n  if (options.development) {\n    if (typeof options.jsxDEV !== 'function') {\n      throw new TypeError(\n        'Expected `jsxDEV` in options when `development: true`'\n      )\n    }\n\n    create = developmentCreate(filePath, options.jsxDEV)\n  } else {\n    if (typeof options.jsx !== 'function') {\n      throw new TypeError('Expected `jsx` in production options')\n    }\n\n    if (typeof options.jsxs !== 'function') {\n      throw new TypeError('Expected `jsxs` in production options')\n    }\n\n    create = productionCreate(filePath, options.jsx, options.jsxs)\n  }\n\n  /** @type {State} */\n  const state = {\n    Fragment: options.Fragment,\n    ancestors: [],\n    components: options.components || {},\n    create,\n    elementAttributeNameCase: options.elementAttributeNameCase || 'react',\n    evaluater: options.createEvaluater ? options.createEvaluater() : undefined,\n    filePath,\n    ignoreInvalidStyle: options.ignoreInvalidStyle || false,\n    passKeys: options.passKeys !== false,\n    passNode: options.passNode || false,\n    schema: options.space === 'svg' ? svg : html,\n    stylePropertyNameCase: options.stylePropertyNameCase || 'dom',\n    tableCellAlignToStyle: options.tableCellAlignToStyle !== false\n  }\n\n  const result = one(state, tree, undefined)\n\n  // JSX element.\n  if (result && typeof result !== 'string') {\n    return result\n  }\n\n  // Text node or something that turned into nothing.\n  return state.create(\n    tree,\n    state.Fragment,\n    {children: result || undefined},\n    undefined\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction one(state, node, key) {\n  if (node.type === 'element') {\n    return element(state, node, key)\n  }\n\n  if (node.type === 'mdxFlowExpression' || node.type === 'mdxTextExpression') {\n    return mdxExpression(state, node)\n  }\n\n  if (node.type === 'mdxJsxFlowElement' || node.type === 'mdxJsxTextElement') {\n    return mdxJsxElement(state, node, key)\n  }\n\n  if (node.type === 'mdxjsEsm') {\n    return mdxEsm(state, node)\n  }\n\n  if (node.type === 'root') {\n    return root(state, node, key)\n  }\n\n  if (node.type === 'text') {\n    return text(state, node)\n  }\n}\n\n/**\n * Handle element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction element(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.tagName.toLowerCase() === 'svg' && parentSchema.space === 'html') {\n    schema = svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type = findComponentFromName(state, node.tagName, false)\n  const props = createElementProps(state, node)\n  let children = createChildren(state, node)\n\n  if (tableElements.has(node.tagName)) {\n    children = children.filter(function (child) {\n      return typeof child === 'string' ? !whitespace(child) : true\n    })\n  }\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle MDX expression.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxFlowExpressionHast | MdxTextExpressionHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxExpression(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    const program = node.data.estree\n    const expression = program.body[0]\n    assert(expression.type === 'ExpressionStatement')\n\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateExpression(expression.expression)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX ESM.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxjsEsmHast} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxEsm(state, node) {\n  if (node.data && node.data.estree && state.evaluater) {\n    // Assume result is a child.\n    return /** @type {Child | undefined} */ (\n      state.evaluater.evaluateProgram(node.data.estree)\n    )\n  }\n\n  crashEstree(state, node.position)\n}\n\n/**\n * Handle MDX JSX.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction mdxJsxElement(state, node, key) {\n  const parentSchema = state.schema\n  let schema = parentSchema\n\n  if (node.name === 'svg' && parentSchema.space === 'html') {\n    schema = svg\n    state.schema = schema\n  }\n\n  state.ancestors.push(node)\n\n  const type =\n    node.name === null\n      ? state.Fragment\n      : findComponentFromName(state, node.name, true)\n  const props = createJsxElementProps(state, node)\n  const children = createChildren(state, node)\n\n  addNode(state, props, type, node)\n  addChildren(props, children)\n\n  // Restore.\n  state.ancestors.pop()\n  state.schema = parentSchema\n\n  return state.create(node, type, props, key)\n}\n\n/**\n * Handle root.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Root} node\n *   Current node.\n * @param {string | undefined} key\n *   Key.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction root(state, node, key) {\n  /** @type {Props} */\n  const props = {}\n\n  addChildren(props, createChildren(state, node))\n\n  return state.create(node, state.Fragment, props, key)\n}\n\n/**\n * Handle text.\n *\n * @param {State} _\n *   Info passed around.\n * @param {Text} node\n *   Current node.\n * @returns {Child | undefined}\n *   Child, optional.\n */\nfunction text(_, node) {\n  return node.value\n}\n\n/**\n * Add `node` to props.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Props} props\n *   Props.\n * @param {unknown} type\n *   Type.\n * @param {Element | MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addNode(state, props, type, node) {\n  // If this is swapped out for a component:\n  if (typeof type !== 'string' && type !== state.Fragment && state.passNode) {\n    props.node = node\n  }\n}\n\n/**\n * Add children to props.\n *\n * @param {Props} props\n *   Props.\n * @param {Array<Child>} children\n *   Children.\n * @returns {undefined}\n *   Nothing.\n */\nfunction addChildren(props, children) {\n  if (children.length > 0) {\n    const value = children.length > 1 ? children : children[0]\n\n    if (value) {\n      props.children = value\n    }\n  }\n}\n\n/**\n * @param {string | undefined} _\n *   Path to file.\n * @param {Jsx} jsx\n *   Dynamic.\n * @param {Jsx} jsxs\n *   Static.\n * @returns {Create}\n *   Create a production element.\n */\nfunction productionCreate(_, jsx, jsxs) {\n  return create\n  /** @type {Create} */\n  function create(_, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const fn = isStaticChildren ? jsxs : jsx\n    return key ? fn(type, props, key) : fn(type, props)\n  }\n}\n\n/**\n * @param {string | undefined} filePath\n *   Path to file.\n * @param {JsxDev} jsxDEV\n *   Development.\n * @returns {Create}\n *   Create a development element.\n */\nfunction developmentCreate(filePath, jsxDEV) {\n  return create\n  /** @type {Create} */\n  function create(node, type, props, key) {\n    // Only an array when there are 2 or more children.\n    const isStaticChildren = Array.isArray(props.children)\n    const point = pointStart(node)\n    return jsxDEV(\n      type,\n      props,\n      key,\n      isStaticChildren,\n      {\n        columnNumber: point ? point.column - 1 : undefined,\n        fileName: filePath,\n        lineNumber: point ? point.line : undefined\n      },\n      undefined\n    )\n  }\n}\n\n/**\n * Create props from an element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Element} node\n *   Current element.\n * @returns {Props}\n *   Props.\n */\nfunction createElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n  /** @type {string | undefined} */\n  let alignValue\n  /** @type {string} */\n  let prop\n\n  for (prop in node.properties) {\n    if (prop !== 'children' && own.call(node.properties, prop)) {\n      const result = createProperty(state, prop, node.properties[prop])\n\n      if (result) {\n        const [key, value] = result\n\n        if (\n          state.tableCellAlignToStyle &&\n          key === 'align' &&\n          typeof value === 'string' &&\n          tableCellElement.has(node.tagName)\n        ) {\n          alignValue = value\n        } else {\n          props[key] = value\n        }\n      }\n    }\n  }\n\n  if (alignValue) {\n    // Assume style is an object.\n    const style = /** @type {Style} */ (props.style || (props.style = {}))\n    style[state.stylePropertyNameCase === 'css' ? 'text-align' : 'textAlign'] =\n      alignValue\n  }\n\n  return props\n}\n\n/**\n * Create props from a JSX element.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdxJsxFlowElementHast | MdxJsxTextElementHast} node\n *   Current JSX element.\n * @returns {Props}\n *   Props.\n */\nfunction createJsxElementProps(state, node) {\n  /** @type {Props} */\n  const props = {}\n\n  for (const attribute of node.attributes) {\n    if (attribute.type === 'mdxJsxExpressionAttribute') {\n      if (attribute.data && attribute.data.estree && state.evaluater) {\n        const program = attribute.data.estree\n        const expression = program.body[0]\n        assert(expression.type === 'ExpressionStatement')\n        const objectExpression = expression.expression\n        assert(objectExpression.type === 'ObjectExpression')\n        const property = objectExpression.properties[0]\n        assert(property.type === 'SpreadElement')\n\n        Object.assign(\n          props,\n          state.evaluater.evaluateExpression(property.argument)\n        )\n      } else {\n        crashEstree(state, node.position)\n      }\n    } else {\n      // For JSX, the author is responsible of passing in the correct values.\n      const name = attribute.name\n      /** @type {unknown} */\n      let value\n\n      if (attribute.value && typeof attribute.value === 'object') {\n        if (\n          attribute.value.data &&\n          attribute.value.data.estree &&\n          state.evaluater\n        ) {\n          const program = attribute.value.data.estree\n          const expression = program.body[0]\n          assert(expression.type === 'ExpressionStatement')\n          value = state.evaluater.evaluateExpression(expression.expression)\n        } else {\n          crashEstree(state, node.position)\n        }\n      } else {\n        value = attribute.value === null ? true : attribute.value\n      }\n\n      // Assume a prop.\n      props[name] = /** @type {Props[keyof Props]} */ (value)\n    }\n  }\n\n  return props\n}\n\n/**\n * Create children.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Parents} node\n *   Current element.\n * @returns {Array<Child>}\n *   Children.\n */\nfunction createChildren(state, node) {\n  /** @type {Array<Child>} */\n  const children = []\n  let index = -1\n  /** @type {Map<string, number>} */\n  // Note: test this when Solid doesn’t want to merge my upcoming PR.\n  /* c8 ignore next */\n  const countsByName = state.passKeys ? new Map() : emptyMap\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    /** @type {string | undefined} */\n    let key\n\n    if (state.passKeys) {\n      const name =\n        child.type === 'element'\n          ? child.tagName\n          : child.type === 'mdxJsxFlowElement' ||\n              child.type === 'mdxJsxTextElement'\n            ? child.name\n            : undefined\n\n      if (name) {\n        const count = countsByName.get(name) || 0\n        key = name + '-' + count\n        countsByName.set(name, count + 1)\n      }\n    }\n\n    const result = one(state, child, key)\n    if (result !== undefined) children.push(result)\n  }\n\n  return children\n}\n\n/**\n * Handle a property.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} prop\n *   Key.\n * @param {Array<number | string> | boolean | number | string | null | undefined} value\n *   hast property value.\n * @returns {Field | undefined}\n *   Field for runtime, optional.\n */\nfunction createProperty(state, prop, value) {\n  const info = find(state.schema, prop)\n\n  // Ignore nullish and `NaN` values.\n  if (\n    value === null ||\n    value === undefined ||\n    (typeof value === 'number' && Number.isNaN(value))\n  ) {\n    return\n  }\n\n  if (Array.isArray(value)) {\n    // Accept `array`.\n    // Most props are space-separated.\n    value = info.commaSeparated ? commas(value) : spaces(value)\n  }\n\n  // React only accepts `style` as object.\n  if (info.property === 'style') {\n    let styleObject =\n      typeof value === 'object' ? value : parseStyle(state, String(value))\n\n    if (state.stylePropertyNameCase === 'css') {\n      styleObject = transformStylesToCssCasing(styleObject)\n    }\n\n    return ['style', styleObject]\n  }\n\n  return [\n    state.elementAttributeNameCase === 'react' && info.space\n      ? hastToReact[info.property] || info.property\n      : info.attribute,\n    value\n  ]\n}\n\n/**\n * Parse a CSS declaration to an object.\n *\n * @param {State} state\n *   Info passed around.\n * @param {string} value\n *   CSS declarations.\n * @returns {Style}\n *   Properties.\n * @throws\n *   Throws `VFileMessage` when CSS cannot be parsed.\n */\nfunction parseStyle(state, value) {\n  try {\n    return styleToJs(value, {reactCompat: true})\n  } catch (error) {\n    if (state.ignoreInvalidStyle) {\n      return {}\n    }\n\n    const cause = /** @type {Error} */ (error)\n    const message = new VFileMessage('Cannot parse `style` attribute', {\n      ancestors: state.ancestors,\n      cause,\n      ruleId: 'style',\n      source: 'hast-util-to-jsx-runtime'\n    })\n    message.file = state.filePath || undefined\n    message.url = docs + '#cannot-parse-style-attribute'\n\n    throw message\n  }\n}\n\n/**\n * Create a JSX name from a string.\n *\n * @param {State} state\n *   To do.\n * @param {string} name\n *   Name.\n * @param {boolean} allowExpression\n *   Allow member expressions and identifiers.\n * @returns {unknown}\n *   To do.\n */\nfunction findComponentFromName(state, name, allowExpression) {\n  /** @type {Identifier | Literal | MemberExpression} */\n  let result\n\n  if (!allowExpression) {\n    result = {type: 'Literal', value: name}\n  } else if (name.includes('.')) {\n    const identifiers = name.split('.')\n    let index = -1\n    /** @type {Identifier | Literal | MemberExpression | undefined} */\n    let node\n\n    while (++index < identifiers.length) {\n      /** @type {Identifier | Literal} */\n      const prop = isIdentifierName(identifiers[index])\n        ? {type: 'Identifier', name: identifiers[index]}\n        : {type: 'Literal', value: identifiers[index]}\n      node = node\n        ? {\n            type: 'MemberExpression',\n            object: node,\n            property: prop,\n            computed: Boolean(index && prop.type === 'Literal'),\n            optional: false\n          }\n        : prop\n    }\n\n    assert(node, 'always a result')\n    result = node\n  } else {\n    result =\n      isIdentifierName(name) && !/^[a-z]/.test(name)\n        ? {type: 'Identifier', name}\n        : {type: 'Literal', value: name}\n  }\n\n  // Only literals can be passed in `components` currently.\n  // No identifiers / member expressions.\n  if (result.type === 'Literal') {\n    const name = /** @type {string | number} */ (result.value)\n    return own.call(state.components, name) ? state.components[name] : name\n  }\n\n  // Assume component.\n  if (state.evaluater) {\n    return state.evaluater.evaluateExpression(result)\n  }\n\n  crashEstree(state)\n}\n\n/**\n * @param {State} state\n * @param {Position | undefined} [place]\n * @returns {never}\n */\nfunction crashEstree(state, place) {\n  const message = new VFileMessage(\n    'Cannot handle MDX estrees without `createEvaluater`',\n    {\n      ancestors: state.ancestors,\n      place,\n      ruleId: 'mdx-estree',\n      source: 'hast-util-to-jsx-runtime'\n    }\n  )\n  message.file = state.filePath || undefined\n  message.url = docs + '#cannot-handle-mdx-estrees-without-createevaluater'\n\n  throw message\n}\n\n/**\n * Transform a DOM casing style object to a CSS casing style object.\n *\n * @param {Style} domCasing\n * @returns {Style}\n */\nfunction transformStylesToCssCasing(domCasing) {\n  /** @type {Style} */\n  const cssCasing = {}\n  /** @type {string} */\n  let from\n\n  for (from in domCasing) {\n    if (own.call(domCasing, from)) {\n      cssCasing[transformStyleToCssCasing(from)] = domCasing[from]\n    }\n  }\n\n  return cssCasing\n}\n\n/**\n * Transform a DOM casing style field to a CSS casing style field.\n *\n * @param {string} from\n * @returns {string}\n */\nfunction transformStyleToCssCasing(from) {\n  let to = from.replace(cap, toDash)\n  // Handle `ms-xxx` -> `-ms-xxx`.\n  if (to.slice(0, 3) === 'ms-') to = '-' + to\n  return to\n}\n\n/**\n * Make `$0` dash cased.\n *\n * @param {string} $0\n *   Capitalized ASCII leter.\n * @returns {string}\n *   Dash and lower letter.\n */\nfunction toDash($0) {\n  return '-' + $0.toLowerCase()\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef NodeLike\n * @property {string} type\n * @property {PositionLike | null | undefined} [position]\n *\n * @typedef PointLike\n * @property {number | null | undefined} [line]\n * @property {number | null | undefined} [column]\n * @property {number | null | undefined} [offset]\n *\n * @typedef PositionLike\n * @property {PointLike | null | undefined} [start]\n * @property {PointLike | null | undefined} [end]\n */\n\n/**\n * Serialize the positional info of a point, position (start and end points),\n * or node.\n *\n * @param {Node | NodeLike | Point | PointLike | Position | PositionLike | null | undefined} [value]\n *   Node, position, or point.\n * @returns {string}\n *   Pretty printed positional info of a node (`string`).\n *\n *   In the format of a range `ls:cs-le:ce` (when given `node` or `position`)\n *   or a point `l:c` (when given `point`), where `l` stands for line, `c` for\n *   column, `s` for `start`, and `e` for end.\n *   An empty string (`''`) is returned if the given value is neither `node`,\n *   `position`, nor `point`.\n */\nexport function stringifyPosition(value) {\n  // Nothing.\n  if (!value || typeof value !== 'object') {\n    return ''\n  }\n\n  // Node.\n  if ('position' in value || 'type' in value) {\n    return position(value.position)\n  }\n\n  // Position.\n  if ('start' in value || 'end' in value) {\n    return position(value)\n  }\n\n  // Point.\n  if ('line' in value || 'column' in value) {\n    return point(value)\n  }\n\n  // ?\n  return ''\n}\n\n/**\n * @param {Point | PointLike | null | undefined} point\n * @returns {string}\n */\nfunction point(point) {\n  return index(point && point.line) + ':' + index(point && point.column)\n}\n\n/**\n * @param {Position | PositionLike | null | undefined} pos\n * @returns {string}\n */\nfunction position(pos) {\n  return point(pos && pos.start) + '-' + point(pos && pos.end)\n}\n\n/**\n * @param {number | null | undefined} value\n * @returns {number}\n */\nfunction index(value) {\n  return value && typeof value === 'number' ? value : 1\n}\n", "/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Point} Point\n * @typedef {import('unist').Position} Position\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n *\n * @typedef Options\n *   Configuration.\n * @property {Array<Node> | null | undefined} [ancestors]\n *   Stack of (inclusive) ancestor nodes surrounding the message (optional).\n * @property {Error | null | undefined} [cause]\n *   Original error cause of the message (optional).\n * @property {Point | Position | null | undefined} [place]\n *   Place of message (optional).\n * @property {string | null | undefined} [ruleId]\n *   Category of message (optional, example: `'my-rule'`).\n * @property {string | null | undefined} [source]\n *   Namespace of who sent the message (optional, example: `'my-package'`).\n */\n\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\n/**\n * Message.\n */\nexport class VFileMessage extends Error {\n  /**\n   * Create a message for `reason`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Options | null | undefined} [options]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | Options | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns\n   *   Instance of `VFileMessage`.\n   */\n  // eslint-disable-next-line complexity\n  constructor(causeOrReason, optionsOrParentOrPlace, origin) {\n    super()\n\n    if (typeof optionsOrParentOrPlace === 'string') {\n      origin = optionsOrParentOrPlace\n      optionsOrParentOrPlace = undefined\n    }\n\n    /** @type {string} */\n    let reason = ''\n    /** @type {Options} */\n    let options = {}\n    let legacyCause = false\n\n    if (optionsOrParentOrPlace) {\n      // Point.\n      if (\n        'line' in optionsOrParentOrPlace &&\n        'column' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Position.\n      else if (\n        'start' in optionsOrParentOrPlace &&\n        'end' in optionsOrParentOrPlace\n      ) {\n        options = {place: optionsOrParentOrPlace}\n      }\n      // Node.\n      else if ('type' in optionsOrParentOrPlace) {\n        options = {\n          ancestors: [optionsOrParentOrPlace],\n          place: optionsOrParentOrPlace.position\n        }\n      }\n      // Options.\n      else {\n        options = {...optionsOrParentOrPlace}\n      }\n    }\n\n    if (typeof causeOrReason === 'string') {\n      reason = causeOrReason\n    }\n    // Error.\n    else if (!options.cause && causeOrReason) {\n      legacyCause = true\n      reason = causeOrReason.message\n      options.cause = causeOrReason\n    }\n\n    if (!options.ruleId && !options.source && typeof origin === 'string') {\n      const index = origin.indexOf(':')\n\n      if (index === -1) {\n        options.ruleId = origin\n      } else {\n        options.source = origin.slice(0, index)\n        options.ruleId = origin.slice(index + 1)\n      }\n    }\n\n    if (!options.place && options.ancestors && options.ancestors) {\n      const parent = options.ancestors[options.ancestors.length - 1]\n\n      if (parent) {\n        options.place = parent.position\n      }\n    }\n\n    const start =\n      options.place && 'start' in options.place\n        ? options.place.start\n        : options.place\n\n    /* eslint-disable no-unused-expressions */\n    /**\n     * Stack of ancestor nodes surrounding the message.\n     *\n     * @type {Array<Node> | undefined}\n     */\n    this.ancestors = options.ancestors || undefined\n\n    /**\n     * Original error cause of the message.\n     *\n     * @type {Error | undefined}\n     */\n    this.cause = options.cause || undefined\n\n    /**\n     * Starting column of message.\n     *\n     * @type {number | undefined}\n     */\n    this.column = start ? start.column : undefined\n\n    /**\n     * State of problem.\n     *\n     * * `true` — error, file not usable\n     * * `false` — warning, change may be needed\n     * * `undefined` — change likely not needed\n     *\n     * @type {boolean | null | undefined}\n     */\n    this.fatal = undefined\n\n    /**\n     * Path of a file (used throughout the `VFile` ecosystem).\n     *\n     * @type {string | undefined}\n     */\n    this.file\n\n    // Field from `Error`.\n    /**\n     * Reason for message.\n     *\n     * @type {string}\n     */\n    this.message = reason\n\n    /**\n     * Starting line of error.\n     *\n     * @type {number | undefined}\n     */\n    this.line = start ? start.line : undefined\n\n    // Field from `Error`.\n    /**\n     * Serialized positional info of message.\n     *\n     * On normal errors, this would be something like `ParseError`, buit in\n     * `VFile` messages we use this space to show where an error happened.\n     */\n    this.name = stringifyPosition(options.place) || '1:1'\n\n    /**\n     * Place of message.\n     *\n     * @type {Point | Position | undefined}\n     */\n    this.place = options.place || undefined\n\n    /**\n     * Reason for message, should use markdown.\n     *\n     * @type {string}\n     */\n    this.reason = this.message\n\n    /**\n     * Category of message (example: `'my-rule'`).\n     *\n     * @type {string | undefined}\n     */\n    this.ruleId = options.ruleId || undefined\n\n    /**\n     * Namespace of message (example: `'my-package'`).\n     *\n     * @type {string | undefined}\n     */\n    this.source = options.source || undefined\n\n    // Field from `Error`.\n    /**\n     * Stack of message.\n     *\n     * This is used by normal errors to show where something happened in\n     * programming code, irrelevant for `VFile` messages,\n     *\n     * @type {string}\n     */\n    this.stack =\n      legacyCause && options.cause && typeof options.cause.stack === 'string'\n        ? options.cause.stack\n        : ''\n\n    // The following fields are “well known”.\n    // Not standard.\n    // Feel free to add other non-standard fields to your messages.\n\n    /**\n     * Specify the source value that’s being reported, which is deemed\n     * incorrect.\n     *\n     * @type {string | undefined}\n     */\n    this.actual\n\n    /**\n     * Suggest acceptable values that can be used instead of `actual`.\n     *\n     * @type {Array<string> | undefined}\n     */\n    this.expected\n\n    /**\n     * Long form description of the message (you should use markdown).\n     *\n     * @type {string | undefined}\n     */\n    this.note\n\n    /**\n     * Link to docs for the message.\n     *\n     * > 👉 **Note**: this must be an absolute URL that can be passed as `x`\n     * > to `new URL(x)`.\n     *\n     * @type {string | undefined}\n     */\n    this.url\n    /* eslint-enable no-unused-expressions */\n  }\n}\n\nVFileMessage.prototype.file = ''\nVFileMessage.prototype.name = ''\nVFileMessage.prototype.reason = ''\nVFileMessage.prototype.message = ''\nVFileMessage.prototype.stack = ''\nVFileMessage.prototype.column = undefined\nVFileMessage.prototype.line = undefined\nVFileMessage.prototype.ancestors = undefined\nVFileMessage.prototype.cause = undefined\nVFileMessage.prototype.fatal = undefined\nVFileMessage.prototype.place = undefined\nVFileMessage.prototype.ruleId = undefined\nVFileMessage.prototype.source = undefined\n", "/**\n * HTML URL properties.\n *\n * Each key is a property name and each value is a list of tag names it applies\n * to or `null` if it applies to all elements.\n *\n * @type {Record<string, Array<string> | null>}\n */\nexport const urlAttributes = {\n  action: ['form'],\n  cite: ['blockquote', 'del', 'ins', 'q'],\n  data: ['object'],\n  formAction: ['button', 'input'],\n  href: ['a', 'area', 'base', 'link'],\n  icon: ['menuitem'],\n  itemId: null,\n  manifest: ['html'],\n  ping: ['a', 'area'],\n  poster: ['video'],\n  src: [\n    'audio',\n    'embed',\n    'iframe',\n    'img',\n    'input',\n    'script',\n    'source',\n    'track',\n    'video'\n  ]\n}\n", "/**\n * @import {<PERSON><PERSON>, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */\n\n/**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\nimport {unreachable} from 'devlop'\nimport {toJsxRuntime} from 'hast-util-to-jsx-runtime'\nimport {urlAttributes} from 'html-url-attributes'\nimport {Fragment, jsx, jsxs} from 'react/jsx-runtime'\nimport {useEffect, useState} from 'react'\nimport remarkParse from 'remark-parse'\nimport remarkRehype from 'remark-rehype'\nimport {unified} from 'unified'\nimport {visit} from 'unist-util-visit'\nimport {VFile} from 'vfile'\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {from: 'className', id: 'remove-classname'},\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nexport function Markdown(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  return post(processor.runSync(processor.parse(file), file), options)\n}\n\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */\nexport async function MarkdownAsync(options) {\n  const processor = createProcessor(options)\n  const file = createFile(options)\n  const tree = await processor.run(processor.parse(file), file)\n  return post(tree, options)\n}\n\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */\nexport function MarkdownHooks(options) {\n  const processor = createProcessor(options)\n  const [error, setError] = useState(\n    /** @type {Error | undefined} */ (undefined)\n  )\n  const [tree, setTree] = useState(/** @type {Root | undefined} */ (undefined))\n\n  useEffect(\n    function () {\n      let cancelled = false\n      const file = createFile(options)\n\n      processor.run(processor.parse(file), file, function (error, tree) {\n        if (!cancelled) {\n          setError(error)\n          setTree(tree)\n        }\n      })\n\n      /**\n       * @returns {undefined}\n       *   Nothing.\n       */\n      return function () {\n        cancelled = true\n      }\n    },\n    [\n      options.children,\n      options.rehypePlugins,\n      options.remarkPlugins,\n      options.remarkRehypeOptions\n    ]\n  )\n\n  if (error) throw error\n\n  return tree ? post(tree, options) : options.fallback\n}\n\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */\nfunction createProcessor(options) {\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n\n  const processor = unified()\n    .use(remarkParse)\n    .use(remarkPlugins)\n    .use(remarkRehype, remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  return processor\n}\n\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */\nfunction createFile(options) {\n  const children = options.children || ''\n  const file = new VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    unreachable(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  return file\n}\n\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction post(tree, options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      unreachable(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  if (allowedElements && disallowedElements) {\n    unreachable(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  visit(tree, transform)\n\n  return toJsxRuntime(tree, {\n    Fragment,\n    components,\n    ignoreInvalidStyle: true,\n    jsx,\n    jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in urlAttributes) {\n        if (\n          Object.hasOwn(urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nexport function defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n", "/**\n * While micromark is a lexer/tokenizer, the common case of going from markdown\n * to html is currently built in as this module, even though the parts can be\n * used separately to build ASTs, CSTs, or many other output formats.\n *\n * Having an HTML compiler built in is useful because it allows us to check for\n * compliancy to CommonMark, the de facto norm of markdown, specified in roughly\n * 600 input/output cases.\n *\n * This module has an interface that accepts lists of events instead of the\n * whole at once, however, because markdown can’t be truly streaming, we buffer\n * events before processing and outputting the final result.\n */\n\n/**\n * @import {\n *   CompileContext,\n *   CompileData,\n *   CompileOptions,\n *   Compile,\n *   Definition,\n *   Event,\n *   Handle,\n *   HtmlExtension,\n *   LineEnding,\n *   NormalizedHtmlExtension,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef Media\n * @property {boolean | undefined} [image]\n * @property {string | undefined} [labelId]\n * @property {string | undefined} [label]\n * @property {string | undefined} [referenceId]\n * @property {string | undefined} [destination]\n * @property {string | undefined} [title]\n */\n\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {ok as assert} from 'devlop'\nimport {push} from 'micromark-util-chunked'\nimport {combineHtmlExtensions} from 'micromark-util-combine-extensions'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {encode as _encode} from 'micromark-util-encode'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {sanitizeUri} from 'micromark-util-sanitize-uri'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nconst hasOwnProperty = {}.hasOwnProperty\n\n/**\n * These two are allowlists of safe protocols for full URLs in respectively the\n * `href` (on `<a>`) and `src` (on `<img>`) attributes.\n * They are based on what is allowed on GitHub,\n * <https://github.com/syntax-tree/hast-util-sanitize/blob/9275b21/lib/github.json#L31>\n */\nconst protocolHref = /^(https?|ircs?|mailto|xmpp)$/i\nconst protocolSource = /^https?$/i\n\n/**\n * @param {CompileOptions | null | undefined} [options]\n * @returns {Compile}\n */\nexport function compile(options) {\n  const settings = options || {}\n\n  /**\n   * Tags is needed because according to markdown, links and emphasis and\n   * whatnot can exist in images, however, as HTML doesn’t allow content in\n   * images, the tags are ignored in the `alt` attribute, but the content\n   * remains.\n   *\n   * @type {boolean | undefined}\n   */\n  let tags = true\n\n  /**\n   * An object to track identifiers to media (URLs and titles) defined with\n   * definitions.\n   *\n   * @type {Record<string, Definition>}\n   */\n  const definitions = {}\n\n  /**\n   * A lot of the handlers need to capture some of the output data, modify it\n   * somehow, and then deal with it.\n   * We do that by tracking a stack of buffers, that can be opened (with\n   * `buffer`) and closed (with `resume`) to access them.\n   *\n   * @type {Array<Array<string>>}\n   */\n  const buffers = [[]]\n\n  /**\n   * As we can have links in images and the other way around, where the deepest\n   * ones are closed first, we need to track which one we’re in.\n   *\n   * @type {Array<Media>}\n   */\n  const mediaStack = []\n\n  /**\n   * Same as `mediaStack` for tightness, which is specific to lists.\n   * We need to track if we’re currently in a tight or loose container.\n   *\n   * @type {Array<boolean>}\n   */\n  const tightStack = []\n\n  /** @type {HtmlExtension} */\n  const defaultHandlers = {\n    enter: {\n      blockQuote: onenterblockquote,\n      codeFenced: onentercodefenced,\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: onentercodeindented,\n      codeText: onentercodetext,\n      content: onentercontent,\n      definition: onenterdefinition,\n      definitionDestinationString: onenterdefinitiondestinationstring,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: onenteremphasis,\n      htmlFlow: onenterhtmlflow,\n      htmlText: onenterhtml,\n      image: onenterimage,\n      label: buffer,\n      link: onenterlink,\n      listItemMarker: onenterlistitemmarker,\n      listItemValue: onenterlistitemvalue,\n      listOrdered: onenterlistordered,\n      listUnordered: onenterlistunordered,\n      paragraph: onenterparagraph,\n      reference: buffer,\n      resource: onenterresource,\n      resourceDestinationString: onenterresourcedestinationstring,\n      resourceTitleString: buffer,\n      setextHeading: onentersetextheading,\n      strong: onenterstrong\n    },\n    exit: {\n      atxHeading: onexitatxheading,\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: onexitblockquote,\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: onexitflowcode,\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onresumedrop,\n      codeFlowValue: onexitcodeflowvalue,\n      codeIndented: onexitflowcode,\n      codeText: onexitcodetext,\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: onexitdefinition,\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: onexitemphasis,\n      hardBreakEscape: onexithardbreak,\n      hardBreakTrailing: onexithardbreak,\n      htmlFlow: onexithtml,\n      htmlFlowData: onexitdata,\n      htmlText: onexithtml,\n      htmlTextData: onexitdata,\n      image: onexitmedia,\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: onexitmedia,\n      listOrdered: onexitlistordered,\n      listUnordered: onexitlistunordered,\n      paragraph: onexitparagraph,\n      reference: onresumedrop,\n      referenceString: onexitreferencestring,\n      resource: onresumedrop,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      setextHeading: onexitsetextheading,\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: onexitstrong,\n      thematicBreak: onexitthematicbreak\n    }\n  }\n\n  /**\n   * Combine the HTML extensions with the default handlers.\n   * An HTML extension is an object whose fields are either `enter` or `exit`\n   * (reflecting whether a token is entered or exited).\n   * The values at such objects are names of tokens mapping to handlers.\n   * Handlers are called, respectively when a token is opener or closed, with\n   * that token, and a context as `this`.\n   */\n  const handlers = /** @type {NormalizedHtmlExtension} */ (\n    combineHtmlExtensions([defaultHandlers, ...(settings.htmlExtensions || [])])\n  )\n\n  /**\n   * Handlers do often need to keep track of some state.\n   * That state is provided here as a key-value store (an object).\n   *\n   * @type {CompileData}\n   */\n  const data = {\n    definitions,\n    tightStack\n  }\n\n  /**\n   * The context for handlers references a couple of useful functions.\n   * In handlers from extensions, those can be accessed at `this`.\n   * For the handlers here, they can be accessed directly.\n   *\n   * @type {Omit<CompileContext, 'sliceSerialize'>}\n   */\n  const context = {\n    buffer,\n    encode,\n    getData,\n    lineEndingIfNeeded,\n    options: settings,\n    raw,\n    resume,\n    setData,\n    tag\n  }\n\n  /**\n   * Generally, micromark copies line endings (`'\\r'`, `'\\n'`, `'\\r\\n'`) in the\n   * markdown document over to the compiled HTML.\n   * In some cases, such as `> a`, CommonMark requires that extra line endings\n   * are added: `<blockquote>\\n<p>a</p>\\n</blockquote>`.\n   * This variable hold the default line ending when given (or `undefined`),\n   * and in the latter case will be updated to the first found line ending if\n   * there is one.\n   */\n  let lineEndingStyle = settings.defaultLineEnding\n\n  // Return the function that handles a slice of events.\n  return compile\n\n  /**\n   * Deal w/ a slice of events.\n   * Return either the empty string if there’s nothing of note to return, or the\n   * result when done.\n   *\n   * @param {ReadonlyArray<Event>} events\n   * @returns {string}\n   */\n  function compile(events) {\n    let index = -1\n    let start = 0\n    /** @type {Array<number>} */\n    const listStack = []\n    // As definitions can come after references, we need to figure out the media\n    // (urls and titles) defined by them before handling the references.\n    // So, we do sort of what HTML does: put metadata at the start (in head), and\n    // then put content after (`body`).\n    /** @type {Array<Event>} */\n    let head = []\n    /** @type {Array<Event>} */\n    let body = []\n\n    while (++index < events.length) {\n      // Figure out the line ending style used in the document.\n      if (\n        !lineEndingStyle &&\n        (events[index][1].type === types.lineEnding ||\n          events[index][1].type === types.lineEndingBlank)\n      ) {\n        lineEndingStyle = /** @type {LineEnding} */ (\n          events[index][2].sliceSerialize(events[index][1])\n        )\n      }\n\n      // Preprocess lists to infer whether the list is loose or not.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          prepareList(events.slice(listStack.pop(), index))\n        }\n      }\n\n      // Move definitions to the front.\n      if (events[index][1].type === types.definition) {\n        if (events[index][0] === 'enter') {\n          body = push(body, events.slice(start, index))\n          start = index\n        } else {\n          head = push(head, events.slice(start, index + 1))\n          start = index + 1\n        }\n      }\n    }\n\n    head = push(head, body)\n    head = push(head, events.slice(start))\n    index = -1\n    const result = head\n\n    // Handle the start of the document, if defined.\n    if (handlers.enter.null) {\n      handlers.enter.null.call(context)\n    }\n\n    // Handle all events.\n    while (++index < events.length) {\n      const handles = handlers[result[index][0]]\n      const kind = result[index][1].type\n      const handle = handles[kind]\n\n      if (hasOwnProperty.call(handles, kind) && handle) {\n        handle.call(\n          {sliceSerialize: result[index][2].sliceSerialize, ...context},\n          result[index][1]\n        )\n      }\n    }\n\n    // Handle the end of the document, if defined.\n    if (handlers.exit.null) {\n      handlers.exit.null.call(context)\n    }\n\n    return buffers[0].join('')\n  }\n\n  /**\n   * Figure out whether lists are loose or not.\n   *\n   * @param {ReadonlyArray<Event>} slice\n   * @returns {undefined}\n   */\n  function prepareList(slice) {\n    const length = slice.length\n    let index = 0 // Skip open.\n    let containerBalance = 0\n    let loose = false\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index < length) {\n      const event = slice[index]\n\n      if (event[1]._container) {\n        atMarker = undefined\n\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n      } else\n        switch (event[1].type) {\n          case types.listItemPrefix: {\n            if (event[0] === 'exit') {\n              atMarker = true\n            }\n\n            break\n          }\n\n          case types.linePrefix: {\n            // Ignore\n\n            break\n          }\n\n          case types.lineEndingBlank: {\n            if (event[0] === 'enter' && !containerBalance) {\n              if (atMarker) {\n                atMarker = undefined\n              } else {\n                loose = true\n              }\n            }\n\n            break\n          }\n\n          default: {\n            atMarker = undefined\n          }\n        }\n    }\n\n    slice[0][1]._loose = loose\n  }\n\n  /**\n   * @type {CompileContext['setData']}\n   */\n  function setData(key, value) {\n    // @ts-expect-error: assume `value` is omitted (`undefined` is passed) only\n    // if allowed.\n    data[key] = value\n  }\n\n  /**\n   * @type {CompileContext['getData']}\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /** @type {CompileContext['buffer']} */\n  function buffer() {\n    buffers.push([])\n  }\n\n  /** @type {CompileContext['resume']} */\n  function resume() {\n    const buf = buffers.pop()\n    assert(buf !== undefined, 'Cannot resume w/o buffer')\n    return buf.join('')\n  }\n\n  /** @type {CompileContext['tag']} */\n  function tag(value) {\n    if (!tags) return\n    setData('lastWasTag', true)\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /** @type {CompileContext['raw']} */\n  function raw(value) {\n    setData('lastWasTag')\n    buffers[buffers.length - 1].push(value)\n  }\n\n  /**\n   * Output an extra line ending.\n   *\n   * @returns {undefined}\n   */\n  function lineEnding() {\n    raw(lineEndingStyle || '\\n')\n  }\n\n  /** @type {CompileContext['lineEndingIfNeeded']} */\n  function lineEndingIfNeeded() {\n    const buffer = buffers[buffers.length - 1]\n    const slice = buffer[buffer.length - 1]\n    const previous = slice ? slice.charCodeAt(slice.length - 1) : codes.eof\n\n    if (\n      previous === codes.lf ||\n      previous === codes.cr ||\n      previous === codes.eof\n    ) {\n      return\n    }\n\n    lineEnding()\n  }\n\n  /** @type {CompileContext['encode']} */\n  function encode(value) {\n    return getData('ignoreEncode') ? value : _encode(value)\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @returns {undefined}\n   */\n  function onresumedrop() {\n    resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ol')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistunordered(token) {\n    tightStack.push(!token._loose)\n    lineEndingIfNeeded()\n    tag('<ul')\n    setData('expectFirstItem', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectFirstItem')) {\n      const value = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n\n      if (value !== 1) {\n        tag(' start=\"' + encode(String(value)) + '\"')\n      }\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterlistitemmarker() {\n    if (getData('expectFirstItem')) {\n      tag('>')\n    } else {\n      onexitlistitem()\n    }\n\n    lineEndingIfNeeded()\n    tag('<li>')\n    setData('expectFirstItem')\n    // “Hack” to prevent a line ending from showing up if the item is empty.\n    setData('lastWasTag')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ol>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistunordered() {\n    onexitlistitem()\n    tightStack.pop()\n    lineEnding()\n    tag('</ul>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitlistitem() {\n    if (getData('lastWasTag') && !getData('slurpAllLineEndings')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</li>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterblockquote() {\n    tightStack.push(false)\n    lineEndingIfNeeded()\n    tag('<blockquote>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitblockquote() {\n    tightStack.pop()\n    lineEndingIfNeeded()\n    tag('</blockquote>')\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterparagraph() {\n    if (!tightStack[tightStack.length - 1]) {\n      lineEndingIfNeeded()\n      tag('<p>')\n    }\n\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitparagraph() {\n    if (tightStack[tightStack.length - 1]) {\n      setData('slurpAllLineEndings', true)\n    } else {\n      tag('</p>')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodefenced() {\n    lineEndingIfNeeded()\n    tag('<pre><code')\n    setData('fencesCount', 0)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const value = resume()\n    tag(' class=\"language-' + value + '\"')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    const count = getData('fencesCount') || 0\n\n    if (!count) {\n      tag('>')\n      setData('slurpOneLineEnding', true)\n    }\n\n    setData('fencesCount', count + 1)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercodeindented() {\n    lineEndingIfNeeded()\n    tag('<pre><code>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitflowcode() {\n    const count = getData('fencesCount')\n\n    // One special case is if we are inside a container, and the fenced code was\n    // not closed (meaning it runs to the end).\n    // In that case, the following line ending, is considered *outside* the\n    // fenced code and block quote by micromark, but CM wants to treat that\n    // ending as part of the code.\n    if (\n      count !== undefined &&\n      count < 2 &&\n      data.tightStack.length > 0 &&\n      !getData('lastWasTag')\n    ) {\n      lineEnding()\n    }\n\n    // But in most cases, it’s simpler: when we’ve seen some data, emit an extra\n    // line ending when needed.\n    if (getData('flowCodeSeenData')) {\n      lineEndingIfNeeded()\n    }\n\n    tag('</code></pre>')\n    if (count !== undefined && count < 2) lineEndingIfNeeded()\n    setData('flowCodeSeenData')\n    setData('fencesCount')\n    setData('slurpOneLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterimage() {\n    mediaStack.push({image: true})\n    tags = undefined // Disallow tags.\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlink() {\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabeltext(token) {\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlabel() {\n    mediaStack[mediaStack.length - 1].label = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitreferencestring(token) {\n    mediaStack[mediaStack.length - 1].referenceId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresource() {\n    buffer() // We can have line endings in the resource, ignore them.\n    mediaStack[mediaStack.length - 1].destination = ''\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterresourcedestinationstring() {\n    buffer()\n    // Ignore encoding the result, as we’ll first percent encode the url and\n    // encode manually after.\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcedestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitresourcetitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitmedia() {\n    let index = mediaStack.length - 1 // Skip current.\n    const media = mediaStack[index]\n    const id = media.referenceId || media.labelId\n    assert(id !== undefined, 'media should have `referenceId` or `labelId`')\n    assert(media.label !== undefined, 'media should have `label`')\n    const context =\n      media.destination === undefined\n        ? definitions[normalizeIdentifier(id)]\n        : media\n\n    tags = true\n\n    while (index--) {\n      if (mediaStack[index].image) {\n        tags = undefined\n        break\n      }\n    }\n\n    if (media.image) {\n      tag(\n        '<img src=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolSource\n          ) +\n          '\" alt=\"'\n      )\n      raw(media.label)\n      tag('\"')\n    } else {\n      tag(\n        '<a href=\"' +\n          sanitizeUri(\n            context.destination,\n            settings.allowDangerousProtocol ? undefined : protocolHref\n          ) +\n          '\"'\n      )\n    }\n\n    tag(context.title ? ' title=\"' + context.title + '\"' : '')\n\n    if (media.image) {\n      tag(' />')\n    } else {\n      tag('>')\n      raw(media.label)\n      tag('</a>')\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinition() {\n    buffer()\n    mediaStack.push({})\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    // Discard label, use the source content instead.\n    resume()\n    mediaStack[mediaStack.length - 1].labelId = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterdefinitiondestinationstring() {\n    buffer()\n    setData('ignoreEncode', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    mediaStack[mediaStack.length - 1].destination = resume()\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    mediaStack[mediaStack.length - 1].title = resume()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinition() {\n    const media = mediaStack[mediaStack.length - 1]\n    assert(media.labelId !== undefined, 'media should have `labelId`')\n    const id = normalizeIdentifier(media.labelId)\n\n    resume()\n\n    if (!hasOwnProperty.call(definitions, id)) {\n      definitions[id] = mediaStack[mediaStack.length - 1]\n    }\n\n    mediaStack.pop()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentercontent() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    // Exit for further sequences.\n    if (getData('headingRank')) return\n    setData('headingRank', this.sliceSerialize(token).length)\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onentersetextheading() {\n    buffer()\n    setData('slurpAllLineEndings')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('slurpAllLineEndings', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheading() {\n    tag('</h' + getData('headingRank') + '>')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    setData(\n      'headingRank',\n      this.sliceSerialize(token).charCodeAt(0) === codes.equalsTo ? 1 : 2\n    )\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    const value = resume()\n    lineEndingIfNeeded()\n    tag('<h' + getData('headingRank') + '>')\n    raw(value)\n    tag('</h' + getData('headingRank') + '>')\n    setData('slurpAllLineEndings')\n    setData('headingRank')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdata(token) {\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitlineending(token) {\n    if (getData('slurpAllLineEndings')) {\n      return\n    }\n\n    if (getData('slurpOneLineEnding')) {\n      setData('slurpOneLineEnding')\n      return\n    }\n\n    if (getData('inCodeText')) {\n      raw(' ')\n      return\n    }\n\n    raw(encode(this.sliceSerialize(token)))\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeflowvalue(token) {\n    raw(encode(this.sliceSerialize(token)))\n    setData('flowCodeSeenData', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexithardbreak() {\n    tag('<br />')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtmlflow() {\n    lineEndingIfNeeded()\n    onenterhtml()\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexithtml() {\n    setData('ignoreEncode')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterhtml() {\n    if (settings.allowDangerousHtml) {\n      setData('ignoreEncode', true)\n    }\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenteremphasis() {\n    tag('<em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onenterstrong() {\n    tag('<strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onentercodetext() {\n    setData('inCodeText', true)\n    tag('<code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitcodetext() {\n    setData('inCodeText')\n    tag('</code>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitemphasis() {\n    tag('</em>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitstrong() {\n    tag('</strong>')\n  }\n\n  /**\n   * @returns {undefined}\n   */\n  function onexitthematicbreak() {\n    lineEndingIfNeeded()\n    tag('<hr />')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @param {Token} token\n   * @returns {undefined}\n   */\n  function onexitcharacterreferencemarker(token) {\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const value = this.sliceSerialize(token)\n    const decoded = getData('characterReferenceType')\n      ? decodeNumericCharacterReference(\n          value,\n          getData('characterReferenceType') ===\n            types.characterReferenceMarkerNumeric\n            ? constants.numericBaseDecimal\n            : constants.numericBaseHexadecimal\n        )\n      : decodeNamedCharacterReference(value)\n\n    // `decodeNamedCharacterReference` can return `false` for invalid named\n    // character references,\n    // but everything we’ve tokenized is valid.\n    raw(encode(/** @type {string} */ (decoded)))\n    setData('characterReferenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    const uri = this.sliceSerialize(token)\n    tag(\n      '<a href=\"' +\n        sanitizeUri(\n          uri,\n          settings.allowDangerousProtocol ? undefined : protocolHref\n        ) +\n        '\">'\n    )\n    raw(encode(uri))\n    tag('</a>')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    const uri = this.sliceSerialize(token)\n    tag('<a href=\"' + sanitizeUri('mailto:' + uri) + '\">')\n    raw(encode(uri))\n    tag('</a>')\n  }\n}\n", "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const content = {tokenize: initializeContent}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Initializer}\n *   Content.\n */\nfunction initializeContent(effects) {\n  const contentStart = effects.attempt(\n    this.parser.constructs.contentInitial,\n    afterContentStartConstruct,\n    paragraphInitial\n  )\n  /** @type {Token} */\n  let previous\n\n  return contentStart\n\n  /** @type {State} */\n  function afterContentStartConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    return factorySpace(effects, contentStart, types.linePrefix)\n  }\n\n  /** @type {State} */\n  function paragraphInitial(code) {\n    assert(\n      code !== codes.eof && !markdownLineEnding(code),\n      'expected anything other than a line ending or EOF'\n    )\n    effects.enter(types.paragraph)\n    return lineStart(code)\n  }\n\n  /** @type {State} */\n  function lineStart(code) {\n    const token = effects.enter(types.chunkText, {\n      contentType: constants.contentTypeText,\n      previous\n    })\n\n    if (previous) {\n      previous.next = token\n    }\n\n    previous = token\n\n    return data(code)\n  }\n\n  /** @type {State} */\n  function data(code) {\n    if (code === codes.eof) {\n      effects.exit(types.chunkText)\n      effects.exit(types.paragraph)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      effects.exit(types.chunkText)\n      return lineStart\n    }\n\n    // Data.\n    effects.consume(code)\n    return data\n  }\n}\n", "/**\n * @import {\n *   Construct,\n *   ContainerState,\n *   InitialConstruct,\n *   Initializer,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Tokenizer,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @typedef {[Construct, ContainerState]} StackItem\n *   Construct and its state.\n */\n\nimport {ok as assert} from 'devlop'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {splice} from 'micromark-util-chunked'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const document = {tokenize: initializeDocument}\n\n/** @type {Construct} */\nconst containerConstruct = {tokenize: tokenizeContainer}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeDocument(effects) {\n  const self = this\n  /** @type {Array<StackItem>} */\n  const stack = []\n  let continued = 0\n  /** @type {TokenizeContext | undefined} */\n  let childFlow\n  /** @type {Token | undefined} */\n  let childToken\n  /** @type {number} */\n  let lineStartOffset\n\n  return start\n\n  /** @type {State} */\n  function start(code) {\n    // First we iterate through the open blocks, starting with the root\n    // document, and descending through last children down to the last open\n    // block.\n    // Each block imposes a condition that the line must satisfy if the block is\n    // to remain open.\n    // For example, a block quote requires a `>` character.\n    // A paragraph requires a non-blank line.\n    // In this phase we may match all or just some of the open blocks.\n    // But we cannot close unmatched blocks yet, because we may have a lazy\n    // continuation line.\n    if (continued < stack.length) {\n      const item = stack[continued]\n      self.containerState = item[1]\n      assert(\n        item[0].continuation,\n        'expected `continuation` to be defined on container construct'\n      )\n      return effects.attempt(\n        item[0].continuation,\n        documentContinue,\n        checkNewContainers\n      )(code)\n    }\n\n    // Done.\n    return checkNewContainers(code)\n  }\n\n  /** @type {State} */\n  function documentContinue(code) {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined after continuation'\n    )\n\n    continued++\n\n    // Note: this field is called `_closeFlow` but it also closes containers.\n    // Perhaps a good idea to rename it but it’s already used in the wild by\n    // extensions.\n    if (self.containerState._closeFlow) {\n      self.containerState._closeFlow = undefined\n\n      if (childFlow) {\n        closeFlow()\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when dealing with lazy lines in `writeToChild`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the flow chunk.\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          point = self.events[indexBeforeFlow][1].end\n          break\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      let index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n\n      return checkNewContainers(code)\n    }\n\n    return start(code)\n  }\n\n  /** @type {State} */\n  function checkNewContainers(code) {\n    // Next, after consuming the continuation markers for existing blocks, we\n    // look for new block starts (e.g. `>` for a block quote).\n    // If we encounter a new block start, we close any blocks unmatched in\n    // step 1 before creating the new block as a child of the last matched\n    // block.\n    if (continued === stack.length) {\n      // No need to `check` whether there’s a container, of `exitContainers`\n      // would be moot.\n      // We can instead immediately `attempt` to parse one.\n      if (!childFlow) {\n        return documentContinued(code)\n      }\n\n      // If we have concrete content, such as block HTML or fenced code,\n      // we can’t have containers “pierce” into them, so we can immediately\n      // start.\n      if (childFlow.currentConstruct && childFlow.currentConstruct.concrete) {\n        return flowStart(code)\n      }\n\n      // If we do have flow, it could still be a blank line,\n      // but we’d be interrupting it w/ a new container if there’s a current\n      // construct.\n      // To do: next major: remove `_gfmTableDynamicInterruptHack` (no longer\n      // needed in micromark-extension-gfm-table@1.0.6).\n      self.interrupt = Boolean(\n        childFlow.currentConstruct && !childFlow._gfmTableDynamicInterruptHack\n      )\n    }\n\n    // Check if there is a new container.\n    self.containerState = {}\n    return effects.check(\n      containerConstruct,\n      thereIsANewContainer,\n      thereIsNoNewContainer\n    )(code)\n  }\n\n  /** @type {State} */\n  function thereIsANewContainer(code) {\n    if (childFlow) closeFlow()\n    exitContainers(continued)\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function thereIsNoNewContainer(code) {\n    self.parser.lazy[self.now().line] = continued !== stack.length\n    lineStartOffset = self.now().offset\n    return flowStart(code)\n  }\n\n  /** @type {State} */\n  function documentContinued(code) {\n    // Try new containers.\n    self.containerState = {}\n    return effects.attempt(\n      containerConstruct,\n      containerContinue,\n      flowStart\n    )(code)\n  }\n\n  /** @type {State} */\n  function containerContinue(code) {\n    assert(\n      self.currentConstruct,\n      'expected `currentConstruct` to be defined on tokenizer'\n    )\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined on tokenizer'\n    )\n    continued++\n    stack.push([self.currentConstruct, self.containerState])\n    // Try another.\n    return documentContinued(code)\n  }\n\n  /** @type {State} */\n  function flowStart(code) {\n    if (code === codes.eof) {\n      if (childFlow) closeFlow()\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    childFlow = childFlow || self.parser.flow(self.now())\n    effects.enter(types.chunkFlow, {\n      _tokenizer: childFlow,\n      contentType: constants.contentTypeFlow,\n      previous: childToken\n    })\n\n    return flowContinue(code)\n  }\n\n  /** @type {State} */\n  function flowContinue(code) {\n    if (code === codes.eof) {\n      writeToChild(effects.exit(types.chunkFlow), true)\n      exitContainers(0)\n      effects.consume(code)\n      return\n    }\n\n    if (markdownLineEnding(code)) {\n      effects.consume(code)\n      writeToChild(effects.exit(types.chunkFlow))\n      // Get ready for the next line.\n      continued = 0\n      self.interrupt = undefined\n      return start\n    }\n\n    effects.consume(code)\n    return flowContinue\n  }\n\n  /**\n   * @param {Token} token\n   *   Token.\n   * @param {boolean | undefined} [endOfFile]\n   *   Whether the token is at the end of the file (default: `false`).\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function writeToChild(token, endOfFile) {\n    assert(childFlow, 'expected `childFlow` to be defined when continuing')\n    const stream = self.sliceStream(token)\n    if (endOfFile) stream.push(null)\n    token.previous = childToken\n    if (childToken) childToken.next = token\n    childToken = token\n    childFlow.defineSkip(token.start)\n    childFlow.write(stream)\n\n    // Alright, so we just added a lazy line:\n    //\n    // ```markdown\n    // > a\n    // b.\n    //\n    // Or:\n    //\n    // > ~~~c\n    // d\n    //\n    // Or:\n    //\n    // > | e |\n    // f\n    // ```\n    //\n    // The construct in the second example (fenced code) does not accept lazy\n    // lines, so it marked itself as done at the end of its first line, and\n    // then the content construct parses `d`.\n    // Most constructs in markdown match on the first line: if the first line\n    // forms a construct, a non-lazy line can’t “unmake” it.\n    //\n    // The construct in the third example is potentially a GFM table, and\n    // those are *weird*.\n    // It *could* be a table, from the first line, if the following line\n    // matches a condition.\n    // In this case, that second line is lazy, which “unmakes” the first line\n    // and turns the whole into one content block.\n    //\n    // We’ve now parsed the non-lazy and the lazy line, and can figure out\n    // whether the lazy line started a new flow block.\n    // If it did, we exit the current containers between the two flow blocks.\n    if (self.parser.lazy[token.start.line]) {\n      let index = childFlow.events.length\n\n      while (index--) {\n        if (\n          // The token starts before the line ending…\n          childFlow.events[index][1].start.offset < lineStartOffset &&\n          // …and either is not ended yet…\n          (!childFlow.events[index][1].end ||\n            // …or ends after it.\n            childFlow.events[index][1].end.offset > lineStartOffset)\n        ) {\n          // Exit: there’s still something open, which means it’s a lazy line\n          // part of something.\n          return\n        }\n      }\n\n      // Note: this algorithm for moving events around is similar to the\n      // algorithm when closing flow in `documentContinue`.\n      const indexBeforeExits = self.events.length\n      let indexBeforeFlow = indexBeforeExits\n      /** @type {boolean | undefined} */\n      let seen\n      /** @type {Point | undefined} */\n      let point\n\n      // Find the previous chunk (the one before the lazy line).\n      while (indexBeforeFlow--) {\n        if (\n          self.events[indexBeforeFlow][0] === 'exit' &&\n          self.events[indexBeforeFlow][1].type === types.chunkFlow\n        ) {\n          if (seen) {\n            point = self.events[indexBeforeFlow][1].end\n            break\n          }\n\n          seen = true\n        }\n      }\n\n      assert(point, 'could not find previous flow chunk')\n\n      exitContainers(continued)\n\n      // Fix positions.\n      index = indexBeforeExits\n\n      while (index < self.events.length) {\n        self.events[index][1].end = {...point}\n        index++\n      }\n\n      // Inject the exits earlier (they’re still also at the end).\n      splice(\n        self.events,\n        indexBeforeFlow + 1,\n        0,\n        self.events.slice(indexBeforeExits)\n      )\n\n      // Discard the duplicate exits.\n      self.events.length = index\n    }\n  }\n\n  /**\n   * @param {number} size\n   *   Size.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function exitContainers(size) {\n    let index = stack.length\n\n    // Exit open containers.\n    while (index-- > size) {\n      const entry = stack[index]\n      self.containerState = entry[1]\n      assert(\n        entry[0].exit,\n        'expected `exit` to be defined on container construct'\n      )\n      entry[0].exit.call(self, effects)\n    }\n\n    stack.length = size\n  }\n\n  function closeFlow() {\n    assert(\n      self.containerState,\n      'expected `containerState` to be defined when closing flow'\n    )\n    assert(childFlow, 'expected `childFlow` to be defined when closing it')\n    childFlow.write([codes.eof])\n    childToken = undefined\n    childFlow = undefined\n    self.containerState._closeFlow = undefined\n  }\n}\n\n/**\n * @this {TokenizeContext}\n *   Context.\n * @type {Tokenizer}\n *   Tokenizer.\n */\nfunction tokenizeContainer(effects, ok, nok) {\n  // Always populated by defaults.\n  assert(\n    this.parser.constructs.disable.null,\n    'expected `disable.null` to be populated'\n  )\n  return factorySpace(\n    effects,\n    effects.attempt(this.parser.constructs.document, ok, nok),\n    types.linePrefix,\n    this.parser.constructs.disable.null.includes('codeIndented')\n      ? undefined\n      : constants.tabSize\n  )\n}\n", "/**\n * @import {\n *   InitialConstruct,\n *   Initializer,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {blankLine, content} from 'micromark-core-commonmark'\nimport {factorySpace} from 'micromark-factory-space'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {codes, types} from 'micromark-util-symbol'\n\n/** @type {InitialConstruct} */\nexport const flow = {tokenize: initializeFlow}\n\n/**\n * @this {TokenizeContext}\n *   Self.\n * @type {Initializer}\n *   Initializer.\n */\nfunction initializeFlow(effects) {\n  const self = this\n  const initial = effects.attempt(\n    // Try to parse a blank line.\n    blankLine,\n    atBlankEnding,\n    // Try to parse initial flow (essentially, only code).\n    effects.attempt(\n      this.parser.constructs.flowInitial,\n      afterConstruct,\n      factorySpace(\n        effects,\n        effects.attempt(\n          this.parser.constructs.flow,\n          afterConstruct,\n          effects.attempt(content, afterConstruct)\n        ),\n        types.linePrefix\n      )\n    )\n  )\n\n  return initial\n\n  /** @type {State} */\n  function atBlankEnding(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEndingBlank)\n    effects.consume(code)\n    effects.exit(types.lineEndingBlank)\n    self.currentConstruct = undefined\n    return initial\n  }\n\n  /** @type {State} */\n  function afterConstruct(code) {\n    assert(\n      code === codes.eof || markdownLineEnding(code),\n      'expected eol or eof'\n    )\n\n    if (code === codes.eof) {\n      effects.consume(code)\n      return\n    }\n\n    effects.enter(types.lineEnding)\n    effects.consume(code)\n    effects.exit(types.lineEnding)\n    self.currentConstruct = undefined\n    return initial\n  }\n}\n", "/**\n * @import {\n *   Code,\n *   InitialConstruct,\n *   Initializer,\n *   Resolver,\n *   State,\n *   TokenizeContext\n * } from 'micromark-util-types'\n */\n\nimport {ok as assert} from 'devlop'\nimport {codes, constants, types} from 'micromark-util-symbol'\n\nexport const resolver = {resolveAll: createResolver()}\nexport const string = initializeFactory('string')\nexport const text = initializeFactory('text')\n\n/**\n * @param {'string' | 'text'} field\n *   Field.\n * @returns {InitialConstruct}\n *   Construct.\n */\nfunction initializeFactory(field) {\n  return {\n    resolveAll: createResolver(\n      field === 'text' ? resolveAllLineSuffixes : undefined\n    ),\n    tokenize: initializeText\n  }\n\n  /**\n   * @this {TokenizeContext}\n   *   Context.\n   * @type {Initializer}\n   */\n  function initializeText(effects) {\n    const self = this\n    const constructs = this.parser.constructs[field]\n    const text = effects.attempt(constructs, start, notText)\n\n    return start\n\n    /** @type {State} */\n    function start(code) {\n      return atBreak(code) ? text(code) : notText(code)\n    }\n\n    /** @type {State} */\n    function notText(code) {\n      if (code === codes.eof) {\n        effects.consume(code)\n        return\n      }\n\n      effects.enter(types.data)\n      effects.consume(code)\n      return data\n    }\n\n    /** @type {State} */\n    function data(code) {\n      if (atBreak(code)) {\n        effects.exit(types.data)\n        return text(code)\n      }\n\n      // Data.\n      effects.consume(code)\n      return data\n    }\n\n    /**\n     * @param {Code} code\n     *   Code.\n     * @returns {boolean}\n     *   Whether the code is a break.\n     */\n    function atBreak(code) {\n      if (code === codes.eof) {\n        return true\n      }\n\n      const list = constructs[code]\n      let index = -1\n\n      if (list) {\n        // Always populated by defaults.\n        assert(Array.isArray(list), 'expected `disable.null` to be populated')\n\n        while (++index < list.length) {\n          const item = list[index]\n          if (!item.previous || item.previous.call(self, self.previous)) {\n            return true\n          }\n        }\n      }\n\n      return false\n    }\n  }\n}\n\n/**\n * @param {Resolver | undefined} [extraResolver]\n *   Resolver.\n * @returns {Resolver}\n *   Resolver.\n */\nfunction createResolver(extraResolver) {\n  return resolveAllText\n\n  /** @type {Resolver} */\n  function resolveAllText(events, context) {\n    let index = -1\n    /** @type {number | undefined} */\n    let enter\n\n    // A rather boring computation (to merge adjacent `data` events) which\n    // improves mm performance by 29%.\n    while (++index <= events.length) {\n      if (enter === undefined) {\n        if (events[index] && events[index][1].type === types.data) {\n          enter = index\n          index++\n        }\n      } else if (!events[index] || events[index][1].type !== types.data) {\n        // Don’t do anything if there is one data token.\n        if (index !== enter + 2) {\n          events[enter][1].end = events[index - 1][1].end\n          events.splice(enter + 2, index - enter - 2)\n          index = enter + 2\n        }\n\n        enter = undefined\n      }\n    }\n\n    return extraResolver ? extraResolver(events, context) : events\n  }\n}\n\n/**\n * A rather ugly set of instructions which again looks at chunks in the input\n * stream.\n * The reason to do this here is that it is *much* faster to parse in reverse.\n * And that we can’t hook into `null` to split the line suffix before an EOF.\n * To do: figure out if we can make this into a clean utility, or even in core.\n * As it will be useful for GFMs literal autolink extension (and maybe even\n * tables?)\n *\n * @type {Resolver}\n */\nfunction resolveAllLineSuffixes(events, context) {\n  let eventIndex = 0 // Skip first.\n\n  while (++eventIndex <= events.length) {\n    if (\n      (eventIndex === events.length ||\n        events[eventIndex][1].type === types.lineEnding) &&\n      events[eventIndex - 1][1].type === types.data\n    ) {\n      const data = events[eventIndex - 1][1]\n      const chunks = context.sliceStream(data)\n      let index = chunks.length\n      let bufferIndex = -1\n      let size = 0\n      /** @type {boolean | undefined} */\n      let tabs\n\n      while (index--) {\n        const chunk = chunks[index]\n\n        if (typeof chunk === 'string') {\n          bufferIndex = chunk.length\n\n          while (chunk.charCodeAt(bufferIndex - 1) === codes.space) {\n            size++\n            bufferIndex--\n          }\n\n          if (bufferIndex) break\n          bufferIndex = -1\n        }\n        // Number\n        else if (chunk === codes.horizontalTab) {\n          tabs = true\n          size++\n        } else if (chunk === codes.virtualSpace) {\n          // Empty\n        } else {\n          // Replacement character, exit.\n          index++\n          break\n        }\n      }\n\n      // Allow final trailing whitespace.\n      if (context._contentTypeTextTrailing && eventIndex === events.length) {\n        size = 0\n      }\n\n      if (size) {\n        const token = {\n          type:\n            eventIndex === events.length ||\n            tabs ||\n            size < constants.hardBreakPrefixSizeMin\n              ? types.lineSuffix\n              : types.hardBreakTrailing,\n          start: {\n            _bufferIndex: index\n              ? bufferIndex\n              : data.start._bufferIndex + bufferIndex,\n            _index: data.start._index + index,\n            line: data.end.line,\n            column: data.end.column - size,\n            offset: data.end.offset - size\n          },\n          end: {...data.end}\n        }\n\n        data.end = {...token.start}\n\n        if (data.start.offset === data.end.offset) {\n          Object.assign(data, token)\n        } else {\n          events.splice(\n            eventIndex,\n            0,\n            ['enter', token, context],\n            ['exit', token, context]\n          )\n          eventIndex += 2\n        }\n      }\n\n      eventIndex++\n    }\n  }\n\n  return events\n}\n", "/**\n * @import {Extension} from 'micromark-util-types'\n */\n\nimport {\n  attention,\n  autolink,\n  blockQuote,\n  characterEscape,\n  characterReference,\n  codeFenced,\n  codeIndented,\n  codeText,\n  definition,\n  hardBreakEscape,\n  headingAtx,\n  htmlFlow,\n  htmlText,\n  labelEnd,\n  labelStartImage,\n  labelStartLink,\n  lineEnding,\n  list,\n  setextUnderline,\n  thematicBreak\n} from 'micromark-core-commonmark'\nimport {codes} from 'micromark-util-symbol'\nimport {resolver as resolveText} from './initialize/text.js'\n\n/** @satisfies {Extension['document']} */\nexport const document = {\n  [codes.asterisk]: list,\n  [codes.plusSign]: list,\n  [codes.dash]: list,\n  [codes.digit0]: list,\n  [codes.digit1]: list,\n  [codes.digit2]: list,\n  [codes.digit3]: list,\n  [codes.digit4]: list,\n  [codes.digit5]: list,\n  [codes.digit6]: list,\n  [codes.digit7]: list,\n  [codes.digit8]: list,\n  [codes.digit9]: list,\n  [codes.greaterThan]: blockQuote\n}\n\n/** @satisfies {Extension['contentInitial']} */\nexport const contentInitial = {\n  [codes.leftSquareBracket]: definition\n}\n\n/** @satisfies {Extension['flowInitial']} */\nexport const flowInitial = {\n  [codes.horizontalTab]: codeIndented,\n  [codes.virtualSpace]: codeIndented,\n  [codes.space]: codeIndented\n}\n\n/** @satisfies {Extension['flow']} */\nexport const flow = {\n  [codes.numberSign]: headingAtx,\n  [codes.asterisk]: thematicBreak,\n  [codes.dash]: [setextUnderline, thematicBreak],\n  [codes.lessThan]: htmlFlow,\n  [codes.equalsTo]: setextUnderline,\n  [codes.underscore]: thematicBreak,\n  [codes.graveAccent]: codeFenced,\n  [codes.tilde]: codeFenced\n}\n\n/** @satisfies {Extension['string']} */\nexport const string = {\n  [codes.ampersand]: characterReference,\n  [codes.backslash]: characterEscape\n}\n\n/** @satisfies {Extension['text']} */\nexport const text = {\n  [codes.carriageReturn]: lineEnding,\n  [codes.lineFeed]: lineEnding,\n  [codes.carriageReturnLineFeed]: lineEnding,\n  [codes.exclamationMark]: labelStartImage,\n  [codes.ampersand]: characterReference,\n  [codes.asterisk]: attention,\n  [codes.lessThan]: [autolink, htmlText],\n  [codes.leftSquareBracket]: labelStartLink,\n  [codes.backslash]: [hardBreakEscape, characterEscape],\n  [codes.rightSquareBracket]: labelEnd,\n  [codes.underscore]: attention,\n  [codes.graveAccent]: codeText\n}\n\n/** @satisfies {Extension['insideSpan']} */\nexport const insideSpan = {null: [attention, resolveText]}\n\n/** @satisfies {Extension['attentionMarkers']} */\nexport const attentionMarkers = {null: [codes.asterisk, codes.underscore]}\n\n/** @satisfies {Extension['disable']} */\nexport const disable = {null: []}\n", "/**\n * @import {\n *   Chunk,\n *   Code,\n *   ConstructRecord,\n *   Construct,\n *   Effects,\n *   InitialConstruct,\n *   ParseContext,\n *   Point,\n *   State,\n *   TokenizeContext,\n *   Token\n * } from 'micromark-util-types'\n */\n\n/**\n * @callback Restore\n *   Restore the state.\n * @returns {undefined}\n *   Nothing.\n *\n * @typedef Info\n *   Info.\n * @property {Restore} restore\n *   Restore.\n * @property {number} from\n *   From.\n *\n * @callback ReturnHandle\n *   Handle a successful run.\n * @param {Construct} construct\n *   Construct.\n * @param {Info} info\n *   Info.\n * @returns {undefined}\n *   Nothing.\n */\n\nimport createDebug from 'debug'\nimport {ok as assert} from 'devlop'\nimport {markdownLineEnding} from 'micromark-util-character'\nimport {push, splice} from 'micromark-util-chunked'\nimport {resolveAll} from 'micromark-util-resolve-all'\nimport {codes, values} from 'micromark-util-symbol'\n\nconst debug = createDebug('micromark')\n\n/**\n * Create a tokenizer.\n * Tokenizers deal with one type of data (e.g., containers, flow, text).\n * The parser is the object dealing with it all.\n * `initialize` works like other constructs, except that only its `tokenize`\n * function is used, in which case it doesn’t receive an `ok` or `nok`.\n * `from` can be given to set the point before the first character, although\n * when further lines are indented, they must be set with `defineSkip`.\n *\n * @param {ParseContext} parser\n *   Parser.\n * @param {InitialConstruct} initialize\n *   Construct.\n * @param {Omit<Point, '_bufferIndex' | '_index'> | undefined} [from]\n *   Point (optional).\n * @returns {TokenizeContext}\n *   Context.\n */\nexport function createTokenizer(parser, initialize, from) {\n  /** @type {Point} */\n  let point = {\n    _bufferIndex: -1,\n    _index: 0,\n    line: (from && from.line) || 1,\n    column: (from && from.column) || 1,\n    offset: (from && from.offset) || 0\n  }\n  /** @type {Record<string, number>} */\n  const columnStart = {}\n  /** @type {Array<Construct>} */\n  const resolveAllConstructs = []\n  /** @type {Array<Chunk>} */\n  let chunks = []\n  /** @type {Array<Token>} */\n  let stack = []\n  /** @type {boolean | undefined} */\n  let consumed = true\n\n  /**\n   * Tools used for tokenizing.\n   *\n   * @type {Effects}\n   */\n  const effects = {\n    attempt: constructFactory(onsuccessfulconstruct),\n    check: constructFactory(onsuccessfulcheck),\n    consume,\n    enter,\n    exit,\n    interrupt: constructFactory(onsuccessfulcheck, {interrupt: true})\n  }\n\n  /**\n   * State and tools for resolving and serializing.\n   *\n   * @type {TokenizeContext}\n   */\n  const context = {\n    code: codes.eof,\n    containerState: {},\n    defineSkip,\n    events: [],\n    now,\n    parser,\n    previous: codes.eof,\n    sliceSerialize,\n    sliceStream,\n    write\n  }\n\n  /**\n   * The state function.\n   *\n   * @type {State | undefined}\n   */\n  let state = initialize.tokenize.call(context, effects)\n\n  /**\n   * Track which character we expect to be consumed, to catch bugs.\n   *\n   * @type {Code}\n   */\n  let expectedCode\n\n  if (initialize.resolveAll) {\n    resolveAllConstructs.push(initialize)\n  }\n\n  return context\n\n  /** @type {TokenizeContext['write']} */\n  function write(slice) {\n    chunks = push(chunks, slice)\n\n    main()\n\n    // Exit if we’re not done, resolve might change stuff.\n    if (chunks[chunks.length - 1] !== codes.eof) {\n      return []\n    }\n\n    addResult(initialize, 0)\n\n    // Otherwise, resolve, and exit.\n    context.events = resolveAll(resolveAllConstructs, context.events, context)\n\n    return context.events\n  }\n\n  //\n  // Tools.\n  //\n\n  /** @type {TokenizeContext['sliceSerialize']} */\n  function sliceSerialize(token, expandTabs) {\n    return serializeChunks(sliceStream(token), expandTabs)\n  }\n\n  /** @type {TokenizeContext['sliceStream']} */\n  function sliceStream(token) {\n    return sliceChunks(chunks, token)\n  }\n\n  /** @type {TokenizeContext['now']} */\n  function now() {\n    // This is a hot path, so we clone manually instead of `Object.assign({}, point)`\n    const {_bufferIndex, _index, line, column, offset} = point\n    return {_bufferIndex, _index, line, column, offset}\n  }\n\n  /** @type {TokenizeContext['defineSkip']} */\n  function defineSkip(value) {\n    columnStart[value.line] = value.column\n    accountForPotentialSkip()\n    debug('position: define skip: `%j`', point)\n  }\n\n  //\n  // State management.\n  //\n\n  /**\n   * Main loop (note that `_index` and `_bufferIndex` in `point` are modified by\n   * `consume`).\n   * Here is where we walk through the chunks, which either include strings of\n   * several characters, or numerical character codes.\n   * The reason to do this in a loop instead of a call is so the stack can\n   * drain.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function main() {\n    /** @type {number} */\n    let chunkIndex\n\n    while (point._index < chunks.length) {\n      const chunk = chunks[point._index]\n\n      // If we’re in a buffer chunk, loop through it.\n      if (typeof chunk === 'string') {\n        chunkIndex = point._index\n\n        if (point._bufferIndex < 0) {\n          point._bufferIndex = 0\n        }\n\n        while (\n          point._index === chunkIndex &&\n          point._bufferIndex < chunk.length\n        ) {\n          go(chunk.charCodeAt(point._bufferIndex))\n        }\n      } else {\n        go(chunk)\n      }\n    }\n  }\n\n  /**\n   * Deal with one code.\n   *\n   * @param {Code} code\n   *   Code.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function go(code) {\n    assert(consumed === true, 'expected character to be consumed')\n    consumed = undefined\n    debug('main: passing `%s` to %s', code, state && state.name)\n    expectedCode = code\n    assert(typeof state === 'function', 'expected state')\n    state = state(code)\n  }\n\n  /** @type {Effects['consume']} */\n  function consume(code) {\n    assert(code === expectedCode, 'expected given code to equal expected code')\n\n    debug('consume: `%s`', code)\n\n    assert(\n      consumed === undefined,\n      'expected code to not have been consumed: this might be because `return x(code)` instead of `return x` was used'\n    )\n    assert(\n      code === null\n        ? context.events.length === 0 ||\n            context.events[context.events.length - 1][0] === 'exit'\n        : context.events[context.events.length - 1][0] === 'enter',\n      'expected last token to be open'\n    )\n\n    if (markdownLineEnding(code)) {\n      point.line++\n      point.column = 1\n      point.offset += code === codes.carriageReturnLineFeed ? 2 : 1\n      accountForPotentialSkip()\n      debug('position: after eol: `%j`', point)\n    } else if (code !== codes.virtualSpace) {\n      point.column++\n      point.offset++\n    }\n\n    // Not in a string chunk.\n    if (point._bufferIndex < 0) {\n      point._index++\n    } else {\n      point._bufferIndex++\n\n      // At end of string chunk.\n      if (\n        point._bufferIndex ===\n        // Points w/ non-negative `_bufferIndex` reference\n        // strings.\n        /** @type {string} */ (chunks[point._index]).length\n      ) {\n        point._bufferIndex = -1\n        point._index++\n      }\n    }\n\n    // Expose the previous character.\n    context.previous = code\n\n    // Mark as consumed.\n    consumed = true\n  }\n\n  /** @type {Effects['enter']} */\n  function enter(type, fields) {\n    /** @type {Token} */\n    // @ts-expect-error Patch instead of assign required fields to help GC.\n    const token = fields || {}\n    token.type = type\n    token.start = now()\n\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n    debug('enter: `%s`', type)\n\n    context.events.push(['enter', token, context])\n\n    stack.push(token)\n\n    return token\n  }\n\n  /** @type {Effects['exit']} */\n  function exit(type) {\n    assert(typeof type === 'string', 'expected string type')\n    assert(type.length > 0, 'expected non-empty string')\n\n    const token = stack.pop()\n    assert(token, 'cannot close w/o open tokens')\n    token.end = now()\n\n    assert(type === token.type, 'expected exit token to match current token')\n\n    assert(\n      !(\n        token.start._index === token.end._index &&\n        token.start._bufferIndex === token.end._bufferIndex\n      ),\n      'expected non-empty token (`' + type + '`)'\n    )\n\n    debug('exit: `%s`', token.type)\n    context.events.push(['exit', token, context])\n\n    return token\n  }\n\n  /**\n   * Use results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulconstruct(construct, info) {\n    addResult(construct, info.from)\n  }\n\n  /**\n   * Discard results.\n   *\n   * @type {ReturnHandle}\n   */\n  function onsuccessfulcheck(_, info) {\n    info.restore()\n  }\n\n  /**\n   * Factory to attempt/check/interrupt.\n   *\n   * @param {ReturnHandle} onreturn\n   *   Callback.\n   * @param {{interrupt?: boolean | undefined} | undefined} [fields]\n   *   Fields.\n   */\n  function constructFactory(onreturn, fields) {\n    return hook\n\n    /**\n     * Handle either an object mapping codes to constructs, a list of\n     * constructs, or a single construct.\n     *\n     * @param {Array<Construct> | ConstructRecord | Construct} constructs\n     *   Constructs.\n     * @param {State} returnState\n     *   State.\n     * @param {State | undefined} [bogusState]\n     *   State.\n     * @returns {State}\n     *   State.\n     */\n    function hook(constructs, returnState, bogusState) {\n      /** @type {ReadonlyArray<Construct>} */\n      let listOfConstructs\n      /** @type {number} */\n      let constructIndex\n      /** @type {Construct} */\n      let currentConstruct\n      /** @type {Info} */\n      let info\n\n      return Array.isArray(constructs)\n        ? /* c8 ignore next 1 */\n          handleListOfConstructs(constructs)\n        : 'tokenize' in constructs\n          ? // Looks like a construct.\n            handleListOfConstructs([/** @type {Construct} */ (constructs)])\n          : handleMapOfConstructs(constructs)\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ConstructRecord} map\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleMapOfConstructs(map) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          const left = code !== null && map[code]\n          const all = code !== null && map.null\n          const list = [\n            // To do: add more extension tests.\n            /* c8 ignore next 2 */\n            ...(Array.isArray(left) ? left : left ? [left] : []),\n            ...(Array.isArray(all) ? all : all ? [all] : [])\n          ]\n\n          return handleListOfConstructs(list)(code)\n        }\n      }\n\n      /**\n       * Handle a list of construct.\n       *\n       * @param {ReadonlyArray<Construct>} list\n       *   Constructs.\n       * @returns {State}\n       *   State.\n       */\n      function handleListOfConstructs(list) {\n        listOfConstructs = list\n        constructIndex = 0\n\n        if (list.length === 0) {\n          assert(bogusState, 'expected `bogusState` to be given')\n          return bogusState\n        }\n\n        return handleConstruct(list[constructIndex])\n      }\n\n      /**\n       * Handle a single construct.\n       *\n       * @param {Construct} construct\n       *   Construct.\n       * @returns {State}\n       *   State.\n       */\n      function handleConstruct(construct) {\n        return start\n\n        /** @type {State} */\n        function start(code) {\n          // To do: not needed to store if there is no bogus state, probably?\n          // Currently doesn’t work because `inspect` in document does a check\n          // w/o a bogus, which doesn’t make sense. But it does seem to help perf\n          // by not storing.\n          info = store()\n          currentConstruct = construct\n\n          if (!construct.partial) {\n            context.currentConstruct = construct\n          }\n\n          // Always populated by defaults.\n          assert(\n            context.parser.constructs.disable.null,\n            'expected `disable.null` to be populated'\n          )\n\n          if (\n            construct.name &&\n            context.parser.constructs.disable.null.includes(construct.name)\n          ) {\n            return nok(code)\n          }\n\n          return construct.tokenize.call(\n            // If we do have fields, create an object w/ `context` as its\n            // prototype.\n            // This allows a “live binding”, which is needed for `interrupt`.\n            fields ? Object.assign(Object.create(context), fields) : context,\n            effects,\n            ok,\n            nok\n          )(code)\n        }\n      }\n\n      /** @type {State} */\n      function ok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        onreturn(currentConstruct, info)\n        return returnState\n      }\n\n      /** @type {State} */\n      function nok(code) {\n        assert(code === expectedCode, 'expected code')\n        consumed = true\n        info.restore()\n\n        if (++constructIndex < listOfConstructs.length) {\n          return handleConstruct(listOfConstructs[constructIndex])\n        }\n\n        return bogusState\n      }\n    }\n  }\n\n  /**\n   * @param {Construct} construct\n   *   Construct.\n   * @param {number} from\n   *   From.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addResult(construct, from) {\n    if (construct.resolveAll && !resolveAllConstructs.includes(construct)) {\n      resolveAllConstructs.push(construct)\n    }\n\n    if (construct.resolve) {\n      splice(\n        context.events,\n        from,\n        context.events.length - from,\n        construct.resolve(context.events.slice(from), context)\n      )\n    }\n\n    if (construct.resolveTo) {\n      context.events = construct.resolveTo(context.events, context)\n    }\n\n    assert(\n      construct.partial ||\n        context.events.length === 0 ||\n        context.events[context.events.length - 1][0] === 'exit',\n      'expected last token to end'\n    )\n  }\n\n  /**\n   * Store state.\n   *\n   * @returns {Info}\n   *   Info.\n   */\n  function store() {\n    const startPoint = now()\n    const startPrevious = context.previous\n    const startCurrentConstruct = context.currentConstruct\n    const startEventsIndex = context.events.length\n    const startStack = Array.from(stack)\n\n    return {from: startEventsIndex, restore}\n\n    /**\n     * Restore state.\n     *\n     * @returns {undefined}\n     *   Nothing.\n     */\n    function restore() {\n      point = startPoint\n      context.previous = startPrevious\n      context.currentConstruct = startCurrentConstruct\n      context.events.length = startEventsIndex\n      stack = startStack\n      accountForPotentialSkip()\n      debug('position: restore: `%j`', point)\n    }\n  }\n\n  /**\n   * Move the current point a bit forward in the line when it’s on a column\n   * skip.\n   *\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function accountForPotentialSkip() {\n    if (point.line in columnStart && point.column < 2) {\n      point.column = columnStart[point.line]\n      point.offset += columnStart[point.line] - 1\n    }\n  }\n}\n\n/**\n * Get the chunks from a slice of chunks in the range of a token.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {Pick<Token, 'end' | 'start'>} token\n *   Token.\n * @returns {Array<Chunk>}\n *   Chunks.\n */\nfunction sliceChunks(chunks, token) {\n  const startIndex = token.start._index\n  const startBufferIndex = token.start._bufferIndex\n  const endIndex = token.end._index\n  const endBufferIndex = token.end._bufferIndex\n  /** @type {Array<Chunk>} */\n  let view\n\n  if (startIndex === endIndex) {\n    assert(endBufferIndex > -1, 'expected non-negative end buffer index')\n    assert(startBufferIndex > -1, 'expected non-negative start buffer index')\n    // @ts-expect-error `_bufferIndex` is used on string chunks.\n    view = [chunks[startIndex].slice(startBufferIndex, endBufferIndex)]\n  } else {\n    view = chunks.slice(startIndex, endIndex)\n\n    if (startBufferIndex > -1) {\n      const head = view[0]\n      if (typeof head === 'string') {\n        view[0] = head.slice(startBufferIndex)\n        /* c8 ignore next 4 -- used to be used, no longer */\n      } else {\n        assert(startBufferIndex === 0, 'expected `startBufferIndex` to be `0`')\n        view.shift()\n      }\n    }\n\n    if (endBufferIndex > 0) {\n      // @ts-expect-error `_bufferIndex` is used on string chunks.\n      view.push(chunks[endIndex].slice(0, endBufferIndex))\n    }\n  }\n\n  return view\n}\n\n/**\n * Get the string value of a slice of chunks.\n *\n * @param {ReadonlyArray<Chunk>} chunks\n *   Chunks.\n * @param {boolean | undefined} [expandTabs=false]\n *   Whether to expand tabs (default: `false`).\n * @returns {string}\n *   Result.\n */\nfunction serializeChunks(chunks, expandTabs) {\n  let index = -1\n  /** @type {Array<string>} */\n  const result = []\n  /** @type {boolean | undefined} */\n  let atTab\n\n  while (++index < chunks.length) {\n    const chunk = chunks[index]\n    /** @type {string} */\n    let value\n\n    if (typeof chunk === 'string') {\n      value = chunk\n    } else\n      switch (chunk) {\n        case codes.carriageReturn: {\n          value = values.cr\n\n          break\n        }\n\n        case codes.lineFeed: {\n          value = values.lf\n\n          break\n        }\n\n        case codes.carriageReturnLineFeed: {\n          value = values.cr + values.lf\n\n          break\n        }\n\n        case codes.horizontalTab: {\n          value = expandTabs ? values.space : values.ht\n\n          break\n        }\n\n        case codes.virtualSpace: {\n          if (!expandTabs && atTab) continue\n          value = values.space\n\n          break\n        }\n\n        default: {\n          assert(typeof chunk === 'number', 'expected number')\n          // Currently only replacement character.\n          value = String.fromCharCode(chunk)\n        }\n      }\n\n    atTab = chunk === codes.horizontalTab\n    result.push(value)\n  }\n\n  return result.join('')\n}\n", "/**\n * @import {\n *   Create,\n *   FullNormalizedExtension,\n *   InitialConstruct,\n *   ParseContext,\n *   ParseOptions\n * } from 'micromark-util-types'\n */\n\nimport {combineExtensions} from 'micromark-util-combine-extensions'\nimport {content} from './initialize/content.js'\nimport {document} from './initialize/document.js'\nimport {flow} from './initialize/flow.js'\nimport {string, text} from './initialize/text.js'\nimport * as defaultConstructs from './constructs.js'\nimport {createTokenizer} from './create-tokenizer.js'\n\n/**\n * @param {ParseOptions | null | undefined} [options]\n *   Configuration (optional).\n * @returns {ParseContext}\n *   Parser.\n */\nexport function parse(options) {\n  const settings = options || {}\n  const constructs = /** @type {FullNormalizedExtension} */ (\n    combineExtensions([defaultConstructs, ...(settings.extensions || [])])\n  )\n\n  /** @type {ParseContext} */\n  const parser = {\n    constructs,\n    content: create(content),\n    defined: [],\n    document: create(document),\n    flow: create(flow),\n    lazy: {},\n    string: create(string),\n    text: create(text)\n  }\n\n  return parser\n\n  /**\n   * @param {InitialConstruct} initial\n   *   Construct to start with.\n   * @returns {Create}\n   *   Create a tokenizer.\n   */\n  function create(initial) {\n    return creator\n    /** @type {Create} */\n    function creator(from) {\n      return createTokenizer(parser, initial, from)\n    }\n  }\n}\n", "/**\n * @import {Event} from 'micromark-util-types'\n */\n\nimport {subtokenize} from 'micromark-util-subtokenize'\n\n/**\n * @param {Array<Event>} events\n *   Events.\n * @returns {Array<Event>}\n *   Events.\n */\nexport function postprocess(events) {\n  while (!subtokenize(events)) {\n    // Empty\n  }\n\n  return events\n}\n", "/**\n * @import {Chunk, Code, Encoding, Value} from 'micromark-util-types'\n */\n\n/**\n * @callback Preprocessor\n *   Preprocess a value.\n * @param {Value} value\n *   Value.\n * @param {Encoding | null | undefined} [encoding]\n *   Encoding when `value` is a typed array (optional).\n * @param {boolean | null | undefined} [end=false]\n *   Whether this is the last chunk (default: `false`).\n * @returns {Array<Chunk>}\n *   Chunks.\n */\n\nimport {codes, constants} from 'micromark-util-symbol'\n\nconst search = /[\\0\\t\\n\\r]/g\n\n/**\n * @returns {Preprocessor}\n *   Preprocess a value.\n */\nexport function preprocess() {\n  let column = 1\n  let buffer = ''\n  /** @type {boolean | undefined} */\n  let start = true\n  /** @type {boolean | undefined} */\n  let atCarriageReturn\n\n  return preprocessor\n\n  /** @type {Preprocessor} */\n  // eslint-disable-next-line complexity\n  function preprocessor(value, encoding, end) {\n    /** @type {Array<Chunk>} */\n    const chunks = []\n    /** @type {RegExpMatchArray | null} */\n    let match\n    /** @type {number} */\n    let next\n    /** @type {number} */\n    let startPosition\n    /** @type {number} */\n    let endPosition\n    /** @type {Code} */\n    let code\n\n    value =\n      buffer +\n      (typeof value === 'string'\n        ? value.toString()\n        : new TextDecoder(encoding || undefined).decode(value))\n\n    startPosition = 0\n    buffer = ''\n\n    if (start) {\n      // To do: `markdown-rs` actually parses BOMs (byte order mark).\n      if (value.charCodeAt(0) === codes.byteOrderMarker) {\n        startPosition++\n      }\n\n      start = undefined\n    }\n\n    while (startPosition < value.length) {\n      search.lastIndex = startPosition\n      match = search.exec(value)\n      endPosition =\n        match && match.index !== undefined ? match.index : value.length\n      code = value.charCodeAt(endPosition)\n\n      if (!match) {\n        buffer = value.slice(startPosition)\n        break\n      }\n\n      if (\n        code === codes.lf &&\n        startPosition === endPosition &&\n        atCarriageReturn\n      ) {\n        chunks.push(codes.carriageReturnLineFeed)\n        atCarriageReturn = undefined\n      } else {\n        if (atCarriageReturn) {\n          chunks.push(codes.carriageReturn)\n          atCarriageReturn = undefined\n        }\n\n        if (startPosition < endPosition) {\n          chunks.push(value.slice(startPosition, endPosition))\n          column += endPosition - startPosition\n        }\n\n        switch (code) {\n          case codes.nul: {\n            chunks.push(codes.replacementCharacter)\n            column++\n\n            break\n          }\n\n          case codes.ht: {\n            next = Math.ceil(column / constants.tabSize) * constants.tabSize\n            chunks.push(codes.horizontalTab)\n            while (column++ < next) chunks.push(codes.virtualSpace)\n\n            break\n          }\n\n          case codes.lf: {\n            chunks.push(codes.lineFeed)\n            column = 1\n\n            break\n          }\n\n          default: {\n            atCarriageReturn = true\n            column = 1\n          }\n        }\n      }\n\n      startPosition = endPosition + 1\n    }\n\n    if (end) {\n      if (atCarriageReturn) chunks.push(codes.carriageReturn)\n      if (buffer) chunks.push(buffer)\n      chunks.push(codes.eof)\n    }\n\n    return chunks\n  }\n}\n", "/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\nimport {ok as assert} from 'devlop'\nimport {toString} from 'mdast-util-to-string'\nimport {parse, postprocess, preprocess} from 'micromark'\nimport {decodeNumericCharacterReference} from 'micromark-util-decode-numeric-character-reference'\nimport {decodeString} from 'micromark-util-decode-string'\nimport {normalizeIdentifier} from 'micromark-util-normalize-identifier'\nimport {codes, constants, types} from 'micromark-util-symbol'\nimport {decodeNamedCharacterReference} from 'decode-named-character-reference'\nimport {stringifyPosition} from 'unist-util-stringify-position'\n\nconst own = {}.hasOwnProperty\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nexport function fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    postprocess(\n      parse(options)\n        .document()\n        .write(preprocess()(value, encoding, true))\n    )\n  )\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === types.listOrdered ||\n        events[index][1].type === types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          assert(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      switch (event[1].type) {\n        case types.listUnordered:\n        case types.listOrdered:\n        case types.blockQuote: {\n          if (event[0] === 'enter') {\n            containerBalance++\n          } else {\n            containerBalance--\n          }\n\n          atMarker = undefined\n\n          break\n        }\n\n        case types.lineEndingBlank: {\n          if (event[0] === 'enter') {\n            if (\n              listItem &&\n              !atMarker &&\n              !containerBalance &&\n              !firstBlankLineIndex\n            ) {\n              firstBlankLineIndex = index\n            }\n\n            atMarker = undefined\n          }\n\n          break\n        }\n\n        case types.linePrefix:\n        case types.listItemValue:\n        case types.listItemMarker:\n        case types.listItemPrefix:\n        case types.listItemPrefixWhitespace: {\n          // Empty.\n\n          break\n        }\n\n        default: {\n          atMarker = undefined\n        }\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === types.listUnordered ||\n            event[1].type === types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === types.lineEnding ||\n              tailEvent[1].type === types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === types.linePrefix ||\n              tailEvent[1].type === types.blockQuotePrefix ||\n              tailEvent[1].type === types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === types.blockQuoteMarker ||\n              tailEvent[1].type === types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          listItem = item\n          events.splice(index, 0, ['enter', item, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    assert(parent, 'expected `parent`')\n    assert('children' in parent, 'expected `parent`')\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    siblings.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler || undefined])\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    }\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    assert(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          stringifyPosition({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    assert(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    assert(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return toString(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2]\n      assert(ancestor, 'expected nodes on stack')\n      assert(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        constants.numericBaseDecimal\n      )\n      this.data.expectingFirstListItemValue = undefined\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return\n    this.buffer()\n    this.data.flowCodeInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    this.data.flowCodeInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      assert(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).codePointAt(0) === codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert('children' in node, 'expected parent on stack')\n    /** @type {Array<Nodes>} */\n    const siblings = node.children\n\n    let tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      }\n      siblings.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected a `node` to be on the stack')\n    assert('value' in tail, 'expected a `literal` to be on the stack')\n    assert(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    assert(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      assert('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      assert(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      this.data.atHardBreak = undefined\n      return\n    }\n\n    if (\n      !this.data.setextHeadingSlurpLineEnding &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    assert(ancestor, 'expected ancestor on stack')\n    assert(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = decodeString(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = normalizeIdentifier(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    assert(fragment, 'expected node on stack')\n    assert(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    this.data.inReference = true\n\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = normalizeIdentifier(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    this.data.referenceType = 'full'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    assert(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    this.data.characterReferenceType = token.type\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = this.data.characterReferenceType\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = decodeNumericCharacterReference(\n        data,\n        type === types.characterReferenceMarkerNumeric\n          ? constants.numericBaseDecimal\n          : constants.numericBaseHexadecimal\n      )\n      this.data.characterReferenceType = undefined\n    } else {\n      const result = decodeNamedCharacterReference(data)\n      assert(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack[this.stack.length - 1]\n    assert(tail, 'expected `node`')\n    assert('value' in tail, 'expected `node.value`')\n    tail.value += value\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop()\n    assert(tail, 'expected `node`')\n    assert(tail.position, 'expected `node.position`')\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    assert(node, 'expected node on stack')\n    assert(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    }\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'transforms': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'enter':\n        case 'exit': {\n          const right = extension[key]\n          if (right) {\n            Object.assign(combined[key], right)\n          }\n\n          break\n        }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        stringifyPosition({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        stringifyPosition({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n", "/**\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast-util-from-markdown').Options} FromMarkdownOptions\n * @typedef {import('unified').Parser<Root>} Parser\n * @typedef {import('unified').Processor<Root>} Processor\n */\n\n/**\n * @typedef {Omit<FromMarkdownOptions, 'extensions' | 'mdastExtensions'>} Options\n */\n\nimport {fromMarkdown} from 'mdast-util-from-markdown'\n\n/**\n * Aadd support for parsing from markdown.\n *\n * @param {Readonly<Options> | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nexport default function remarkParse(options) {\n  /** @type {Processor} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n\n  self.parser = parser\n\n  /**\n   * @type {Parser}\n   */\n  function parser(doc) {\n    return fromMarkdown(doc, {\n      ...self.data('settings'),\n      ...options,\n      // Note: these options are not in the readme.\n      // The goal is for them to be set by plugins on `data` instead of being\n      // passed by users.\n      extensions: self.data('micromarkExtensions') || [],\n      mdastExtensions: self.data('fromMarkdownExtensions') || []\n    })\n  }\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `blockquote` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Blockquote} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function blockquote(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'blockquote',\n    properties: {},\n    children: state.wrap(state.all(node), true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').Break} Break\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `break` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Break} node\n *   mdast node.\n * @returns {Array<Element | Text>}\n *   hast element content.\n */\nexport function hardBreak(state, node) {\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'br', properties: {}, children: []}\n  state.patch(node, result)\n  return [state.applyData(node, result), {type: 'text', value: '\\n'}]\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Code} Code\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `code` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Code} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function code(state, node) {\n  const value = node.value ? node.value + '\\n' : ''\n  /** @type {Properties} */\n  const properties = {}\n\n  if (node.lang) {\n    properties.className = ['language-' + node.lang]\n  }\n\n  // Create `<code>`.\n  /** @type {Element} */\n  let result = {\n    type: 'element',\n    tagName: 'code',\n    properties,\n    children: [{type: 'text', value}]\n  }\n\n  if (node.meta) {\n    result.data = {meta: node.meta}\n  }\n\n  state.patch(node, result)\n  result = state.applyData(node, result)\n\n  // Create `<pre>`.\n  result = {type: 'element', tagName: 'pre', properties: {}, children: [result]}\n  state.patch(node, result)\n  return result\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Delete} Delete\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `delete` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Delete} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strikethrough(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'del',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `emphasis` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Emphasis} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function emphasis(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'em',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `footnoteReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {FootnoteReference} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function footnoteReference(state, node) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const id = String(node.identifier).toUpperCase()\n  const safeId = normalizeUri(id.toLowerCase())\n  const index = state.footnoteOrder.indexOf(id)\n  /** @type {number} */\n  let counter\n\n  let reuseCounter = state.footnoteCounts.get(id)\n\n  if (reuseCounter === undefined) {\n    reuseCounter = 0\n    state.footnoteOrder.push(id)\n    counter = state.footnoteOrder.length\n  } else {\n    counter = index + 1\n  }\n\n  reuseCounter += 1\n  state.footnoteCounts.set(id, reuseCounter)\n\n  /** @type {Element} */\n  const link = {\n    type: 'element',\n    tagName: 'a',\n    properties: {\n      href: '#' + clobberPrefix + 'fn-' + safeId,\n      id:\n        clobberPrefix +\n        'fnref-' +\n        safeId +\n        (reuseCounter > 1 ? '-' + reuseCounter : ''),\n      dataFootnoteRef: true,\n      ariaDescribedBy: ['footnote-label']\n    },\n    children: [{type: 'text', value: String(counter)}]\n  }\n  state.patch(node, link)\n\n  /** @type {Element} */\n  const sup = {\n    type: 'element',\n    tagName: 'sup',\n    properties: {},\n    children: [link]\n  }\n  state.patch(node, sup)\n  return state.applyData(node, sup)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `heading` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Heading} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function heading(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'h' + node.depth,\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Html} Html\n * @typedef {import('../state.js').State} State\n * @typedef {import('../../index.js').Raw} Raw\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `html` node into hast (`raw` node in dangerous mode, otherwise\n * nothing).\n *\n * @param {State} state\n *   Info passed around.\n * @param {Html} node\n *   mdast node.\n * @returns {Element | Raw | undefined}\n *   hast node.\n */\nexport function html(state, node) {\n  if (state.options.allowDangerousHtml) {\n    /** @type {Raw} */\n    const result = {type: 'raw', value: node.value}\n    state.patch(node, result)\n    return state.applyData(node, result)\n  }\n\n  return undefined\n}\n", "/**\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('mdast').Nodes} Nodes\n * @typedef {import('mdast').Reference} Reference\n *\n * @typedef {import('./state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Return the content of a reference without definition as plain text.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Extract<Nodes, Reference>} node\n *   Reference node (image, link).\n * @returns {Array<ElementContent>}\n *   hast content.\n */\nexport function revert(state, node) {\n  const subtype = node.referenceType\n  let suffix = ']'\n\n  if (subtype === 'collapsed') {\n    suffix += '[]'\n  } else if (subtype === 'full') {\n    suffix += '[' + (node.label || node.identifier) + ']'\n  }\n\n  if (node.type === 'imageReference') {\n    return [{type: 'text', value: '![' + node.alt + suffix}]\n  }\n\n  const contents = state.all(node)\n  const head = contents[0]\n\n  if (head && head.type === 'text') {\n    head.value = '[' + head.value\n  } else {\n    contents.unshift({type: 'text', value: '['})\n  }\n\n  const tail = contents[contents.length - 1]\n\n  if (tail && tail.type === 'text') {\n    tail.value += suffix\n  } else {\n    contents.push({type: 'text', value: suffix})\n  }\n\n  return contents\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `imageReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ImageReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function imageReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(definition.url || ''), alt: node.alt}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Image} Image\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `image` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Image} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function image(state, node) {\n  /** @type {Properties} */\n  const properties = {src: normalizeUri(node.url)}\n\n  if (node.alt !== null && node.alt !== undefined) {\n    properties.alt = node.alt\n  }\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'img', properties, children: []}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Text} Text\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `inlineCode` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {InlineCode} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function inlineCode(state, node) {\n  /** @type {Text} */\n  const text = {type: 'text', value: node.value.replace(/\\r?\\n|\\r/g, ' ')}\n  state.patch(node, text)\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'code',\n    properties: {},\n    children: [text]\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\nimport {revert} from '../revert.js'\n\n/**\n * Turn an mdast `linkReference` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {LinkReference} node\n *   mdast node.\n * @returns {Array<ElementContent> | ElementContent}\n *   hast node.\n */\nexport function linkReference(state, node) {\n  const id = String(node.identifier).toUpperCase()\n  const definition = state.definitionById.get(id)\n\n  if (!definition) {\n    return revert(state, node)\n  }\n\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(definition.url || '')}\n\n  if (definition.title !== null && definition.title !== undefined) {\n    properties.title = definition.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Link} Link\n * @typedef {import('../state.js').State} State\n */\n\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Turn an mdast `link` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Link} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function link(state, node) {\n  /** @type {Properties} */\n  const properties = {href: normalizeUri(node.url)}\n\n  if (node.title !== null && node.title !== undefined) {\n    properties.title = node.title\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'a',\n    properties,\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `listItem` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ListItem} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function listItem(state, node, parent) {\n  const results = state.all(node)\n  const loose = parent ? listLoose(parent) : listItemLoose(node)\n  /** @type {Properties} */\n  const properties = {}\n  /** @type {Array<ElementContent>} */\n  const children = []\n\n  if (typeof node.checked === 'boolean') {\n    const head = results[0]\n    /** @type {Element} */\n    let paragraph\n\n    if (head && head.type === 'element' && head.tagName === 'p') {\n      paragraph = head\n    } else {\n      paragraph = {type: 'element', tagName: 'p', properties: {}, children: []}\n      results.unshift(paragraph)\n    }\n\n    if (paragraph.children.length > 0) {\n      paragraph.children.unshift({type: 'text', value: ' '})\n    }\n\n    paragraph.children.unshift({\n      type: 'element',\n      tagName: 'input',\n      properties: {type: 'checkbox', checked: node.checked, disabled: true},\n      children: []\n    })\n\n    // According to github-markdown-css, this class hides bullet.\n    // See: <https://github.com/sindresorhus/github-markdown-css>.\n    properties.className = ['task-list-item']\n  }\n\n  let index = -1\n\n  while (++index < results.length) {\n    const child = results[index]\n\n    // Add eols before nodes, except if this is a loose, first paragraph.\n    if (\n      loose ||\n      index !== 0 ||\n      child.type !== 'element' ||\n      child.tagName !== 'p'\n    ) {\n      children.push({type: 'text', value: '\\n'})\n    }\n\n    if (child.type === 'element' && child.tagName === 'p' && !loose) {\n      children.push(...child.children)\n    } else {\n      children.push(child)\n    }\n  }\n\n  const tail = results[results.length - 1]\n\n  // Add a final eol.\n  if (tail && (loose || tail.type !== 'element' || tail.tagName !== 'p')) {\n    children.push({type: 'text', value: '\\n'})\n  }\n\n  /** @type {Element} */\n  const result = {type: 'element', tagName: 'li', properties, children}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * @param {Parents} node\n * @return {Boolean}\n */\nfunction listLoose(node) {\n  let loose = false\n  if (node.type === 'list') {\n    loose = node.spread || false\n    const children = node.children\n    let index = -1\n\n    while (!loose && ++index < children.length) {\n      loose = listItemLoose(children[index])\n    }\n  }\n\n  return loose\n}\n\n/**\n * @param {ListItem} node\n * @return {Boolean}\n */\nfunction listItemLoose(node) {\n  const spread = node.spread\n\n  return spread === null || spread === undefined\n    ? node.children.length > 1\n    : spread\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').List} List\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `list` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {List} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function list(state, node) {\n  /** @type {Properties} */\n  const properties = {}\n  const results = state.all(node)\n  let index = -1\n\n  if (typeof node.start === 'number' && node.start !== 1) {\n    properties.start = node.start\n  }\n\n  // Like GitHub, add a class for custom styling.\n  while (++index < results.length) {\n    const child = results[index]\n\n    if (\n      child.type === 'element' &&\n      child.tagName === 'li' &&\n      child.properties &&\n      Array.isArray(child.properties.className) &&\n      child.properties.className.includes('task-list-item')\n    ) {\n      properties.className = ['contains-task-list']\n      break\n    }\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: node.ordered ? 'ol' : 'ul',\n    properties,\n    children: state.wrap(results, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `paragraph` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Paragraph} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function paragraph(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'p',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Parents} HastParents\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('mdast').Root} MdastRoot\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `root` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastRoot} node\n *   mdast node.\n * @returns {HastParents}\n *   hast node.\n */\nexport function root(state, node) {\n  /** @type {HastRoot} */\n  const result = {type: 'root', children: state.wrap(state.all(node))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `strong` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Strong} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function strong(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'strong',\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').Table} Table\n * @typedef {import('../state.js').State} State\n */\n\nimport {pointEnd, pointStart} from 'unist-util-position'\n\n/**\n * Turn an mdast `table` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {Table} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function table(state, node) {\n  const rows = state.all(node)\n  const firstRow = rows.shift()\n  /** @type {Array<Element>} */\n  const tableContent = []\n\n  if (firstRow) {\n    /** @type {Element} */\n    const head = {\n      type: 'element',\n      tagName: 'thead',\n      properties: {},\n      children: state.wrap([firstRow], true)\n    }\n    state.patch(node.children[0], head)\n    tableContent.push(head)\n  }\n\n  if (rows.length > 0) {\n    /** @type {Element} */\n    const body = {\n      type: 'element',\n      tagName: 'tbody',\n      properties: {},\n      children: state.wrap(rows, true)\n    }\n\n    const start = pointStart(node.children[1])\n    const end = pointEnd(node.children[node.children.length - 1])\n    if (start && end) body.position = {start, end}\n    tableContent.push(body)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'table',\n    properties: {},\n    children: state.wrap(tableContent, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('mdast').Parents} Parents\n * @typedef {import('mdast').TableRow} TableRow\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableRow` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableRow} node\n *   mdast node.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Element}\n *   hast node.\n */\nexport function tableRow(state, node, parent) {\n  const siblings = parent ? parent.children : undefined\n  // Generate a body row when without parent.\n  const rowIndex = siblings ? siblings.indexOf(node) : 1\n  const tagName = rowIndex === 0 ? 'th' : 'td'\n  // To do: option to use `style`?\n  const align = parent && parent.type === 'table' ? parent.align : undefined\n  const length = align ? align.length : node.children.length\n  let cellIndex = -1\n  /** @type {Array<ElementContent>} */\n  const cells = []\n\n  while (++cellIndex < length) {\n    // Note: can also be undefined.\n    const cell = node.children[cellIndex]\n    /** @type {Properties} */\n    const properties = {}\n    const alignValue = align ? align[cellIndex] : undefined\n\n    if (alignValue) {\n      properties.align = alignValue\n    }\n\n    /** @type {Element} */\n    let result = {type: 'element', tagName, properties, children: []}\n\n    if (cell) {\n      result.children = state.all(cell)\n      state.patch(cell, result)\n      result = state.applyData(cell, result)\n    }\n\n    cells.push(result)\n  }\n\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'tr',\n    properties: {},\n    children: state.wrap(cells, true)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').TableCell} TableCell\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `tableCell` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {TableCell} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function tableCell(state, node) {\n  // Note: this function is normally not called: see `table-row` for how rows\n  // and their cells are compiled.\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'td', // Assume body cell.\n    properties: {},\n    children: state.all(node)\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "const tab = 9 /* `\\t` */\nconst space = 32 /* ` ` */\n\n/**\n * Remove initial and final spaces and tabs at the line breaks in `value`.\n * Does not trim initial and final spaces and tabs of the value itself.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Trimmed value.\n */\nexport function trimLines(value) {\n  const source = String(value)\n  const search = /\\r?\\n|\\r/g\n  let match = search.exec(source)\n  let last = 0\n  /** @type {Array<string>} */\n  const lines = []\n\n  while (match) {\n    lines.push(\n      trimLine(source.slice(last, match.index), last > 0, true),\n      match[0]\n    )\n\n    last = match.index + match[0].length\n    match = search.exec(source)\n  }\n\n  lines.push(trimLine(source.slice(last), last > 0, false))\n\n  return lines.join('')\n}\n\n/**\n * @param {string} value\n *   Line to trim.\n * @param {boolean} start\n *   Whether to trim the start of the line.\n * @param {boolean} end\n *   Whether to trim the end of the line.\n * @returns {string}\n *   Trimmed line.\n */\nfunction trimLine(value, start, end) {\n  let startIndex = 0\n  let endIndex = value.length\n\n  if (start) {\n    let code = value.codePointAt(startIndex)\n\n    while (code === tab || code === space) {\n      startIndex++\n      code = value.codePointAt(startIndex)\n    }\n  }\n\n  if (end) {\n    let code = value.codePointAt(endIndex - 1)\n\n    while (code === tab || code === space) {\n      endIndex--\n      code = value.codePointAt(endIndex - 1)\n    }\n  }\n\n  return endIndex > startIndex ? value.slice(startIndex, endIndex) : ''\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('mdast').Text} MdastText\n * @typedef {import('../state.js').State} State\n */\n\nimport {trimLines} from 'trim-lines'\n\n/**\n * Turn an mdast `text` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastText} node\n *   mdast node.\n * @returns {HastElement | HastText}\n *   hast node.\n */\nexport function text(state, node) {\n  /** @type {HastText} */\n  const result = {type: 'text', value: trimLines(String(node.value))}\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('../state.js').State} State\n */\n\n// Make VS Code show references to the above types.\n''\n\n/**\n * Turn an mdast `thematicBreak` node into hast.\n *\n * @param {State} state\n *   Info passed around.\n * @param {ThematicBreak} node\n *   mdast node.\n * @returns {Element}\n *   hast node.\n */\nexport function thematicBreak(state, node) {\n  /** @type {Element} */\n  const result = {\n    type: 'element',\n    tagName: 'hr',\n    properties: {},\n    children: []\n  }\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n", "import {blockquote} from './blockquote.js'\nimport {hardBreak} from './break.js'\nimport {code} from './code.js'\nimport {strikethrough} from './delete.js'\nimport {emphasis} from './emphasis.js'\nimport {footnoteReference} from './footnote-reference.js'\nimport {heading} from './heading.js'\nimport {html} from './html.js'\nimport {imageReference} from './image-reference.js'\nimport {image} from './image.js'\nimport {inlineCode} from './inline-code.js'\nimport {linkReference} from './link-reference.js'\nimport {link} from './link.js'\nimport {listItem} from './list-item.js'\nimport {list} from './list.js'\nimport {paragraph} from './paragraph.js'\nimport {root} from './root.js'\nimport {strong} from './strong.js'\nimport {table} from './table.js'\nimport {tableRow} from './table-row.js'\nimport {tableCell} from './table-cell.js'\nimport {text} from './text.js'\nimport {thematicBreak} from './thematic-break.js'\n\n/**\n * Default handlers for nodes.\n *\n * @satisfies {import('../state.js').Handlers}\n */\nexport const handlers = {\n  blockquote,\n  break: hardBreak,\n  code,\n  delete: strikethrough,\n  emphasis,\n  footnoteReference,\n  heading,\n  html,\n  imageReference,\n  image,\n  inlineCode,\n  linkReference,\n  link,\n  listItem,\n  list,\n  paragraph,\n  // @ts-expect-error: root is different, but hard to type.\n  root,\n  strong,\n  table,\n  tableCell,\n  tableRow,\n  text,\n  thematicBreak,\n  toml: ignore,\n  yaml: ignore,\n  definition: ignore,\n  footnoteDefinition: ignore\n}\n\n// Return nothing for nodes that are ignored.\nfunction ignore() {\n  return undefined\n}\n", "/**\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').ElementContent} ElementContent\n *\n * @typedef {import('./state.js').State} State\n */\n\n/**\n * @callback FootnoteBackContentTemplate\n *   Generate content for the backreference dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent> | ElementContent | string}\n *   Content for the backreference when linking back from definitions to their\n *   reference.\n *\n * @callback FootnoteBackLabelTemplate\n *   Generate a back label dynamically.\n *\n *   For the following markdown:\n *\n *   ```markdown\n *   Alpha[^micromark], bravo[^micromark], and charlie[^remark].\n *\n *   [^remark]: things about remark\n *   [^micromark]: things about micromark\n *   ```\n *\n *   This function will be called with:\n *\n *   *  `0` and `0` for the backreference from `things about micromark` to\n *      `alpha`, as it is the first used definition, and the first call to it\n *   *  `0` and `1` for the backreference from `things about micromark` to\n *      `bravo`, as it is the first used definition, and the second call to it\n *   *  `1` and `0` for the backreference from `things about remark` to\n *      `charlie`, as it is the second used definition\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Back label to use when linking back from definitions to their reference.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {normalizeUri} from 'micromark-util-sanitize-uri'\n\n/**\n * Generate the default content that GitHub uses on backreferences.\n *\n * @param {number} _\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {Array<ElementContent>}\n *   Content.\n */\nexport function defaultFootnoteBackContent(_, rereferenceIndex) {\n  /** @type {Array<ElementContent>} */\n  const result = [{type: 'text', value: '↩'}]\n\n  if (rereferenceIndex > 1) {\n    result.push({\n      type: 'element',\n      tagName: 'sup',\n      properties: {},\n      children: [{type: 'text', value: String(rereferenceIndex)}]\n    })\n  }\n\n  return result\n}\n\n/**\n * Generate the default label that GitHub uses on backreferences.\n *\n * @param {number} referenceIndex\n *   Index of the definition in the order that they are first referenced,\n *   0-indexed.\n * @param {number} rereferenceIndex\n *   Index of calls to the same definition, 0-indexed.\n * @returns {string}\n *   Label.\n */\nexport function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n  return (\n    'Back to reference ' +\n    (referenceIndex + 1) +\n    (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n  )\n}\n\n/**\n * Generate a hast footer for called footnote definitions.\n *\n * @param {State} state\n *   Info passed around.\n * @returns {Element | undefined}\n *   `section` element or `undefined`.\n */\n// eslint-disable-next-line complexity\nexport function footer(state) {\n  const clobberPrefix =\n    typeof state.options.clobberPrefix === 'string'\n      ? state.options.clobberPrefix\n      : 'user-content-'\n  const footnoteBackContent =\n    state.options.footnoteBackContent || defaultFootnoteBackContent\n  const footnoteBackLabel =\n    state.options.footnoteBackLabel || defaultFootnoteBackLabel\n  const footnoteLabel = state.options.footnoteLabel || 'Footnotes'\n  const footnoteLabelTagName = state.options.footnoteLabelTagName || 'h2'\n  const footnoteLabelProperties = state.options.footnoteLabelProperties || {\n    className: ['sr-only']\n  }\n  /** @type {Array<ElementContent>} */\n  const listItems = []\n  let referenceIndex = -1\n\n  while (++referenceIndex < state.footnoteOrder.length) {\n    const definition = state.footnoteById.get(\n      state.footnoteOrder[referenceIndex]\n    )\n\n    if (!definition) {\n      continue\n    }\n\n    const content = state.all(definition)\n    const id = String(definition.identifier).toUpperCase()\n    const safeId = normalizeUri(id.toLowerCase())\n    let rereferenceIndex = 0\n    /** @type {Array<ElementContent>} */\n    const backReferences = []\n    const counts = state.footnoteCounts.get(id)\n\n    // eslint-disable-next-line no-unmodified-loop-condition\n    while (counts !== undefined && ++rereferenceIndex <= counts) {\n      if (backReferences.length > 0) {\n        backReferences.push({type: 'text', value: ' '})\n      }\n\n      let children =\n        typeof footnoteBackContent === 'string'\n          ? footnoteBackContent\n          : footnoteBackContent(referenceIndex, rereferenceIndex)\n\n      if (typeof children === 'string') {\n        children = {type: 'text', value: children}\n      }\n\n      backReferences.push({\n        type: 'element',\n        tagName: 'a',\n        properties: {\n          href:\n            '#' +\n            clobberPrefix +\n            'fnref-' +\n            safeId +\n            (rereferenceIndex > 1 ? '-' + rereferenceIndex : ''),\n          dataFootnoteBackref: '',\n          ariaLabel:\n            typeof footnoteBackLabel === 'string'\n              ? footnoteBackLabel\n              : footnoteBackLabel(referenceIndex, rereferenceIndex),\n          className: ['data-footnote-backref']\n        },\n        children: Array.isArray(children) ? children : [children]\n      })\n    }\n\n    const tail = content[content.length - 1]\n\n    if (tail && tail.type === 'element' && tail.tagName === 'p') {\n      const tailTail = tail.children[tail.children.length - 1]\n      if (tailTail && tailTail.type === 'text') {\n        tailTail.value += ' '\n      } else {\n        tail.children.push({type: 'text', value: ' '})\n      }\n\n      tail.children.push(...backReferences)\n    } else {\n      content.push(...backReferences)\n    }\n\n    /** @type {Element} */\n    const listItem = {\n      type: 'element',\n      tagName: 'li',\n      properties: {id: clobberPrefix + 'fn-' + safeId},\n      children: state.wrap(content, true)\n    }\n\n    state.patch(definition, listItem)\n\n    listItems.push(listItem)\n  }\n\n  if (listItems.length === 0) {\n    return\n  }\n\n  return {\n    type: 'element',\n    tagName: 'section',\n    properties: {dataFootnotes: true, className: ['footnotes']},\n    children: [\n      {\n        type: 'element',\n        tagName: footnoteLabelTagName,\n        properties: {\n          ...structuredClone(footnoteLabelProperties),\n          id: 'footnote-label'\n        },\n        children: [{type: 'text', value: footnoteLabel}]\n      },\n      {type: 'text', value: '\\n'},\n      {\n        type: 'element',\n        tagName: 'ol',\n        properties: {},\n        children: state.wrap(listItems, true)\n      },\n      {type: 'text', value: '\\n'}\n    ]\n  }\n}\n", "/**\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').ElementContent} HastElementContent\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('hast').Properties} HastProperties\n * @typedef {import('hast').RootContent} HastRootContent\n * @typedef {import('hast').Text} HastText\n *\n * @typedef {import('mdast').Definition} MdastDefinition\n * @typedef {import('mdast').FootnoteDefinition} MdastFootnoteDefinition\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('mdast').Parents} MdastParents\n *\n * @typedef {import('vfile').VFile} VFile\n *\n * @typedef {import('./footer.js').FootnoteBackContentTemplate} FootnoteBackContentTemplate\n * @typedef {import('./footer.js').FootnoteBackLabelTemplate} FootnoteBackLabelTemplate\n */\n\n/**\n * @callback Handler\n *   Handle a node.\n * @param {State} state\n *   Info passed around.\n * @param {any} node\n *   mdast node to handle.\n * @param {MdastParents | undefined} parent\n *   Parent of `node`.\n * @returns {Array<HastElementContent> | HastElementContent | undefined}\n *   hast node.\n *\n * @typedef {Partial<Record<MdastNodes['type'], Handler>>} Handlers\n *   Handle nodes.\n *\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [allowDangerousHtml=false]\n *   Whether to persist raw HTML in markdown in the hast tree (default:\n *   `false`).\n * @property {string | null | undefined} [clobberPrefix='user-content-']\n *   Prefix to use before the `id` property on footnotes to prevent them from\n *   *clobbering* (default: `'user-content-'`).\n *\n *   Pass `''` for trusted markdown and when you are careful with\n *   polyfilling.\n *   You could pass a different prefix.\n *\n *   DOM clobbering is this:\n *\n *   ```html\n *   <p id=\"x\"></p>\n *   <script>alert(x) // `x` now refers to the `p#x` DOM element</script>\n *   ```\n *\n *   The above example shows that elements are made available by browsers, by\n *   their ID, on the `window` object.\n *   This is a security risk because you might be expecting some other variable\n *   at that place.\n *   It can also break polyfills.\n *   Using a prefix solves these problems.\n * @property {VFile | null | undefined} [file]\n *   Corresponding virtual file representing the input document (optional).\n * @property {FootnoteBackContentTemplate | string | null | undefined} [footnoteBackContent]\n *   Content of the backreference back to references (default: `defaultFootnoteBackContent`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackContent(_, rereferenceIndex) {\n *     const result = [{type: 'text', value: '↩'}]\n *\n *     if (rereferenceIndex > 1) {\n *       result.push({\n *         type: 'element',\n *         tagName: 'sup',\n *         properties: {},\n *         children: [{type: 'text', value: String(rereferenceIndex)}]\n *       })\n *     }\n *\n *     return result\n *   }\n *   ```\n *\n *   This content is used in the `a` element of each backreference (the `↩`\n *   links).\n * @property {FootnoteBackLabelTemplate | string | null | undefined} [footnoteBackLabel]\n *   Label to describe the backreference back to references (default:\n *   `defaultFootnoteBackLabel`).\n *\n *   The default value is:\n *\n *   ```js\n *   function defaultFootnoteBackLabel(referenceIndex, rereferenceIndex) {\n *    return (\n *      'Back to reference ' +\n *      (referenceIndex + 1) +\n *      (rereferenceIndex > 1 ? '-' + rereferenceIndex : '')\n *    )\n *   }\n *   ```\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is used in the `ariaLabel` property on each backreference\n *   (the `↩` links).\n *   It affects users of assistive technology.\n * @property {string | null | undefined} [footnoteLabel='Footnotes']\n *   Textual label to use for the footnotes section (default: `'Footnotes'`).\n *\n *   Change it when the markdown is not in English.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {HastProperties | null | undefined} [footnoteLabelProperties={className: ['sr-only']}]\n *   Properties to use on the footnote label (default: `{className:\n *   ['sr-only']}`).\n *\n *   Change it to show the label and add other properties.\n *\n *   This label is typically hidden visually (assuming an `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass an empty string.\n *   You can also add different properties.\n *\n *   > **Note**: `id: 'footnote-label'` is always added, because footnote\n *   > calls use it with `aria-describedby` to provide an accessible label.\n * @property {string | null | undefined} [footnoteLabelTagName='h2']\n *   HTML tag name to use for the footnote label element (default: `'h2'`).\n *\n *   Change it to match your document structure.\n *\n *   This label is typically hidden visually (assuming a `sr-only` CSS class\n *   is defined that does that) and so affects screen readers only.\n *   If you do have such a class, but want to show this section to everyone,\n *   pass different properties with the `footnoteLabelProperties` option.\n * @property {Handlers | null | undefined} [handlers]\n *   Extra handlers for nodes (optional).\n * @property {Array<MdastNodes['type']> | null | undefined} [passThrough]\n *   List of custom mdast node types to pass through (keep) in hast (note that\n *   the node itself is passed, but eventual children are transformed)\n *   (optional).\n * @property {Handler | null | undefined} [unknownHandler]\n *   Handler for all unknown nodes (optional).\n *\n * @typedef State\n *   Info passed around.\n * @property {(node: MdastNodes) => Array<HastElementContent>} all\n *   Transform the children of an mdast parent to hast.\n * @property {<Type extends HastNodes>(from: MdastNodes, to: Type) => HastElement | Type} applyData\n *   Honor the `data` of `from`, and generate an element instead of `node`.\n * @property {Map<string, MdastDefinition>} definitionById\n *   Definitions by their identifier.\n * @property {Map<string, MdastFootnoteDefinition>} footnoteById\n *   Footnote definitions by their identifier.\n * @property {Map<string, number>} footnoteCounts\n *   Counts for how often the same footnote was called.\n * @property {Array<string>} footnoteOrder\n *   Identifiers of order when footnote calls first appear in tree order.\n * @property {Handlers} handlers\n *   Applied handlers.\n * @property {(node: MdastNodes, parent: MdastParents | undefined) => Array<HastElementContent> | HastElementContent | undefined} one\n *   Transform an mdast node to hast.\n * @property {Options} options\n *   Configuration.\n * @property {(from: MdastNodes, node: HastNodes) => undefined} patch\n *   Copy a node’s positional info.\n * @property {<Type extends HastRootContent>(nodes: Array<Type>, loose?: boolean | undefined) => Array<HastText | Type>} wrap\n *   Wrap `nodes` with line endings between each node, adds initial/final line endings when `loose`.\n */\n\nimport structuredClone from '@ungap/structured-clone'\nimport {visit} from 'unist-util-visit'\nimport {position} from 'unist-util-position'\nimport {handlers as defaultHandlers} from './handlers/index.js'\n\nconst own = {}.hasOwnProperty\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Create `state` from an mdast tree.\n *\n * @param {MdastNodes} tree\n *   mdast node to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {State}\n *   `state` function.\n */\nexport function createState(tree, options) {\n  const settings = options || emptyOptions\n  /** @type {Map<string, MdastDefinition>} */\n  const definitionById = new Map()\n  /** @type {Map<string, MdastFootnoteDefinition>} */\n  const footnoteById = new Map()\n  /** @type {Map<string, number>} */\n  const footnoteCounts = new Map()\n  /** @type {Handlers} */\n  // @ts-expect-error: the root handler returns a root.\n  // Hard to type.\n  const handlers = {...defaultHandlers, ...settings.handlers}\n\n  /** @type {State} */\n  const state = {\n    all,\n    applyData,\n    definitionById,\n    footnoteById,\n    footnoteCounts,\n    footnoteOrder: [],\n    handlers,\n    one,\n    options: settings,\n    patch,\n    wrap\n  }\n\n  visit(tree, function (node) {\n    if (node.type === 'definition' || node.type === 'footnoteDefinition') {\n      const map = node.type === 'definition' ? definitionById : footnoteById\n      const id = String(node.identifier).toUpperCase()\n\n      // Mimick CM behavior of link definitions.\n      // See: <https://github.com/syntax-tree/mdast-util-definitions/blob/9032189/lib/index.js#L20-L21>.\n      if (!map.has(id)) {\n        // @ts-expect-error: node type matches map.\n        map.set(id, node)\n      }\n    }\n  })\n\n  return state\n\n  /**\n   * Transform an mdast node into a hast node.\n   *\n   * @param {MdastNodes} node\n   *   mdast node.\n   * @param {MdastParents | undefined} [parent]\n   *   Parent of `node`.\n   * @returns {Array<HastElementContent> | HastElementContent | undefined}\n   *   Resulting hast node.\n   */\n  function one(node, parent) {\n    const type = node.type\n    const handle = state.handlers[type]\n\n    if (own.call(state.handlers, type) && handle) {\n      return handle(state, node, parent)\n    }\n\n    if (state.options.passThrough && state.options.passThrough.includes(type)) {\n      if ('children' in node) {\n        const {children, ...shallow} = node\n        const result = structuredClone(shallow)\n        // @ts-expect-error: TS doesn’t understand…\n        result.children = state.all(node)\n        // @ts-expect-error: TS doesn’t understand…\n        return result\n      }\n\n      // @ts-expect-error: it’s custom.\n      return structuredClone(node)\n    }\n\n    const unknown = state.options.unknownHandler || defaultUnknownHandler\n\n    return unknown(state, node, parent)\n  }\n\n  /**\n   * Transform the children of an mdast node into hast nodes.\n   *\n   * @param {MdastNodes} parent\n   *   mdast node to compile\n   * @returns {Array<HastElementContent>}\n   *   Resulting hast nodes.\n   */\n  function all(parent) {\n    /** @type {Array<HastElementContent>} */\n    const values = []\n\n    if ('children' in parent) {\n      const nodes = parent.children\n      let index = -1\n      while (++index < nodes.length) {\n        const result = state.one(nodes[index], parent)\n\n        // To do: see if we van clean this? Can we merge texts?\n        if (result) {\n          if (index && nodes[index - 1].type === 'break') {\n            if (!Array.isArray(result) && result.type === 'text') {\n              result.value = trimMarkdownSpaceStart(result.value)\n            }\n\n            if (!Array.isArray(result) && result.type === 'element') {\n              const head = result.children[0]\n\n              if (head && head.type === 'text') {\n                head.value = trimMarkdownSpaceStart(head.value)\n              }\n            }\n          }\n\n          if (Array.isArray(result)) {\n            values.push(...result)\n          } else {\n            values.push(result)\n          }\n        }\n      }\n    }\n\n    return values\n  }\n}\n\n/**\n * Copy a node’s positional info.\n *\n * @param {MdastNodes} from\n *   mdast node to copy from.\n * @param {HastNodes} to\n *   hast node to copy into.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(from, to) {\n  if (from.position) to.position = position(from)\n}\n\n/**\n * Honor the `data` of `from` and maybe generate an element instead of `to`.\n *\n * @template {HastNodes} Type\n *   Node type.\n * @param {MdastNodes} from\n *   mdast node to use data from.\n * @param {Type} to\n *   hast node to change.\n * @returns {HastElement | Type}\n *   Nothing.\n */\nfunction applyData(from, to) {\n  /** @type {HastElement | Type} */\n  let result = to\n\n  // Handle `data.hName`, `data.hProperties, `data.hChildren`.\n  if (from && from.data) {\n    const hName = from.data.hName\n    const hChildren = from.data.hChildren\n    const hProperties = from.data.hProperties\n\n    if (typeof hName === 'string') {\n      // Transforming the node resulted in an element with a different name\n      // than wanted:\n      if (result.type === 'element') {\n        result.tagName = hName\n      }\n      // Transforming the node resulted in a non-element, which happens for\n      // raw, text, and root nodes (unless custom handlers are passed).\n      // The intent of `hName` is to create an element, but likely also to keep\n      // the content around (otherwise: pass `hChildren`).\n      else {\n        /** @type {Array<HastElementContent>} */\n        // @ts-expect-error: assume no doctypes in `root`.\n        const children = 'children' in result ? result.children : [result]\n        result = {type: 'element', tagName: hName, properties: {}, children}\n      }\n    }\n\n    if (result.type === 'element' && hProperties) {\n      Object.assign(result.properties, structuredClone(hProperties))\n    }\n\n    if (\n      'children' in result &&\n      result.children &&\n      hChildren !== null &&\n      hChildren !== undefined\n    ) {\n      result.children = hChildren\n    }\n  }\n\n  return result\n}\n\n/**\n * Transform an unknown node.\n *\n * @param {State} state\n *   Info passed around.\n * @param {MdastNodes} node\n *   Unknown mdast node.\n * @returns {HastElement | HastText}\n *   Resulting hast node.\n */\nfunction defaultUnknownHandler(state, node) {\n  const data = node.data || {}\n  /** @type {HastElement | HastText} */\n  const result =\n    'value' in node &&\n    !(own.call(data, 'hProperties') || own.call(data, 'hChildren'))\n      ? {type: 'text', value: node.value}\n      : {\n          type: 'element',\n          tagName: 'div',\n          properties: {},\n          children: state.all(node)\n        }\n\n  state.patch(node, result)\n  return state.applyData(node, result)\n}\n\n/**\n * Wrap `nodes` with line endings between each node.\n *\n * @template {HastRootContent} Type\n *   Node type.\n * @param {Array<Type>} nodes\n *   List of nodes to wrap.\n * @param {boolean | undefined} [loose=false]\n *   Whether to add line endings at start and end (default: `false`).\n * @returns {Array<HastText | Type>}\n *   Wrapped nodes.\n */\nexport function wrap(nodes, loose) {\n  /** @type {Array<HastText | Type>} */\n  const result = []\n  let index = -1\n\n  if (loose) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  while (++index < nodes.length) {\n    if (index) result.push({type: 'text', value: '\\n'})\n    result.push(nodes[index])\n  }\n\n  if (loose && nodes.length > 0) {\n    result.push({type: 'text', value: '\\n'})\n  }\n\n  return result\n}\n\n/**\n * Trim spaces and tabs at the start of `value`.\n *\n * @param {string} value\n *   Value to trim.\n * @returns {string}\n *   Result.\n */\nfunction trimMarkdownSpaceStart(value) {\n  let index = 0\n  let code = value.charCodeAt(index)\n\n  while (code === 9 || code === 32) {\n    index++\n    code = value.charCodeAt(index)\n  }\n\n  return value.slice(index)\n}\n", "/**\n * @typedef {import('hast').Nodes} HastNodes\n * @typedef {import('mdast').Nodes} MdastNodes\n * @typedef {import('./state.js').Options} Options\n */\n\nimport {ok as assert} from 'devlop'\nimport {footer} from './footer.js'\nimport {createState} from './state.js'\n\n/**\n * Transform mdast to hast.\n *\n * ##### Notes\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most utilities ignore `raw` nodes but two notable ones don’t:\n *\n * *   `hast-util-to-html` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful\n *     if you completely trust authors\n * *   `hast-util-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only\n *     way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `mdast-util-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * Example: headings (DOM clobbering) in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @param {MdastNodes} tree\n *   mdast tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNodes}\n *   hast tree.\n */\nexport function toHast(tree, options) {\n  const state = createState(tree, options)\n  const node = state.one(tree, undefined)\n  const foot = footer(state)\n  /** @type {HastNodes} */\n  const result = Array.isArray(node)\n    ? {type: 'root', children: node}\n    : node || {type: 'root', children: []}\n\n  if (foot) {\n    // If there’s a footer, there were definitions, meaning block\n    // content.\n    // So `result` is a parent node.\n    assert('children' in result)\n    result.children.push({type: 'text', value: '\\n'}, foot)\n  }\n\n  return result\n}\n", "// Include `data` fields in mdast and `raw` nodes in hast.\n/// <reference types=\"mdast-util-to-hast\" />\n\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\nimport {toHast} from 'mdast-util-to-hast'\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * *   if a processor is given, runs the (rehype) plugins used on it with a\n *     hast tree, then discards the result (*bridge mode*)\n * *   otherwise, returns a hast tree, the plugins used after `remarkRehype`\n *     are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**: It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * *   `rehype-stringify` also has an option `allowDangerousHtml` which will\n *     output the raw HTML.\n *     This is typically discouraged as noted by the option name but is useful if\n *     you completely trust authors\n * *   `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *     into standard hast nodes (`element`, `text`, etc).\n *     This is a heavy task as it needs a full HTML parser, but it is the only way\n *     to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark, which we follow by default.\n * They are supported by GitHub, so footnotes can be enabled in markdown with\n * `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes, which is hidden for sighted users but shown to\n * assistive technology.\n * When your page is not in English, you must define translated values.\n *\n * Back references use ARIA attributes, but the section label itself uses a\n * heading that is hidden with an `sr-only` class.\n * To show it to sighted users, define different attributes in\n * `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem, as it links footnote calls to footnote\n * definitions on the page through `id` attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * *   when the node has a `value` (and doesn’t have `data.hName`,\n *     `data.hProperties`, or `data.hChildren`, see later), create a hast `text`\n *     node\n * *   otherwise, create a `<div>` element (which could be changed with\n *     `data.hName`), with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given, configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nexport default function remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        toHast(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      toHast(tree, {file, ...(destination || options)})\n    )\n  }\n}\n", "/**\n * Throw a given error.\n *\n * @param {Error|null|undefined} [error]\n *   Maybe error.\n * @returns {asserts error is null|undefined}\n */\nexport function bail(error) {\n  if (error) {\n    throw error\n  }\n}\n", "/**\n * @typedef {import('trough').Pipeline} Pipeline\n *\n * @typedef {import('unist').Node} Node\n *\n * @typedef {import('vfile').Compatible} Compatible\n * @typedef {import('vfile').Value} Value\n *\n * @typedef {import('../index.js').CompileResultMap} CompileResultMap\n * @typedef {import('../index.js').Data} Data\n * @typedef {import('../index.js').Settings} Settings\n */\n\n/**\n * @typedef {CompileResultMap[keyof CompileResultMap]} CompileResults\n *   Acceptable results from compilers.\n *\n *   To register custom results, add them to\n *   {@linkcode CompileResultMap}.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the compiler receives (default: `Node`).\n * @template {CompileResults} [Result=CompileResults]\n *   The thing that the compiler yields (default: `CompileResults`).\n * @callback Compiler\n *   A **compiler** handles the compiling of a syntax tree to something else\n *   (in most cases, text) (TypeScript type).\n *\n *   It is used in the stringify phase and called with a {@linkcode Node}\n *   and {@linkcode VFile} representation of the document to compile.\n *   It should return the textual representation of the given tree (typically\n *   `string`).\n *\n *   > **Note**: unified typically compiles by serializing: most compilers\n *   > return `string` (or `Uint8Array`).\n *   > Some compilers, such as the one configured with\n *   > [`rehype-react`][rehype-react], return other values (in this case, a\n *   > React tree).\n *   > If you’re using a compiler that doesn’t serialize, expect different\n *   > result values.\n *   >\n *   > To register custom results in TypeScript, add them to\n *   > {@linkcode CompileResultMap}.\n *\n *   [rehype-react]: https://github.com/rehypejs/rehype-react\n * @param {Tree} tree\n *   Tree to compile.\n * @param {VFile} file\n *   File associated with `tree`.\n * @returns {Result}\n *   New content: compiled text (`string` or `Uint8Array`, for `file.value`) or\n *   something else (for `file.result`).\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The node that the parser yields (default: `Node`)\n * @callback Parser\n *   A **parser** handles the parsing of text to a syntax tree.\n *\n *   It is used in the parse phase and is called with a `string` and\n *   {@linkcode VFile} of the document to parse.\n *   It must return the syntax tree representation of the given file\n *   ({@linkcode Node}).\n * @param {string} document\n *   Document to parse.\n * @param {VFile} file\n *   File associated with `document`.\n * @returns {Tree}\n *   Node representing the given file.\n */\n\n/**\n * @typedef {(\n *   Plugin<Array<any>, any, any> |\n *   PluginTuple<Array<any>, any, any> |\n *   Preset\n * )} Pluggable\n *   Union of the different ways to add plugins and settings.\n */\n\n/**\n * @typedef {Array<Pluggable>} PluggableList\n *   List of plugins and presets.\n */\n\n// Note: we can’t use `callback` yet as it messes up `this`:\n//  <https://github.com/microsoft/TypeScript/issues/55197>.\n/**\n * @template {Array<unknown>} [PluginParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=Node]\n *   Value that is expected as input (default: `Node`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=Input]\n *   Value that is yielded as output (default: `Input`).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   (this: Processor, ...parameters: PluginParameters) =>\n *     Input extends string ? // Parser.\n *        Output extends Node | undefined ? undefined | void : never :\n *     Output extends CompileResults ? // Compiler.\n *        Input extends Node | undefined ? undefined | void : never :\n *     Transformer<\n *       Input extends Node ? Input : Node,\n *       Output extends Node ? Output : Node\n *     > | undefined | void\n * )} Plugin\n *   Single plugin.\n *\n *   Plugins configure the processors they are applied on in the following\n *   ways:\n *\n *   *   they change the processor, such as the parser, the compiler, or by\n *       configuring data\n *   *   they specify how to handle trees and files\n *\n *   In practice, they are functions that can receive options and configure the\n *   processor (`this`).\n *\n *   > **Note**: plugins are called when the processor is *frozen*, not when\n *   > they are applied.\n */\n\n/**\n * Tuple of a plugin and its configuration.\n *\n * The first item is a plugin, the rest are its parameters.\n *\n * @template {Array<unknown>} [TupleParameters=[]]\n *   Arguments passed to the plugin (default: `[]`, the empty tuple).\n * @template {Node | string | undefined} [Input=undefined]\n *   Value that is expected as input (optional).\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node it expects.\n *   *   If the plugin sets a {@linkcode Parser}, this should be\n *       `string`.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be the\n *       node it expects.\n * @template [Output=undefined] (optional).\n *   Value that is yielded as output.\n *\n *   *   If the plugin returns a {@linkcode Transformer}, this\n *       should be the node that that yields.\n *   *   If the plugin sets a {@linkcode Parser}, this should be the\n *       node that it yields.\n *   *   If the plugin sets a {@linkcode Compiler}, this should be\n *       result it yields.\n * @typedef {(\n *   [\n *     plugin: Plugin<TupleParameters, Input, Output>,\n *     ...parameters: TupleParameters\n *   ]\n * )} PluginTuple\n */\n\n/**\n * @typedef Preset\n *   Sharable configuration.\n *\n *   They can contain plugins and settings.\n * @property {PluggableList | undefined} [plugins]\n *   List of plugins and presets (optional).\n * @property {Settings | undefined} [settings]\n *   Shared settings for parsers and compilers (optional).\n */\n\n/**\n * @template {VFile} [File=VFile]\n *   The file that the callback receives (default: `VFile`).\n * @callback ProcessCallback\n *   Callback called when the process is done.\n *\n *   Called with either an error or a result.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {File | undefined} [file]\n *   Processed file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Tree=Node]\n *   The tree that the callback receives (default: `Node`).\n * @callback RunCallback\n *   Callback called when transformers are done.\n *\n *   Called with either an error or results.\n * @param {Error | undefined} [error]\n *   Fatal error (optional).\n * @param {Tree | undefined} [tree]\n *   Transformed tree (optional).\n * @param {VFile | undefined} [file]\n *   File (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Output=Node]\n *   Node type that the transformer yields (default: `Node`).\n * @callback TransformCallback\n *   Callback passed to transforms.\n *\n *   If the signature of a `transformer` accepts a third argument, the\n *   transformer may perform asynchronous operations, and must call it.\n * @param {Error | undefined} [error]\n *   Fatal error to stop the process (optional).\n * @param {Output | undefined} [tree]\n *   New, changed, tree (optional).\n * @param {VFile | undefined} [file]\n *   New, changed, file (optional).\n * @returns {undefined}\n *   Nothing.\n */\n\n/**\n * @template {Node} [Input=Node]\n *   Node type that the transformer expects (default: `Node`).\n * @template {Node} [Output=Input]\n *   Node type that the transformer yields (default: `Input`).\n * @callback Transformer\n *   Transformers handle syntax trees and files.\n *\n *   They are functions that are called each time a syntax tree and file are\n *   passed through the run phase.\n *   When an error occurs in them (either because it’s thrown, returned,\n *   rejected, or passed to `next`), the process stops.\n *\n *   The run phase is handled by [`trough`][trough], see its documentation for\n *   the exact semantics of these functions.\n *\n *   > **Note**: you should likely ignore `next`: don’t accept it.\n *   > it supports callback-style async work.\n *   > But promises are likely easier to reason about.\n *\n *   [trough]: https://github.com/wooorm/trough#function-fninput-next\n * @param {Input} tree\n *   Tree to handle.\n * @param {VFile} file\n *   File to handle.\n * @param {TransformCallback<Output>} next\n *   Callback.\n * @returns {(\n *   Promise<Output | undefined | void> |\n *   Promise<never> | // For some reason this is needed separately.\n *   Output |\n *   Error |\n *   undefined |\n *   void\n * )}\n *   If you accept `next`, nothing.\n *   Otherwise:\n *\n *   *   `Error` — fatal error to stop the process\n *   *   `Promise<undefined>` or `undefined` — the next transformer keeps using\n *       same tree\n *   *   `Promise<Node>` or `Node` — new, changed, tree\n */\n\n/**\n * @template {Node | undefined} ParseTree\n *   Output of `parse`.\n * @template {Node | undefined} HeadTree\n *   Input for `run`.\n * @template {Node | undefined} TailTree\n *   Output for `run`.\n * @template {Node | undefined} CompileTree\n *   Input of `stringify`.\n * @template {CompileResults | undefined} CompileResult\n *   Output of `stringify`.\n * @template {Node | string | undefined} Input\n *   Input of plugin.\n * @template Output\n *   Output of plugin (optional).\n * @typedef {(\n *   Input extends string\n *     ? Output extends Node | undefined\n *       ? // Parser.\n *         Processor<\n *           Output extends undefined ? ParseTree : Output,\n *           HeadTree,\n *           TailTree,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Output extends CompileResults\n *     ? Input extends Node | undefined\n *       ? // Compiler.\n *         Processor<\n *           ParseTree,\n *           HeadTree,\n *           TailTree,\n *           Input extends undefined ? CompileTree : Input,\n *           Output extends undefined ? CompileResult : Output\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : Input extends Node | undefined\n *     ? Output extends Node | undefined\n *       ? // Transform.\n *         Processor<\n *           ParseTree,\n *           HeadTree extends undefined ? Input : HeadTree,\n *           Output extends undefined ? TailTree : Output,\n *           CompileTree,\n *           CompileResult\n *         >\n *       : // Unknown.\n *         Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n *     : // Unknown.\n *       Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>\n * )} UsePlugin\n *   Create a processor based on the input/output of a {@link Plugin plugin}.\n */\n\n/**\n * @template {CompileResults | undefined} Result\n *   Node type that the transformer yields.\n * @typedef {(\n *   Result extends Value | undefined ?\n *     VFile :\n *     VFile & {result: Result}\n *   )} VFileWithOutput\n *   Type to generate a {@linkcode VFile} corresponding to a compiler result.\n *\n *   If a result that is not acceptable on a `VFile` is used, that will\n *   be stored on the `result` field of {@linkcode VFile}.\n */\n\nimport {bail} from 'bail'\nimport extend from 'extend'\nimport {ok as assert} from 'devlop'\nimport isPlainObj from 'is-plain-obj'\nimport {trough} from 'trough'\nimport {VFile} from 'vfile'\nimport {CallableInstance} from './callable-instance.js'\n\n// To do: next major: drop `Compiler`, `Parser`: prefer lowercase.\n\n// To do: we could start yielding `never` in TS when a parser is missing and\n// `parse` is called.\n// Currently, we allow directly setting `processor.parser`, which is untyped.\n\nconst own = {}.hasOwnProperty\n\n/**\n * @template {Node | undefined} [ParseTree=undefined]\n *   Output of `parse` (optional).\n * @template {Node | undefined} [HeadTree=undefined]\n *   Input for `run` (optional).\n * @template {Node | undefined} [TailTree=undefined]\n *   Output for `run` (optional).\n * @template {Node | undefined} [CompileTree=undefined]\n *   Input of `stringify` (optional).\n * @template {CompileResults | undefined} [CompileResult=undefined]\n *   Output of `stringify` (optional).\n * @extends {CallableInstance<[], Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>>}\n */\nexport class Processor extends CallableInstance {\n  /**\n   * Create a processor.\n   */\n  constructor() {\n    // If `Processor()` is called (w/o new), `copy` is called instead.\n    super('copy')\n\n    /**\n     * Compiler to use (deprecated).\n     *\n     * @deprecated\n     *   Use `compiler` instead.\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.Compiler = undefined\n\n    /**\n     * Parser to use (deprecated).\n     *\n     * @deprecated\n     *   Use `parser` instead.\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.Parser = undefined\n\n    // Note: the following fields are considered private.\n    // However, they are needed for tests, and TSC generates an untyped\n    // `private freezeIndex` field for, which trips `type-coverage` up.\n    // Instead, we use `@deprecated` to visualize that they shouldn’t be used.\n    /**\n     * Internal list of configured plugins.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Array<PluginTuple<Array<unknown>>>}\n     */\n    this.attachers = []\n\n    /**\n     * Compiler to use.\n     *\n     * @type {(\n     *   Compiler<\n     *     CompileTree extends undefined ? Node : CompileTree,\n     *     CompileResult extends undefined ? CompileResults : CompileResult\n     *   > |\n     *   undefined\n     * )}\n     */\n    this.compiler = undefined\n\n    /**\n     * Internal state to track where we are while freezing.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {number}\n     */\n    this.freezeIndex = -1\n\n    /**\n     * Internal state to track whether we’re frozen.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {boolean | undefined}\n     */\n    this.frozen = undefined\n\n    /**\n     * Internal state.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Data}\n     */\n    this.namespace = {}\n\n    /**\n     * Parser to use.\n     *\n     * @type {(\n     *   Parser<ParseTree extends undefined ? Node : ParseTree> |\n     *   undefined\n     * )}\n     */\n    this.parser = undefined\n\n    /**\n     * Internal list of configured transformers.\n     *\n     * @deprecated\n     *   This is a private internal property and should not be used.\n     * @type {Pipeline}\n     */\n    this.transformers = trough()\n  }\n\n  /**\n   * Copy a processor.\n   *\n   * @deprecated\n   *   This is a private internal method and should not be used.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   New *unfrozen* processor ({@linkcode Processor}) that is\n   *   configured to work the same as its ancestor.\n   *   When the descendant processor is configured in the future it does not\n   *   affect the ancestral processor.\n   */\n  copy() {\n    // Cast as the type parameters will be the same after attaching.\n    const destination =\n      /** @type {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>} */ (\n        new Processor()\n      )\n    let index = -1\n\n    while (++index < this.attachers.length) {\n      const attacher = this.attachers[index]\n      destination.use(...attacher)\n    }\n\n    destination.data(extend(true, {}, this.namespace))\n\n    return destination\n  }\n\n  /**\n   * Configure the processor with info available to all plugins.\n   * Information is stored in an object.\n   *\n   * Typically, options can be given to a specific plugin, but sometimes it\n   * makes sense to have information shared with several plugins.\n   * For example, a list of HTML elements that are self-closing, which is\n   * needed during all phases.\n   *\n   * > **Note**: setting information cannot occur on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * > **Note**: to register custom data in TypeScript, augment the\n   * > {@linkcode Data} interface.\n   *\n   * @example\n   *   This example show how to get and set info:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   const processor = unified().data('alpha', 'bravo')\n   *\n   *   processor.data('alpha') // => 'bravo'\n   *\n   *   processor.data() // => {alpha: 'bravo'}\n   *\n   *   processor.data({charlie: 'delta'})\n   *\n   *   processor.data() // => {charlie: 'delta'}\n   *   ```\n   *\n   * @template {keyof Data} Key\n   *\n   * @overload\n   * @returns {Data}\n   *\n   * @overload\n   * @param {Data} dataset\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Key} key\n   * @returns {Data[Key]}\n   *\n   * @overload\n   * @param {Key} key\n   * @param {Data[Key]} value\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @param {Data | Key} [key]\n   *   Key to get or set, or entire dataset to set, or nothing to get the\n   *   entire dataset (optional).\n   * @param {Data[Key]} [value]\n   *   Value to set (optional).\n   * @returns {unknown}\n   *   The current processor when setting, the value at `key` when getting, or\n   *   the entire dataset when getting without key.\n   */\n  data(key, value) {\n    if (typeof key === 'string') {\n      // Set `key`.\n      if (arguments.length === 2) {\n        assertUnfrozen('data', this.frozen)\n        this.namespace[key] = value\n        return this\n      }\n\n      // Get `key`.\n      return (own.call(this.namespace, key) && this.namespace[key]) || undefined\n    }\n\n    // Set space.\n    if (key) {\n      assertUnfrozen('data', this.frozen)\n      this.namespace = key\n      return this\n    }\n\n    // Get space.\n    return this.namespace\n  }\n\n  /**\n   * Freeze a processor.\n   *\n   * Frozen processors are meant to be extended and not to be configured\n   * directly.\n   *\n   * When a processor is frozen it cannot be unfrozen.\n   * New processors working the same way can be created by calling the\n   * processor.\n   *\n   * It’s possible to freeze processors explicitly by calling `.freeze()`.\n   * Processors freeze automatically when `.parse()`, `.run()`, `.runSync()`,\n   * `.stringify()`, `.process()`, or `.processSync()` are called.\n   *\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   The current processor.\n   */\n  freeze() {\n    if (this.frozen) {\n      return this\n    }\n\n    // Cast so that we can type plugins easier.\n    // Plugins are supposed to be usable on different processors, not just on\n    // this exact processor.\n    const self = /** @type {Processor} */ (/** @type {unknown} */ (this))\n\n    while (++this.freezeIndex < this.attachers.length) {\n      const [attacher, ...options] = this.attachers[this.freezeIndex]\n\n      if (options[0] === false) {\n        continue\n      }\n\n      if (options[0] === true) {\n        options[0] = undefined\n      }\n\n      const transformer = attacher.call(self, ...options)\n\n      if (typeof transformer === 'function') {\n        this.transformers.use(transformer)\n      }\n    }\n\n    this.frozen = true\n    this.freezeIndex = Number.POSITIVE_INFINITY\n\n    return this\n  }\n\n  /**\n   * Parse text to a syntax tree.\n   *\n   * > **Note**: `parse` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `parse` performs the parse phase, not the run phase or other\n   * > phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   file to parse (optional); typically `string` or `VFile`; any value\n   *   accepted as `x` in `new VFile(x)`.\n   * @returns {ParseTree extends undefined ? Node : ParseTree}\n   *   Syntax tree representing `file`.\n   */\n  parse(file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const parser = this.parser || this.Parser\n    assertParser('parse', parser)\n    return parser(String(realFile), realFile)\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * > **Note**: `process` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `process` performs the parse, run, and stringify phases.\n   *\n   * @overload\n   * @param {Compatible | undefined} file\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<VFileWithOutput<CompileResult>>}\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`]; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @param {ProcessCallback<VFileWithOutput<CompileResult>> | undefined} [done]\n   *   Callback (optional).\n   * @returns {Promise<VFile> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise a promise, rejected with a fatal error or resolved with the\n   *   processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  process(file, done) {\n    const self = this\n\n    this.freeze()\n    assertParser('process', this.parser || this.Parser)\n    assertCompiler('process', this.compiler || this.Compiler)\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {((file: VFileWithOutput<CompileResult>) => undefined | void) | undefined} resolve\n     * @param {(error: Error | undefined) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      const realFile = vfile(file)\n      // Assume `ParseTree` (the result of the parser) matches `HeadTree` (the\n      // input of the first transform).\n      const parseTree =\n        /** @type {HeadTree extends undefined ? Node : HeadTree} */ (\n          /** @type {unknown} */ (self.parse(realFile))\n        )\n\n      self.run(parseTree, realFile, function (error, tree, file) {\n        if (error || !tree || !file) {\n          return realDone(error)\n        }\n\n        // Assume `TailTree` (the output of the last transform) matches\n        // `CompileTree` (the input of the compiler).\n        const compileTree =\n          /** @type {CompileTree extends undefined ? Node : CompileTree} */ (\n            /** @type {unknown} */ (tree)\n          )\n\n        const compileResult = self.stringify(compileTree, file)\n\n        if (looksLikeAValue(compileResult)) {\n          file.value = compileResult\n        } else {\n          file.result = compileResult\n        }\n\n        realDone(error, /** @type {VFileWithOutput<CompileResult>} */ (file))\n      })\n\n      /**\n       * @param {Error | undefined} error\n       * @param {VFileWithOutput<CompileResult> | undefined} [file]\n       * @returns {undefined}\n       */\n      function realDone(error, file) {\n        if (error || !file) {\n          reject(error)\n        } else if (resolve) {\n          resolve(file)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Process the given file as configured on the processor.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `processSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `processSync` performs the parse, run, and stringify phases.\n   *\n   * @param {Compatible | undefined} [file]\n   *   File (optional); typically `string` or `VFile`; any value accepted as\n   *   `x` in `new VFile(x)`.\n   * @returns {VFileWithOutput<CompileResult>}\n   *   The processed file.\n   *\n   *   The parsed, transformed, and compiled value is available at\n   *   `file.value` (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most\n   *   > compilers return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  processSync(file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {VFileWithOutput<CompileResult> | undefined} */\n    let result\n\n    this.freeze()\n    assertParser('processSync', this.parser || this.Parser)\n    assertCompiler('processSync', this.compiler || this.Compiler)\n\n    this.process(file, realDone)\n    assertDone('processSync', 'process', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n\n    return result\n\n    /**\n     * @type {ProcessCallback<VFileWithOutput<CompileResult>>}\n     */\n    function realDone(error, file) {\n      complete = true\n      bail(error)\n      result = file\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * > **Note**: `run` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `run` performs the run phase, not other phases.\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} file\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} done\n   * @returns {undefined}\n   *\n   * @overload\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   * @param {Compatible | undefined} [file]\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree>}\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {(\n   *   RunCallback<TailTree extends undefined ? Node : TailTree> |\n   *   Compatible\n   * )} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @param {RunCallback<TailTree extends undefined ? Node : TailTree>} [done]\n   *   Callback (optional).\n   * @returns {Promise<TailTree extends undefined ? Node : TailTree> | undefined}\n   *   Nothing if `done` is given.\n   *   Otherwise, a promise rejected with a fatal error or resolved with the\n   *   transformed tree.\n   */\n  run(tree, file, done) {\n    assertNode(tree)\n    this.freeze()\n\n    const transformers = this.transformers\n\n    if (!done && typeof file === 'function') {\n      done = file\n      file = undefined\n    }\n\n    return done ? executor(undefined, done) : new Promise(executor)\n\n    // Note: `void`s needed for TS.\n    /**\n     * @param {(\n     *   ((tree: TailTree extends undefined ? Node : TailTree) => undefined | void) |\n     *   undefined\n     * )} resolve\n     * @param {(error: Error) => undefined | void} reject\n     * @returns {undefined}\n     */\n    function executor(resolve, reject) {\n      assert(\n        typeof file !== 'function',\n        '`file` can’t be a `done` anymore, we checked'\n      )\n      const realFile = vfile(file)\n      transformers.run(tree, realFile, realDone)\n\n      /**\n       * @param {Error | undefined} error\n       * @param {Node} outputTree\n       * @param {VFile} file\n       * @returns {undefined}\n       */\n      function realDone(error, outputTree, file) {\n        const resultingTree =\n          /** @type {TailTree extends undefined ? Node : TailTree} */ (\n            outputTree || tree\n          )\n\n        if (error) {\n          reject(error)\n        } else if (resolve) {\n          resolve(resultingTree)\n        } else {\n          assert(done, '`done` is defined if `resolve` is not')\n          done(undefined, resultingTree, file)\n        }\n      }\n    }\n  }\n\n  /**\n   * Run *transformers* on a syntax tree.\n   *\n   * An error is thrown if asynchronous transforms are configured.\n   *\n   * > **Note**: `runSync` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `runSync` performs the run phase, not other phases.\n   *\n   * @param {HeadTree extends undefined ? Node : HeadTree} tree\n   *   Tree to transform and inspect.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {TailTree extends undefined ? Node : TailTree}\n   *   Transformed tree.\n   */\n  runSync(tree, file) {\n    /** @type {boolean} */\n    let complete = false\n    /** @type {(TailTree extends undefined ? Node : TailTree) | undefined} */\n    let result\n\n    this.run(tree, file, realDone)\n\n    assertDone('runSync', 'run', complete)\n    assert(result, 'we either bailed on an error or have a tree')\n    return result\n\n    /**\n     * @type {RunCallback<TailTree extends undefined ? Node : TailTree>}\n     */\n    function realDone(error, tree) {\n      bail(error)\n      result = tree\n      complete = true\n    }\n  }\n\n  /**\n   * Compile a syntax tree.\n   *\n   * > **Note**: `stringify` freezes the processor if not already *frozen*.\n   *\n   * > **Note**: `stringify` performs the stringify phase, not the run phase\n   * > or other phases.\n   *\n   * @param {CompileTree extends undefined ? Node : CompileTree} tree\n   *   Tree to compile.\n   * @param {Compatible | undefined} [file]\n   *   File associated with `node` (optional); any value accepted as `x` in\n   *   `new VFile(x)`.\n   * @returns {CompileResult extends undefined ? Value : CompileResult}\n   *   Textual representation of the tree (see note).\n   *\n   *   > **Note**: unified typically compiles by serializing: most compilers\n   *   > return `string` (or `Uint8Array`).\n   *   > Some compilers, such as the one configured with\n   *   > [`rehype-react`][rehype-react], return other values (in this case, a\n   *   > React tree).\n   *   > If you’re using a compiler that doesn’t serialize, expect different\n   *   > result values.\n   *   >\n   *   > To register custom results in TypeScript, add them to\n   *   > {@linkcode CompileResultMap}.\n   *\n   *   [rehype-react]: https://github.com/rehypejs/rehype-react\n   */\n  stringify(tree, file) {\n    this.freeze()\n    const realFile = vfile(file)\n    const compiler = this.compiler || this.Compiler\n    assertCompiler('stringify', compiler)\n    assertNode(tree)\n\n    return compiler(tree, realFile)\n  }\n\n  /**\n   * Configure the processor to use a plugin, a list of usable values, or a\n   * preset.\n   *\n   * If the processor is already using a plugin, the previous plugin\n   * configuration is changed based on the options that are passed in.\n   * In other words, the plugin is not added a second time.\n   *\n   * > **Note**: `use` cannot be called on *frozen* processors.\n   * > Call the processor first to create a new unfrozen processor.\n   *\n   * @example\n   *   There are many ways to pass plugins to `.use()`.\n   *   This example gives an overview:\n   *\n   *   ```js\n   *   import {unified} from 'unified'\n   *\n   *   unified()\n   *     // Plugin with options:\n   *     .use(pluginA, {x: true, y: true})\n   *     // Passing the same plugin again merges configuration (to `{x: true, y: false, z: true}`):\n   *     .use(pluginA, {y: false, z: true})\n   *     // Plugins:\n   *     .use([pluginB, pluginC])\n   *     // Two plugins, the second with options:\n   *     .use([pluginD, [pluginE, {}]])\n   *     // Preset with plugins and settings:\n   *     .use({plugins: [pluginF, [pluginG, {}]], settings: {position: false}})\n   *     // Settings only:\n   *     .use({settings: {position: false}})\n   *   ```\n   *\n   * @template {Array<unknown>} [Parameters=[]]\n   * @template {Node | string | undefined} [Input=undefined]\n   * @template [Output=Input]\n   *\n   * @overload\n   * @param {Preset | null | undefined} [preset]\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {PluggableList} list\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *\n   * @overload\n   * @param {Plugin<Parameters, Input, Output>} plugin\n   * @param {...(Parameters | [boolean])} parameters\n   * @returns {UsePlugin<ParseTree, HeadTree, TailTree, CompileTree, CompileResult, Input, Output>}\n   *\n   * @param {PluggableList | Plugin | Preset | null | undefined} value\n   *   Usable value.\n   * @param {...unknown} parameters\n   *   Parameters, when a plugin is given as a usable value.\n   * @returns {Processor<ParseTree, HeadTree, TailTree, CompileTree, CompileResult>}\n   *   Current processor.\n   */\n  use(value, ...parameters) {\n    const attachers = this.attachers\n    const namespace = this.namespace\n\n    assertUnfrozen('use', this.frozen)\n\n    if (value === null || value === undefined) {\n      // Empty.\n    } else if (typeof value === 'function') {\n      addPlugin(value, parameters)\n    } else if (typeof value === 'object') {\n      if (Array.isArray(value)) {\n        addList(value)\n      } else {\n        addPreset(value)\n      }\n    } else {\n      throw new TypeError('Expected usable value, not `' + value + '`')\n    }\n\n    return this\n\n    /**\n     * @param {Pluggable} value\n     * @returns {undefined}\n     */\n    function add(value) {\n      if (typeof value === 'function') {\n        addPlugin(value, [])\n      } else if (typeof value === 'object') {\n        if (Array.isArray(value)) {\n          const [plugin, ...parameters] =\n            /** @type {PluginTuple<Array<unknown>>} */ (value)\n          addPlugin(plugin, parameters)\n        } else {\n          addPreset(value)\n        }\n      } else {\n        throw new TypeError('Expected usable value, not `' + value + '`')\n      }\n    }\n\n    /**\n     * @param {Preset} result\n     * @returns {undefined}\n     */\n    function addPreset(result) {\n      if (!('plugins' in result) && !('settings' in result)) {\n        throw new Error(\n          'Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither'\n        )\n      }\n\n      addList(result.plugins)\n\n      if (result.settings) {\n        namespace.settings = extend(true, namespace.settings, result.settings)\n      }\n    }\n\n    /**\n     * @param {PluggableList | null | undefined} plugins\n     * @returns {undefined}\n     */\n    function addList(plugins) {\n      let index = -1\n\n      if (plugins === null || plugins === undefined) {\n        // Empty.\n      } else if (Array.isArray(plugins)) {\n        while (++index < plugins.length) {\n          const thing = plugins[index]\n          add(thing)\n        }\n      } else {\n        throw new TypeError('Expected a list of plugins, not `' + plugins + '`')\n      }\n    }\n\n    /**\n     * @param {Plugin} plugin\n     * @param {Array<unknown>} parameters\n     * @returns {undefined}\n     */\n    function addPlugin(plugin, parameters) {\n      let index = -1\n      let entryIndex = -1\n\n      while (++index < attachers.length) {\n        if (attachers[index][0] === plugin) {\n          entryIndex = index\n          break\n        }\n      }\n\n      if (entryIndex === -1) {\n        attachers.push([plugin, ...parameters])\n      }\n      // Only set if there was at least a `primary` value, otherwise we’d change\n      // `arguments.length`.\n      else if (parameters.length > 0) {\n        let [primary, ...rest] = parameters\n        const currentPrimary = attachers[entryIndex][1]\n        if (isPlainObj(currentPrimary) && isPlainObj(primary)) {\n          primary = extend(true, currentPrimary, primary)\n        }\n\n        attachers[entryIndex] = [plugin, primary, ...rest]\n      }\n    }\n  }\n}\n\n// Note: this returns a *callable* instance.\n// That’s why it’s documented as a function.\n/**\n * Create a new processor.\n *\n * @example\n *   This example shows how a new processor can be created (from `remark`) and linked\n *   to **stdin**(4) and **stdout**(4).\n *\n *   ```js\n *   import process from 'node:process'\n *   import concatStream from 'concat-stream'\n *   import {remark} from 'remark'\n *\n *   process.stdin.pipe(\n *     concatStream(function (buf) {\n *       process.stdout.write(String(remark().processSync(buf)))\n *     })\n *   )\n *   ```\n *\n * @returns\n *   New *unfrozen* processor (`processor`).\n *\n *   This processor is configured to work the same as its ancestor.\n *   When the descendant processor is configured in the future it does not\n *   affect the ancestral processor.\n */\nexport const unified = new Processor().freeze()\n\n/**\n * Assert a parser is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Parser}\n */\nfunction assertParser(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `parser`')\n  }\n}\n\n/**\n * Assert a compiler is available.\n *\n * @param {string} name\n * @param {unknown} value\n * @returns {asserts value is Compiler}\n */\nfunction assertCompiler(name, value) {\n  if (typeof value !== 'function') {\n    throw new TypeError('Cannot `' + name + '` without `compiler`')\n  }\n}\n\n/**\n * Assert the processor is not frozen.\n *\n * @param {string} name\n * @param {unknown} frozen\n * @returns {asserts frozen is false}\n */\nfunction assertUnfrozen(name, frozen) {\n  if (frozen) {\n    throw new Error(\n      'Cannot call `' +\n        name +\n        '` on a frozen processor.\\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.'\n    )\n  }\n}\n\n/**\n * Assert `node` is a unist node.\n *\n * @param {unknown} node\n * @returns {asserts node is Node}\n */\nfunction assertNode(node) {\n  // `isPlainObj` unfortunately uses `any` instead of `unknown`.\n  // type-coverage:ignore-next-line\n  if (!isPlainObj(node) || typeof node.type !== 'string') {\n    throw new TypeError('Expected node, got `' + node + '`')\n    // Fine.\n  }\n}\n\n/**\n * Assert that `complete` is `true`.\n *\n * @param {string} name\n * @param {string} asyncName\n * @param {unknown} complete\n * @returns {asserts complete is true}\n */\nfunction assertDone(name, asyncName, complete) {\n  if (!complete) {\n    throw new Error(\n      '`' + name + '` finished async. Use `' + asyncName + '` instead'\n    )\n  }\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {VFile}\n */\nfunction vfile(value) {\n  return looksLikeAVFile(value) ? value : new VFile(value)\n}\n\n/**\n * @param {Compatible | undefined} [value]\n * @returns {value is VFile}\n */\nfunction looksLikeAVFile(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'message' in value &&\n      'messages' in value\n  )\n}\n\n/**\n * @param {unknown} [value]\n * @returns {value is Value}\n */\nfunction looksLikeAValue(value) {\n  return typeof value === 'string' || isUint8Array(value)\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n", "export default function isPlainObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\n\tconst prototype = Object.getPrototypeOf(value);\n\treturn (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in value) && !(Symbol.iterator in value);\n}\n", "// To do: remove `void`s\n// To do: remove `null` from output of our APIs, allow it as user APIs.\n\n/**\n * @typedef {(error?: Error | null | undefined, ...output: Array<any>) => void} Callback\n *   Callback.\n *\n * @typedef {(...input: Array<any>) => any} Middleware\n *   Ware.\n *\n * @typedef Pipeline\n *   Pipeline.\n * @property {Run} run\n *   Run the pipeline.\n * @property {Use} use\n *   Add middleware.\n *\n * @typedef {(...input: Array<any>) => void} Run\n *   Call all middleware.\n *\n *   Calls `done` on completion with either an error or the output of the\n *   last middleware.\n *\n *   > 👉 **Note**: as the length of input defines whether async functions get a\n *   > `next` function,\n *   > it’s recommended to keep `input` at one value normally.\n\n *\n * @typedef {(fn: Middleware) => Pipeline} Use\n *   Add middleware.\n */\n\n/**\n * Create new middleware.\n *\n * @returns {Pipeline}\n *   Pipeline.\n */\nexport function trough() {\n  /** @type {Array<Middleware>} */\n  const fns = []\n  /** @type {Pipeline} */\n  const pipeline = {run, use}\n\n  return pipeline\n\n  /** @type {Run} */\n  function run(...values) {\n    let middlewareIndex = -1\n    /** @type {Callback} */\n    const callback = values.pop()\n\n    if (typeof callback !== 'function') {\n      throw new TypeError('Expected function as last argument, not ' + callback)\n    }\n\n    next(null, ...values)\n\n    /**\n     * Run the next `fn`, or we’re done.\n     *\n     * @param {Error | null | undefined} error\n     * @param {Array<any>} output\n     */\n    function next(error, ...output) {\n      const fn = fns[++middlewareIndex]\n      let index = -1\n\n      if (error) {\n        callback(error)\n        return\n      }\n\n      // Copy non-nullish input into values.\n      while (++index < values.length) {\n        if (output[index] === null || output[index] === undefined) {\n          output[index] = values[index]\n        }\n      }\n\n      // Save the newly created `output` for the next call.\n      values = output\n\n      // Next or done.\n      if (fn) {\n        wrap(fn, next)(...output)\n      } else {\n        callback(null, ...output)\n      }\n    }\n  }\n\n  /** @type {Use} */\n  function use(middelware) {\n    if (typeof middelware !== 'function') {\n      throw new TypeError(\n        'Expected `middelware` to be a function, not ' + middelware\n      )\n    }\n\n    fns.push(middelware)\n    return pipeline\n  }\n}\n\n/**\n * Wrap `middleware` into a uniform interface.\n *\n * You can pass all input to the resulting function.\n * `callback` is then called with the output of `middleware`.\n *\n * If `middleware` accepts more arguments than the later given in input,\n * an extra `done` function is passed to it after that input,\n * which must be called by `middleware`.\n *\n * The first value in `input` is the main input value.\n * All other input values are the rest input values.\n * The values given to `callback` are the input values,\n * merged with every non-nullish output value.\n *\n * * if `middleware` throws an error,\n *   returns a promise that is rejected,\n *   or calls the given `done` function with an error,\n *   `callback` is called with that error\n * * if `middleware` returns a value or returns a promise that is resolved,\n *   that value is the main output value\n * * if `middleware` calls `done`,\n *   all non-nullish values except for the first one (the error) overwrite the\n *   output values\n *\n * @param {Middleware} middleware\n *   Function to wrap.\n * @param {Callback} callback\n *   Callback called with the output of `middleware`.\n * @returns {Run}\n *   Wrapped middleware.\n */\nexport function wrap(middleware, callback) {\n  /** @type {boolean} */\n  let called\n\n  return wrapped\n\n  /**\n   * Call `middleware`.\n   * @this {any}\n   * @param {Array<any>} parameters\n   * @returns {void}\n   */\n  function wrapped(...parameters) {\n    const fnExpectsCallback = middleware.length > parameters.length\n    /** @type {any} */\n    let result\n\n    if (fnExpectsCallback) {\n      parameters.push(done)\n    }\n\n    try {\n      result = middleware.apply(this, parameters)\n    } catch (error) {\n      const exception = /** @type {Error} */ (error)\n\n      // Well, this is quite the pickle.\n      // `middleware` received a callback and called it synchronously, but that\n      // threw an error.\n      // The only thing left to do is to throw the thing instead.\n      if (fnExpectsCallback && called) {\n        throw exception\n      }\n\n      return done(exception)\n    }\n\n    if (!fnExpectsCallback) {\n      if (result && result.then && typeof result.then === 'function') {\n        result.then(then, done)\n      } else if (result instanceof Error) {\n        done(result)\n      } else {\n        then(result)\n      }\n    }\n  }\n\n  /**\n   * Call `callback`, only once.\n   *\n   * @type {Callback}\n   */\n  function done(error, ...output) {\n    if (!called) {\n      called = true\n      callback(error, ...output)\n    }\n  }\n\n  /**\n   * Call `done` with one value.\n   *\n   * @param {any} [value]\n   */\n  function then(value) {\n    done(null, value)\n  }\n}\n", "// A derivative work based on:\n// <https://github.com/browserify/path-browserify>.\n// Which is licensed:\n//\n// MIT License\n//\n// Copyright (c) 2013 <PERSON>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy of\n// this software and associated documentation files (the \"Software\"), to deal in\n// the Software without restriction, including without limitation the rights to\n// use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of\n// the Software, and to permit persons to whom the Software is furnished to do so,\n// subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in all\n// copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS\n// FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR\n// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER\n// IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN\n// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A derivative work based on:\n//\n// Parts of that are extracted from Node’s internal `path` module:\n// <https://github.com/nodejs/node/blob/master/lib/path.js>.\n// Which is licensed:\n//\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nexport const minpath = {basename, dirname, extname, join, sep: '/'}\n\n/* eslint-disable max-depth, complexity */\n\n/**\n * Get the basename from a path.\n *\n * @param {string} path\n *   File path.\n * @param {string | null | undefined} [extname]\n *   Extension to strip.\n * @returns {string}\n *   Stem or basename.\n */\nfunction basename(path, extname) {\n  if (extname !== undefined && typeof extname !== 'string') {\n    throw new TypeError('\"ext\" argument must be a string')\n  }\n\n  assertPath(path)\n  let start = 0\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let seenNonSlash\n\n  if (\n    extname === undefined ||\n    extname.length === 0 ||\n    extname.length > path.length\n  ) {\n    while (index--) {\n      if (path.codePointAt(index) === 47 /* `/` */) {\n        // If we reached a path separator that was not part of a set of path\n        // separators at the end of the string, stop now.\n        if (seenNonSlash) {\n          start = index + 1\n          break\n        }\n      } else if (end < 0) {\n        // We saw the first non-path separator, mark this as the end of our\n        // path component.\n        seenNonSlash = true\n        end = index + 1\n      }\n    }\n\n    return end < 0 ? '' : path.slice(start, end)\n  }\n\n  if (extname === path) {\n    return ''\n  }\n\n  let firstNonSlashEnd = -1\n  let extnameIndex = extname.length - 1\n\n  while (index--) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (seenNonSlash) {\n        start = index + 1\n        break\n      }\n    } else {\n      if (firstNonSlashEnd < 0) {\n        // We saw the first non-path separator, remember this index in case\n        // we need it if the extension ends up not matching.\n        seenNonSlash = true\n        firstNonSlashEnd = index + 1\n      }\n\n      if (extnameIndex > -1) {\n        // Try to match the explicit extension.\n        if (path.codePointAt(index) === extname.codePointAt(extnameIndex--)) {\n          if (extnameIndex < 0) {\n            // We matched the extension, so mark this as the end of our path\n            // component\n            end = index\n          }\n        } else {\n          // Extension does not match, so our result is the entire path\n          // component\n          extnameIndex = -1\n          end = firstNonSlashEnd\n        }\n      }\n    }\n  }\n\n  if (start === end) {\n    end = firstNonSlashEnd\n  } else if (end < 0) {\n    end = path.length\n  }\n\n  return path.slice(start, end)\n}\n\n/**\n * Get the dirname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\nfunction dirname(path) {\n  assertPath(path)\n\n  if (path.length === 0) {\n    return '.'\n  }\n\n  let end = -1\n  let index = path.length\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  // Prefix `--` is important to not run on `0`.\n  while (--index) {\n    if (path.codePointAt(index) === 47 /* `/` */) {\n      if (unmatchedSlash) {\n        end = index\n        break\n      }\n    } else if (!unmatchedSlash) {\n      // We saw the first non-path separator\n      unmatchedSlash = true\n    }\n  }\n\n  return end < 0\n    ? path.codePointAt(0) === 47 /* `/` */\n      ? '/'\n      : '.'\n    : end === 1 && path.codePointAt(0) === 47 /* `/` */\n      ? '//'\n      : path.slice(0, end)\n}\n\n/**\n * Get an extname from a path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   Extname.\n */\nfunction extname(path) {\n  assertPath(path)\n\n  let index = path.length\n\n  let end = -1\n  let startPart = 0\n  let startDot = -1\n  // Track the state of characters (if any) we see before our first dot and\n  // after any path separator we find.\n  let preDotState = 0\n  /** @type {boolean | undefined} */\n  let unmatchedSlash\n\n  while (index--) {\n    const code = path.codePointAt(index)\n\n    if (code === 47 /* `/` */) {\n      // If we reached a path separator that was not part of a set of path\n      // separators at the end of the string, stop now.\n      if (unmatchedSlash) {\n        startPart = index + 1\n        break\n      }\n\n      continue\n    }\n\n    if (end < 0) {\n      // We saw the first non-path separator, mark this as the end of our\n      // extension.\n      unmatchedSlash = true\n      end = index + 1\n    }\n\n    if (code === 46 /* `.` */) {\n      // If this is our first dot, mark it as the start of our extension.\n      if (startDot < 0) {\n        startDot = index\n      } else if (preDotState !== 1) {\n        preDotState = 1\n      }\n    } else if (startDot > -1) {\n      // We saw a non-dot and non-path separator before our dot, so we should\n      // have a good chance at having a non-empty extension.\n      preDotState = -1\n    }\n  }\n\n  if (\n    startDot < 0 ||\n    end < 0 ||\n    // We saw a non-dot character immediately before the dot.\n    preDotState === 0 ||\n    // The (right-most) trimmed path component is exactly `..`.\n    (preDotState === 1 && startDot === end - 1 && startDot === startPart + 1)\n  ) {\n    return ''\n  }\n\n  return path.slice(startDot, end)\n}\n\n/**\n * Join segments from a path.\n *\n * @param {Array<string>} segments\n *   Path segments.\n * @returns {string}\n *   File path.\n */\nfunction join(...segments) {\n  let index = -1\n  /** @type {string | undefined} */\n  let joined\n\n  while (++index < segments.length) {\n    assertPath(segments[index])\n\n    if (segments[index]) {\n      joined =\n        joined === undefined ? segments[index] : joined + '/' + segments[index]\n    }\n  }\n\n  return joined === undefined ? '.' : normalize(joined)\n}\n\n/**\n * Normalize a basic file path.\n *\n * @param {string} path\n *   File path.\n * @returns {string}\n *   File path.\n */\n// Note: `normalize` is not exposed as `path.normalize`, so some code is\n// manually removed from it.\nfunction normalize(path) {\n  assertPath(path)\n\n  const absolute = path.codePointAt(0) === 47 /* `/` */\n\n  // Normalize the path according to POSIX rules.\n  let value = normalizeString(path, !absolute)\n\n  if (value.length === 0 && !absolute) {\n    value = '.'\n  }\n\n  if (value.length > 0 && path.codePointAt(path.length - 1) === 47 /* / */) {\n    value += '/'\n  }\n\n  return absolute ? '/' + value : value\n}\n\n/**\n * Resolve `.` and `..` elements in a path with directory names.\n *\n * @param {string} path\n *   File path.\n * @param {boolean} allowAboveRoot\n *   Whether `..` can move above root.\n * @returns {string}\n *   File path.\n */\nfunction normalizeString(path, allowAboveRoot) {\n  let result = ''\n  let lastSegmentLength = 0\n  let lastSlash = -1\n  let dots = 0\n  let index = -1\n  /** @type {number | undefined} */\n  let code\n  /** @type {number} */\n  let lastSlashIndex\n\n  while (++index <= path.length) {\n    if (index < path.length) {\n      code = path.codePointAt(index)\n    } else if (code === 47 /* `/` */) {\n      break\n    } else {\n      code = 47 /* `/` */\n    }\n\n    if (code === 47 /* `/` */) {\n      if (lastSlash === index - 1 || dots === 1) {\n        // Empty.\n      } else if (lastSlash !== index - 1 && dots === 2) {\n        if (\n          result.length < 2 ||\n          lastSegmentLength !== 2 ||\n          result.codePointAt(result.length - 1) !== 46 /* `.` */ ||\n          result.codePointAt(result.length - 2) !== 46 /* `.` */\n        ) {\n          if (result.length > 2) {\n            lastSlashIndex = result.lastIndexOf('/')\n\n            if (lastSlashIndex !== result.length - 1) {\n              if (lastSlashIndex < 0) {\n                result = ''\n                lastSegmentLength = 0\n              } else {\n                result = result.slice(0, lastSlashIndex)\n                lastSegmentLength = result.length - 1 - result.lastIndexOf('/')\n              }\n\n              lastSlash = index\n              dots = 0\n              continue\n            }\n          } else if (result.length > 0) {\n            result = ''\n            lastSegmentLength = 0\n            lastSlash = index\n            dots = 0\n            continue\n          }\n        }\n\n        if (allowAboveRoot) {\n          result = result.length > 0 ? result + '/..' : '..'\n          lastSegmentLength = 2\n        }\n      } else {\n        if (result.length > 0) {\n          result += '/' + path.slice(lastSlash + 1, index)\n        } else {\n          result = path.slice(lastSlash + 1, index)\n        }\n\n        lastSegmentLength = index - lastSlash - 1\n      }\n\n      lastSlash = index\n      dots = 0\n    } else if (code === 46 /* `.` */ && dots > -1) {\n      dots++\n    } else {\n      dots = -1\n    }\n  }\n\n  return result\n}\n\n/**\n * Make sure `path` is a string.\n *\n * @param {string} path\n *   File path.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path) {\n  if (typeof path !== 'string') {\n    throw new TypeError(\n      'Path must be a string. Received ' + JSON.stringify(path)\n    )\n  }\n}\n\n/* eslint-enable max-depth, complexity */\n", "// Somewhat based on:\n// <https://github.com/defunctzombie/node-process/blob/master/browser.js>.\n// But I don’t think one tiny line of code can be copyrighted. 😅\nexport const minproc = {cwd}\n\nfunction cwd() {\n  return '/'\n}\n", "/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * We check for auth attribute to distinguish legacy url instance with\n * WHATWG URL instance.\n *\n * @param {unknown} fileUrlOrPath\n *   File path or URL.\n * @returns {fileUrlOrPath is URL}\n *   Whether it’s a URL.\n */\n// From: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js#L720>\nexport function isUrl(fileUrlOrPath) {\n  return Boolean(\n    fileUrlOrPath !== null &&\n      typeof fileUrlOrPath === 'object' &&\n      'href' in fileUrlOrPath &&\n      fileUrlOrPath.href &&\n      'protocol' in fileUrlOrPath &&\n      fileUrlOrPath.protocol &&\n      // @ts-expect-error: indexing is fine.\n      fileUrlOrPath.auth === undefined\n  )\n}\n", "import {isUrl} from './minurl.shared.js'\n\nexport {isUrl} from './minurl.shared.js'\n\n// See: <https://github.com/nodejs/node/blob/6a3403c/lib/internal/url.js>\n\n/**\n * @param {URL | string} path\n *   File URL.\n * @returns {string}\n *   File URL.\n */\nexport function urlToPath(path) {\n  if (typeof path === 'string') {\n    path = new URL(path)\n  } else if (!isUrl(path)) {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'The \"path\" argument must be of type string or an instance of URL. Received `' +\n        path +\n        '`'\n    )\n    error.code = 'ERR_INVALID_ARG_TYPE'\n    throw error\n  }\n\n  if (path.protocol !== 'file:') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError('The URL must be of scheme file')\n    error.code = 'ERR_INVALID_URL_SCHEME'\n    throw error\n  }\n\n  return getPathFromURLPosix(path)\n}\n\n/**\n * Get a path from a POSIX URL.\n *\n * @param {URL} url\n *   URL.\n * @returns {string}\n *   File path.\n */\nfunction getPathFromURLPosix(url) {\n  if (url.hostname !== '') {\n    /** @type {NodeJS.ErrnoException} */\n    const error = new TypeError(\n      'File URL host must be \"localhost\" or empty on darwin'\n    )\n    error.code = 'ERR_INVALID_FILE_URL_HOST'\n    throw error\n  }\n\n  const pathname = url.pathname\n  let index = -1\n\n  while (++index < pathname.length) {\n    if (\n      pathname.codePointAt(index) === 37 /* `%` */ &&\n      pathname.codePointAt(index + 1) === 50 /* `2` */\n    ) {\n      const third = pathname.codePointAt(index + 2)\n      if (third === 70 /* `F` */ || third === 102 /* `f` */) {\n        /** @type {NodeJS.ErrnoException} */\n        const error = new TypeError(\n          'File URL path must not include encoded / characters'\n        )\n        error.code = 'ERR_INVALID_FILE_URL_PATH'\n        throw error\n      }\n    }\n  }\n\n  return decodeURIComponent(pathname)\n}\n", "/**\n * @import {Node, Point, Position} from 'unist'\n * @import {Options as MessageOptions} from 'vfile-message'\n * @import {Compatible, Data, Map, Options, Value} from 'vfile'\n */\n\n/**\n * @typedef {object & {type: string, position?: Position | undefined}} NodeLike\n */\n\nimport {VFileMessage} from 'vfile-message'\nimport {minpath} from '#minpath'\nimport {minproc} from '#minproc'\nimport {urlToPath, isUrl} from '#minurl'\n\n/**\n * Order of setting (least specific to most), we need this because otherwise\n * `{stem: 'a', path: '~/b.js'}` would throw, as a path is needed before a\n * stem can be set.\n */\nconst order = /** @type {const} */ ([\n  'history',\n  'path',\n  'basename',\n  'stem',\n  'extname',\n  'dirname'\n])\n\nexport class VFile {\n  /**\n   * Create a new virtual file.\n   *\n   * `options` is treated as:\n   *\n   * *   `string` or `Uint8Array` — `{value: options}`\n   * *   `URL` — `{path: options}`\n   * *   `VFile` — shallow copies its data over to the new file\n   * *   `object` — all fields are shallow copied over to the new file\n   *\n   * Path related fields are set in the following order (least specific to\n   * most specific): `history`, `path`, `basename`, `stem`, `extname`,\n   * `dirname`.\n   *\n   * You cannot set `dirname` or `extname` without setting either `history`,\n   * `path`, `basename`, or `stem` too.\n   *\n   * @param {Compatible | null | undefined} [value]\n   *   File value.\n   * @returns\n   *   New instance.\n   */\n  constructor(value) {\n    /** @type {Options | VFile} */\n    let options\n\n    if (!value) {\n      options = {}\n    } else if (isUrl(value)) {\n      options = {path: value}\n    } else if (typeof value === 'string' || isUint8Array(value)) {\n      options = {value}\n    } else {\n      options = value\n    }\n\n    /* eslint-disable no-unused-expressions */\n\n    /**\n     * Base of `path` (default: `process.cwd()` or `'/'` in browsers).\n     *\n     * @type {string}\n     */\n    // Prevent calling `cwd` (which could be expensive) if it’s not needed;\n    // the empty string will be overridden in the next block.\n    this.cwd = 'cwd' in options ? '' : minproc.cwd()\n\n    /**\n     * Place to store custom info (default: `{}`).\n     *\n     * It’s OK to store custom data directly on the file but moving it to\n     * `data` is recommended.\n     *\n     * @type {Data}\n     */\n    this.data = {}\n\n    /**\n     * List of file paths the file moved between.\n     *\n     * The first is the original path and the last is the current path.\n     *\n     * @type {Array<string>}\n     */\n    this.history = []\n\n    /**\n     * List of messages associated with the file.\n     *\n     * @type {Array<VFileMessage>}\n     */\n    this.messages = []\n\n    /**\n     * Raw value.\n     *\n     * @type {Value}\n     */\n    this.value\n\n    // The below are non-standard, they are “well-known”.\n    // As in, used in several tools.\n    /**\n     * Source map.\n     *\n     * This type is equivalent to the `RawSourceMap` type from the `source-map`\n     * module.\n     *\n     * @type {Map | null | undefined}\n     */\n    this.map\n\n    /**\n     * Custom, non-string, compiled, representation.\n     *\n     * This is used by unified to store non-string results.\n     * One example is when turning markdown into React nodes.\n     *\n     * @type {unknown}\n     */\n    this.result\n\n    /**\n     * Whether a file was saved to disk.\n     *\n     * This is used by vfile reporters.\n     *\n     * @type {boolean}\n     */\n    this.stored\n    /* eslint-enable no-unused-expressions */\n\n    // Set path related properties in the correct order.\n    let index = -1\n\n    while (++index < order.length) {\n      const field = order[index]\n\n      // Note: we specifically use `in` instead of `hasOwnProperty` to accept\n      // `vfile`s too.\n      if (\n        field in options &&\n        options[field] !== undefined &&\n        options[field] !== null\n      ) {\n        // @ts-expect-error: TS doesn’t understand basic reality.\n        this[field] = field === 'history' ? [...options[field]] : options[field]\n      }\n    }\n\n    /** @type {string} */\n    let field\n\n    // Set non-path related properties.\n    for (field in options) {\n      // @ts-expect-error: fine to set other things.\n      if (!order.includes(field)) {\n        // @ts-expect-error: fine to set other things.\n        this[field] = options[field]\n      }\n    }\n  }\n\n  /**\n   * Get the basename (including extname) (example: `'index.min.js'`).\n   *\n   * @returns {string | undefined}\n   *   Basename.\n   */\n  get basename() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path)\n      : undefined\n  }\n\n  /**\n   * Set basename (including extname) (`'index.min.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} basename\n   *   Basename.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set basename(basename) {\n    assertNonEmpty(basename, 'basename')\n    assertPart(basename, 'basename')\n    this.path = minpath.join(this.dirname || '', basename)\n  }\n\n  /**\n   * Get the parent path (example: `'~'`).\n   *\n   * @returns {string | undefined}\n   *   Dirname.\n   */\n  get dirname() {\n    return typeof this.path === 'string'\n      ? minpath.dirname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the parent path (example: `'~'`).\n   *\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} dirname\n   *   Dirname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set dirname(dirname) {\n    assertPath(this.basename, 'dirname')\n    this.path = minpath.join(dirname || '', this.basename)\n  }\n\n  /**\n   * Get the extname (including dot) (example: `'.js'`).\n   *\n   * @returns {string | undefined}\n   *   Extname.\n   */\n  get extname() {\n    return typeof this.path === 'string'\n      ? minpath.extname(this.path)\n      : undefined\n  }\n\n  /**\n   * Set the extname (including dot) (example: `'.js'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be set if there’s no `path` yet.\n   *\n   * @param {string | undefined} extname\n   *   Extname.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set extname(extname) {\n    assertPart(extname, 'extname')\n    assertPath(this.dirname, 'extname')\n\n    if (extname) {\n      if (extname.codePointAt(0) !== 46 /* `.` */) {\n        throw new Error('`extname` must start with `.`')\n      }\n\n      if (extname.includes('.', 1)) {\n        throw new Error('`extname` cannot contain multiple dots')\n      }\n    }\n\n    this.path = minpath.join(this.dirname, this.stem + (extname || ''))\n  }\n\n  /**\n   * Get the full path (example: `'~/index.min.js'`).\n   *\n   * @returns {string}\n   *   Path.\n   */\n  get path() {\n    return this.history[this.history.length - 1]\n  }\n\n  /**\n   * Set the full path (example: `'~/index.min.js'`).\n   *\n   * Cannot be nullified.\n   * You can set a file URL (a `URL` object with a `file:` protocol) which will\n   * be turned into a path with `url.fileURLToPath`.\n   *\n   * @param {URL | string} path\n   *   Path.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set path(path) {\n    if (isUrl(path)) {\n      path = urlToPath(path)\n    }\n\n    assertNonEmpty(path, 'path')\n\n    if (this.path !== path) {\n      this.history.push(path)\n    }\n  }\n\n  /**\n   * Get the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * @returns {string | undefined}\n   *   Stem.\n   */\n  get stem() {\n    return typeof this.path === 'string'\n      ? minpath.basename(this.path, this.extname)\n      : undefined\n  }\n\n  /**\n   * Set the stem (basename w/o extname) (example: `'index.min'`).\n   *\n   * Cannot contain path separators (`'/'` on unix, macOS, and browsers, `'\\'`\n   * on windows).\n   * Cannot be nullified (use `file.path = file.dirname` instead).\n   *\n   * @param {string} stem\n   *   Stem.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  set stem(stem) {\n    assertNonEmpty(stem, 'stem')\n    assertPart(stem, 'stem')\n    this.path = minpath.join(this.dirname || '', stem + (this.extname || ''))\n  }\n\n  // Normal prototypal methods.\n  /**\n   * Create a fatal message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `true` (error; file not usable)\n   * and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {never}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {never}\n   *   Never.\n   * @throws {VFileMessage}\n   *   Message.\n   */\n  fail(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = true\n\n    throw message\n  }\n\n  /**\n   * Create an info message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `undefined` (info; change\n   * likely not needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  info(causeOrReason, optionsOrParentOrPlace, origin) {\n    // @ts-expect-error: the overloads are fine.\n    const message = this.message(causeOrReason, optionsOrParentOrPlace, origin)\n\n    message.fatal = undefined\n\n    return message\n  }\n\n  /**\n   * Create a message for `reason` associated with the file.\n   *\n   * The `fatal` field of the message is set to `false` (warning; change may be\n   * needed) and the `file` field is set to the current file path.\n   * The message is added to the `messages` field on `file`.\n   *\n   * > 🪦 **Note**: also has obsolete signatures.\n   *\n   * @overload\n   * @param {string} reason\n   * @param {MessageOptions | null | undefined} [options]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {string} reason\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Node | NodeLike | null | undefined} parent\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {Point | Position | null | undefined} place\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @overload\n   * @param {Error | VFileMessage} cause\n   * @param {string | null | undefined} [origin]\n   * @returns {VFileMessage}\n   *\n   * @param {Error | VFileMessage | string} causeOrReason\n   *   Reason for message, should use markdown.\n   * @param {Node | NodeLike | MessageOptions | Point | Position | string | null | undefined} [optionsOrParentOrPlace]\n   *   Configuration (optional).\n   * @param {string | null | undefined} [origin]\n   *   Place in code where the message originates (example:\n   *   `'my-package:my-rule'` or `'my-rule'`).\n   * @returns {VFileMessage}\n   *   Message.\n   */\n  message(causeOrReason, optionsOrParentOrPlace, origin) {\n    const message = new VFileMessage(\n      // @ts-expect-error: the overloads are fine.\n      causeOrReason,\n      optionsOrParentOrPlace,\n      origin\n    )\n\n    if (this.path) {\n      message.name = this.path + ':' + message.name\n      message.file = this.path\n    }\n\n    message.fatal = false\n\n    this.messages.push(message)\n\n    return message\n  }\n\n  /**\n   * Serialize the file.\n   *\n   * > **Note**: which encodings are supported depends on the engine.\n   * > For info on Node.js, see:\n   * > <https://nodejs.org/api/util.html#whatwg-supported-encodings>.\n   *\n   * @param {string | null | undefined} [encoding='utf8']\n   *   Character encoding to understand `value` as when it’s a `Uint8Array`\n   *   (default: `'utf-8'`).\n   * @returns {string}\n   *   Serialized file.\n   */\n  toString(encoding) {\n    if (this.value === undefined) {\n      return ''\n    }\n\n    if (typeof this.value === 'string') {\n      return this.value\n    }\n\n    const decoder = new TextDecoder(encoding || undefined)\n    return decoder.decode(this.value)\n  }\n}\n\n/**\n * Assert that `part` is not a path (as in, does not contain `path.sep`).\n *\n * @param {string | null | undefined} part\n *   File path part.\n * @param {string} name\n *   Part name.\n * @returns {undefined}\n *   Nothing.\n */\nfunction assertPart(part, name) {\n  if (part && part.includes(minpath.sep)) {\n    throw new Error(\n      '`' + name + '` cannot be a path: did not expect `' + minpath.sep + '`'\n    )\n  }\n}\n\n/**\n * Assert that `part` is not empty.\n *\n * @param {string | undefined} part\n *   Thing.\n * @param {string} name\n *   Part name.\n * @returns {asserts part is string}\n *   Nothing.\n */\nfunction assertNonEmpty(part, name) {\n  if (!part) {\n    throw new Error('`' + name + '` cannot be empty')\n  }\n}\n\n/**\n * Assert `path` exists.\n *\n * @param {string | undefined} path\n *   Path.\n * @param {string} name\n *   Dependency name.\n * @returns {asserts path is string}\n *   Nothing.\n */\nfunction assertPath(path, name) {\n  if (!path) {\n    throw new Error('Setting `' + name + '` requires `path` to be set too')\n  }\n}\n\n/**\n * Assert `value` is an `Uint8Array`.\n *\n * @param {unknown} value\n *   thing.\n * @returns {value is Uint8Array}\n *   Whether `value` is an `Uint8Array`.\n */\nfunction isUint8Array(value) {\n  return Boolean(\n    value &&\n      typeof value === 'object' &&\n      'byteLength' in value &&\n      'byteOffset' in value\n  )\n}\n", "export const CallableInstance =\n  /**\n   * @type {new <Parameters extends Array<unknown>, Result>(property: string | symbol) => (...parameters: Parameters) => Result}\n   */\n  (\n    /** @type {unknown} */\n    (\n      /**\n       * @this {Function}\n       * @param {string | symbol} property\n       * @returns {(...parameters: Array<unknown>) => unknown}\n       */\n      function (property) {\n        const self = this\n        const constr = self.constructor\n        const proto = /** @type {Record<string | symbol, Function>} */ (\n          // Prototypes do exist.\n          // type-coverage:ignore-next-line\n          constr.prototype\n        )\n        const value = proto[property]\n        /** @type {(...parameters: Array<unknown>) => unknown} */\n        const apply = function () {\n          return value.apply(apply, arguments)\n        }\n\n        Object.setPrototypeOf(apply, proto)\n\n        // Not needed for us in `unified`: we only call this on the `copy`\n        // function,\n        // and we don't need to add its fields (`length`, `name`)\n        // over.\n        // See also: GH-246.\n        // const names = Object.getOwnPropertyNames(value)\n        //\n        // for (const p of names) {\n        //   const descriptor = Object.getOwnPropertyDescriptor(value, p)\n        //   if (descriptor) Object.defineProperty(apply, p, descriptor)\n        // }\n\n        return apply\n      }\n    )\n  )\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAEA,QAAI,gBAAgB;AAEpB,QAAI,gBAAgB;AACpB,QAAI,mBAAmB;AAGvB,QAAI,iBAAiB;AACrB,QAAI,cAAc;AAClB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AAGtB,QAAI,aAAa;AAGjB,QAAI,UAAU;AACd,QAAI,gBAAgB;AACpB,QAAI,WAAW;AACf,QAAI,eAAe;AAGnB,QAAI,eAAe;AACnB,QAAI,mBAAmB;AASvB,WAAO,UAAU,SAAU,OAAO,SAAS;AACzC,UAAI,OAAO,UAAU,UAAU;AAC7B,cAAM,IAAI,UAAU,iCAAiC;AAAA,MACvD;AAEA,UAAI,CAAC;AAAO,eAAO,CAAC;AAEpB,gBAAU,WAAW,CAAC;AAKtB,UAAI,SAAS;AACb,UAAI,SAAS;AAOb,eAAS,eAAe,KAAK;AAC3B,YAAI,QAAQ,IAAI,MAAM,aAAa;AACnC,YAAI;AAAO,oBAAU,MAAM;AAC3B,YAAI,IAAI,IAAI,YAAY,OAAO;AAC/B,iBAAS,CAAC,IAAI,IAAI,SAAS,IAAI,SAAS,IAAI;AAAA,MAC9C;AAOA,eAASA,YAAW;AAClB,YAAIC,SAAQ,EAAE,MAAM,QAAQ,OAAe;AAC3C,eAAO,SAAU,MAAM;AACrB,eAAK,WAAW,IAAI,SAASA,MAAK;AAClC,UAAAC,YAAW;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAUA,eAAS,SAASD,QAAO;AACvB,aAAK,QAAQA;AACb,aAAK,MAAM,EAAE,MAAM,QAAQ,OAAe;AAC1C,aAAK,SAAS,QAAQ;AAAA,MACxB;AAKA,eAAS,UAAU,UAAU;AAE7B,UAAI,aAAa,CAAC;AAQlB,eAAS,MAAM,KAAK;AAClB,YAAI,MAAM,IAAI;AAAA,UACZ,QAAQ,SAAS,MAAM,SAAS,MAAM,SAAS,OAAO;AAAA,QACxD;AACA,YAAI,SAAS;AACb,YAAI,WAAW,QAAQ;AACvB,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,SAAS;AAEb,YAAI,QAAQ,QAAQ;AAClB,qBAAW,KAAK,GAAG;AAAA,QACrB,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAQA,eAAS,MAAME,KAAI;AACjB,YAAI,IAAIA,IAAG,KAAK,KAAK;AACrB,YAAI,CAAC;AAAG;AACR,YAAI,MAAM,EAAE,CAAC;AACb,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,IAAI,MAAM;AAC9B,eAAO;AAAA,MACT;AAKA,eAASD,cAAa;AACpB,cAAM,gBAAgB;AAAA,MACxB;AAQA,eAAS,SAAS,OAAO;AACvB,YAAI;AACJ,gBAAQ,SAAS,CAAC;AAClB,eAAQ,IAAI,QAAQ,GAAI;AACtB,cAAI,MAAM,OAAO;AACf,kBAAM,KAAK,CAAC;AAAA,UACd;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAQA,eAAS,UAAU;AACjB,YAAI,MAAMF,UAAS;AACnB,YAAI,iBAAiB,MAAM,OAAO,CAAC,KAAK,YAAY,MAAM,OAAO,CAAC;AAAG;AAErE,YAAI,IAAI;AACR,eACE,gBAAgB,MAAM,OAAO,CAAC,MAC7B,YAAY,MAAM,OAAO,CAAC,KAAK,iBAAiB,MAAM,OAAO,IAAI,CAAC,IACnE;AACA,YAAE;AAAA,QACJ;AACA,aAAK;AAEL,YAAI,iBAAiB,MAAM,OAAO,IAAI,CAAC,GAAG;AACxC,iBAAO,MAAM,wBAAwB;AAAA,QACvC;AAEA,YAAI,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC;AAC9B,kBAAU;AACV,uBAAe,GAAG;AAClB,gBAAQ,MAAM,MAAM,CAAC;AACrB,kBAAU;AAEV,eAAO,IAAI;AAAA,UACT,MAAM;AAAA,UACN,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAQA,eAAS,cAAc;AACrB,YAAI,MAAMA,UAAS;AAGnB,YAAI,OAAO,MAAM,cAAc;AAC/B,YAAI,CAAC;AAAM;AACX,gBAAQ;AAGR,YAAI,CAAC,MAAM,WAAW;AAAG,iBAAO,MAAM,sBAAsB;AAG5D,YAAI,MAAM,MAAM,WAAW;AAE3B,YAAI,MAAM,IAAI;AAAA,UACZ,MAAM;AAAA,UACN,UAAU,KAAK,KAAK,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC;AAAA,UAC3D,OAAO,MACH,KAAK,IAAI,CAAC,EAAE,QAAQ,eAAe,YAAY,CAAC,IAChD;AAAA,QACN,CAAC;AAGD,cAAM,eAAe;AAErB,eAAO;AAAA,MACT;AAOA,eAAS,eAAe;AACtB,YAAI,QAAQ,CAAC;AAEb,iBAAS,KAAK;AAGd,YAAI;AACJ,eAAQ,OAAO,YAAY,GAAI;AAC7B,cAAI,SAAS,OAAO;AAClB,kBAAM,KAAK,IAAI;AACf,qBAAS,KAAK;AAAA,UAChB;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,MAAAE,YAAW;AACX,aAAO,aAAa;AAAA,IACtB;AAQA,aAAS,KAAK,KAAK;AACjB,aAAO,MAAM,IAAI,QAAQ,YAAY,YAAY,IAAI;AAAA,IACvD;AAAA;AAAA;;;;;;;;;;ACvOA,YAAA,UAAA;AA5BA,QAAA,wBAAA,gBAAA,6BAAA;AA4BA,aAAwB,cACtB,OACA,UAAmB;AAEnB,UAAI,cAAkC;AAEtC,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAO;MACT;AAEA,UAAM,gBAAe,GAAA,sBAAA,SAAM,KAAK;AAChC,UAAM,cAAc,OAAO,aAAa;AAExC,mBAAa,QAAQ,SAAC,aAAW;AAC/B,YAAI,YAAY,SAAS,eAAe;AACtC;QACF;AAEQ,YAAA,WAAoB,YAAW,UAArB,QAAU,YAAW;AAEvC,YAAI,aAAa;AACf,mBAAS,UAAU,OAAO,WAAW;QACvC,WAAW,OAAO;AAChB,wBAAc,eAAe,CAAA;AAC7B,sBAAY,QAAQ,IAAI;QAC1B;MACF,CAAC;AAED,aAAO;IACT;;;;;;;;;;AC1DA,QAAM,wBAAwB;AAC9B,QAAM,eAAe;AACrB,QAAM,kBAAkB;AACxB,QAAM,sBAAsB;AAC5B,QAAM,yBAAyB;AAK/B,QAAM,gBAAgB,SAAC,UAAgB;AACrC,aAAA,CAAC,YACD,gBAAgB,KAAK,QAAQ,KAC7B,sBAAsB,KAAK,QAAQ;IAFnC;AAOF,QAAM,aAAa,SAAC,OAAe,WAAiB;AAClD,aAAA,UAAU,YAAW;IAArB;AAKF,QAAM,aAAa,SAAC,OAAe,QAAc;AAAK,aAAA,GAAA,OAAG,QAAM,GAAA;IAAT;AAY/C,QAAM,YAAY,SAAC,UAAkB,SAA8B;AAA9B,UAAA,YAAA,QAAA;AAAA,kBAAA,CAAA;MAA8B;AACxE,UAAI,cAAc,QAAQ,GAAG;AAC3B,eAAO;MACT;AAEA,iBAAW,SAAS,YAAW;AAE/B,UAAI,QAAQ,aAAa;AAEvB,mBAAW,SAAS,QAAQ,wBAAwB,UAAU;MAChE,OAAO;AAEL,mBAAW,SAAS,QAAQ,qBAAqB,UAAU;MAC7D;AAEA,aAAO,SAAS,QAAQ,cAAc,UAAU;IAClD;AAhBa,YAAA,YAAS;;;;;;;;;;;ACnCtB,QAAA,oBAAA,gBAAA,aAAA;AAEA,QAAA,cAAA;AASA,aAAS,UAAU,OAAe,SAA0B;AAC1D,UAAM,SAAsB,CAAA;AAE5B,UAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,eAAO;MACT;AAEA,OAAA,GAAA,kBAAA,SAAc,OAAO,SAAC,UAAU,OAAK;AAEnC,YAAI,YAAY,OAAO;AACrB,kBAAO,GAAA,YAAA,WAAU,UAAU,OAAO,CAAC,IAAI;QACzC;MACF,CAAC;AAED,aAAO;IACT;AAEA,cAAU,UAAU;AAEpB,WAAA,UAAS;;;;;AC9BT;AAAA;AAIA,QAAI,IAAI;AACR,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AACZ,QAAI,IAAI,IAAI;AAgBZ,WAAO,UAAU,SAAU,KAAK,SAAS;AACvC,gBAAU,WAAW,CAAC;AACtB,UAAI,OAAO,OAAO;AAClB,UAAI,SAAS,YAAY,IAAI,SAAS,GAAG;AACvC,eAAOE,OAAM,GAAG;AAAA,MAClB,WAAW,SAAS,YAAY,SAAS,GAAG,GAAG;AAC7C,eAAO,QAAQ,OAAO,QAAQ,GAAG,IAAI,SAAS,GAAG;AAAA,MACnD;AACA,YAAM,IAAI;AAAA,QACR,0DACE,KAAK,UAAU,GAAG;AAAA,MACtB;AAAA,IACF;AAUA,aAASA,OAAM,KAAK;AAClB,YAAM,OAAO,GAAG;AAChB,UAAI,IAAI,SAAS,KAAK;AACpB;AAAA,MACF;AACA,UAAI,QAAQ,mIAAmI;AAAA,QAC7I;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV;AAAA,MACF;AACA,UAAI,IAAI,WAAW,MAAM,CAAC,CAAC;AAC3B,UAAI,QAAQ,MAAM,CAAC,KAAK,MAAM,YAAY;AAC1C,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,IAAI;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO;AAAA,QACT;AACE,iBAAO;AAAA,MACX;AAAA,IACF;AAUA,aAAS,SAAS,IAAI;AACpB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,UAAI,SAAS,GAAG;AACd,eAAO,KAAK,MAAM,KAAK,CAAC,IAAI;AAAA,MAC9B;AACA,aAAO,KAAK;AAAA,IACd;AAUA,aAAS,QAAQ,IAAI;AACnB,UAAI,QAAQ,KAAK,IAAI,EAAE;AACvB,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,KAAK;AAAA,MACnC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,MAAM;AAAA,MACpC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,UAAI,SAAS,GAAG;AACd,eAAO,OAAO,IAAI,OAAO,GAAG,QAAQ;AAAA,MACtC;AACA,aAAO,KAAK;AAAA,IACd;AAMA,aAAS,OAAO,IAAI,OAAO,GAAGC,OAAM;AAClC,UAAI,WAAW,SAAS,IAAI;AAC5B,aAAO,KAAK,MAAM,KAAK,CAAC,IAAI,MAAMA,SAAQ,WAAW,MAAM;AAAA,IAC7D;AAAA;AAAA;;;ACjKA;AAAA;AAMA,aAAS,MAAM,KAAK;AACnB,MAAAC,aAAY,QAAQA;AACpB,MAAAA,aAAY,UAAUA;AACtB,MAAAA,aAAY,SAAS;AACrB,MAAAA,aAAY,UAAUC;AACtB,MAAAD,aAAY,SAAS;AACrB,MAAAA,aAAY,UAAU;AACtB,MAAAA,aAAY,WAAW;AACvB,MAAAA,aAAY,UAAU;AAEtB,aAAO,KAAK,GAAG,EAAE,QAAQ,SAAO;AAC/B,QAAAA,aAAY,GAAG,IAAI,IAAI,GAAG;AAAA,MAC3B,CAAC;AAMD,MAAAA,aAAY,QAAQ,CAAC;AACrB,MAAAA,aAAY,QAAQ,CAAC;AAOrB,MAAAA,aAAY,aAAa,CAAC;AAQ1B,eAAS,YAAY,WAAW;AAC/B,YAAI,OAAO;AAEX,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,kBAAS,QAAQ,KAAK,OAAQ,UAAU,WAAW,CAAC;AACpD,kBAAQ;AAAA,QACT;AAEA,eAAOA,aAAY,OAAO,KAAK,IAAI,IAAI,IAAIA,aAAY,OAAO,MAAM;AAAA,MACrE;AACA,MAAAA,aAAY,cAAc;AAS1B,eAASA,aAAY,WAAW;AAC/B,YAAI;AACJ,YAAI,iBAAiB;AACrB,YAAI;AACJ,YAAI;AAEJ,iBAASE,UAAS,MAAM;AAEvB,cAAI,CAACA,OAAM,SAAS;AACnB;AAAA,UACD;AAEA,gBAAM,OAAOA;AAGb,gBAAM,OAAO,OAAO,oBAAI,KAAK,CAAC;AAC9B,gBAAM,KAAK,QAAQ,YAAY;AAC/B,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,eAAK,OAAO;AACZ,qBAAW;AAEX,eAAK,CAAC,IAAIF,aAAY,OAAO,KAAK,CAAC,CAAC;AAEpC,cAAI,OAAO,KAAK,CAAC,MAAM,UAAU;AAEhC,iBAAK,QAAQ,IAAI;AAAA,UAClB;AAGA,cAAIG,SAAQ;AACZ,eAAK,CAAC,IAAI,KAAK,CAAC,EAAE,QAAQ,iBAAiB,CAAC,OAAO,WAAW;AAE7D,gBAAI,UAAU,MAAM;AACnB,qBAAO;AAAA,YACR;AACA,YAAAA;AACA,kBAAM,YAAYH,aAAY,WAAW,MAAM;AAC/C,gBAAI,OAAO,cAAc,YAAY;AACpC,oBAAM,MAAM,KAAKG,MAAK;AACtB,sBAAQ,UAAU,KAAK,MAAM,GAAG;AAGhC,mBAAK,OAAOA,QAAO,CAAC;AACpB,cAAAA;AAAA,YACD;AACA,mBAAO;AAAA,UACR,CAAC;AAGD,UAAAH,aAAY,WAAW,KAAK,MAAM,IAAI;AAEtC,gBAAM,QAAQ,KAAK,OAAOA,aAAY;AACtC,gBAAM,MAAM,MAAM,IAAI;AAAA,QACvB;AAEA,QAAAE,OAAM,YAAY;AAClB,QAAAA,OAAM,YAAYF,aAAY,UAAU;AACxC,QAAAE,OAAM,QAAQF,aAAY,YAAY,SAAS;AAC/C,QAAAE,OAAM,SAASE;AACf,QAAAF,OAAM,UAAUF,aAAY;AAE5B,eAAO,eAAeE,QAAO,WAAW;AAAA,UACvC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,KAAK,MAAM;AACV,gBAAI,mBAAmB,MAAM;AAC5B,qBAAO;AAAA,YACR;AACA,gBAAI,oBAAoBF,aAAY,YAAY;AAC/C,gCAAkBA,aAAY;AAC9B,6BAAeA,aAAY,QAAQ,SAAS;AAAA,YAC7C;AAEA,mBAAO;AAAA,UACR;AAAA,UACA,KAAK,OAAK;AACT,6BAAiB;AAAA,UAClB;AAAA,QACD,CAAC;AAGD,YAAI,OAAOA,aAAY,SAAS,YAAY;AAC3C,UAAAA,aAAY,KAAKE,MAAK;AAAA,QACvB;AAEA,eAAOA;AAAA,MACR;AAEA,eAASE,QAAO,WAAW,WAAW;AACrC,cAAM,WAAWJ,aAAY,KAAK,aAAa,OAAO,cAAc,cAAc,MAAM,aAAa,SAAS;AAC9G,iBAAS,MAAM,KAAK;AACpB,eAAO;AAAA,MACR;AASA,eAAS,OAAO,YAAY;AAC3B,QAAAA,aAAY,KAAK,UAAU;AAC3B,QAAAA,aAAY,aAAa;AAEzB,QAAAA,aAAY,QAAQ,CAAC;AACrB,QAAAA,aAAY,QAAQ,CAAC;AAErB,cAAM,SAAS,OAAO,eAAe,WAAW,aAAa,IAC3D,KAAK,EACL,QAAQ,KAAK,GAAG,EAChB,MAAM,GAAG,EACT,OAAO,OAAO;AAEhB,mBAAW,MAAM,OAAO;AACvB,cAAI,GAAG,CAAC,MAAM,KAAK;AAClB,YAAAA,aAAY,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAAA,UACnC,OAAO;AACN,YAAAA,aAAY,MAAM,KAAK,EAAE;AAAA,UAC1B;AAAA,QACD;AAAA,MACD;AAUA,eAAS,gBAAgBK,SAAQ,UAAU;AAC1C,YAAI,cAAc;AAClB,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAChB,YAAI,aAAa;AAEjB,eAAO,cAAcA,QAAO,QAAQ;AACnC,cAAI,gBAAgB,SAAS,WAAW,SAAS,aAAa,MAAMA,QAAO,WAAW,KAAK,SAAS,aAAa,MAAM,MAAM;AAE5H,gBAAI,SAAS,aAAa,MAAM,KAAK;AACpC,0BAAY;AACZ,2BAAa;AACb;AAAA,YACD,OAAO;AACN;AACA;AAAA,YACD;AAAA,UACD,WAAW,cAAc,IAAI;AAE5B,4BAAgB,YAAY;AAC5B;AACA,0BAAc;AAAA,UACf,OAAO;AACN,mBAAO;AAAA,UACR;AAAA,QACD;AAGA,eAAO,gBAAgB,SAAS,UAAU,SAAS,aAAa,MAAM,KAAK;AAC1E;AAAA,QACD;AAEA,eAAO,kBAAkB,SAAS;AAAA,MACnC;AAQA,eAASJ,WAAU;AAClB,cAAM,aAAa;AAAA,UAClB,GAAGD,aAAY;AAAA,UACf,GAAGA,aAAY,MAAM,IAAI,eAAa,MAAM,SAAS;AAAA,QACtD,EAAE,KAAK,GAAG;AACV,QAAAA,aAAY,OAAO,EAAE;AACrB,eAAO;AAAA,MACR;AASA,eAAS,QAAQM,OAAM;AACtB,mBAAW,QAAQN,aAAY,OAAO;AACrC,cAAI,gBAAgBM,OAAM,IAAI,GAAG;AAChC,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,mBAAW,MAAMN,aAAY,OAAO;AACnC,cAAI,gBAAgBM,OAAM,EAAE,GAAG;AAC9B,mBAAO;AAAA,UACR;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AASA,eAAS,OAAO,KAAK;AACpB,YAAI,eAAe,OAAO;AACzB,iBAAO,IAAI,SAAS,IAAI;AAAA,QACzB;AACA,eAAO;AAAA,MACR;AAMA,eAAS,UAAU;AAClB,gBAAQ,KAAK,uIAAuI;AAAA,MACrJ;AAEA,MAAAN,aAAY,OAAOA,aAAY,KAAK,CAAC;AAErC,aAAOA;AAAA,IACR;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACnSjB;AAAA;AAMA,YAAQ,aAAa;AACrB,YAAQ,OAAO;AACf,YAAQ,OAAO;AACf,YAAQ,YAAY;AACpB,YAAQ,UAAU,aAAa;AAC/B,YAAQ,WAAW,MAAM;AACxB,UAAI,SAAS;AAEb,aAAO,MAAM;AACZ,YAAI,CAAC,QAAQ;AACZ,mBAAS;AACT,kBAAQ,KAAK,uIAAuI;AAAA,QACrJ;AAAA,MACD;AAAA,IACD,GAAG;AAMH,YAAQ,SAAS;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAWA,aAAS,YAAY;AAIpB,UAAI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,SAAS,cAAc,OAAO,QAAQ,SAAS;AACrH,eAAO;AAAA,MACR;AAGA,UAAI,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,uBAAuB,GAAG;AAChI,eAAO;AAAA,MACR;AAEA,UAAI;AAKJ,aAAQ,OAAO,aAAa,eAAe,SAAS,mBAAmB,SAAS,gBAAgB,SAAS,SAAS,gBAAgB,MAAM;AAAA,MAEtI,OAAO,WAAW,eAAe,OAAO,YAAY,OAAO,QAAQ,WAAY,OAAO,QAAQ,aAAa,OAAO,QAAQ;AAAA;AAAA,MAG1H,OAAO,cAAc,eAAe,UAAU,cAAc,IAAI,UAAU,UAAU,YAAY,EAAE,MAAM,gBAAgB,MAAM,SAAS,EAAE,CAAC,GAAG,EAAE,KAAK;AAAA,MAEpJ,OAAO,cAAc,eAAe,UAAU,aAAa,UAAU,UAAU,YAAY,EAAE,MAAM,oBAAoB;AAAA,IAC1H;AAQA,aAAS,WAAW,MAAM;AACzB,WAAK,CAAC,KAAK,KAAK,YAAY,OAAO,MAClC,KAAK,aACJ,KAAK,YAAY,QAAQ,OAC1B,KAAK,CAAC,KACL,KAAK,YAAY,QAAQ,OAC1B,MAAM,OAAO,QAAQ,SAAS,KAAK,IAAI;AAExC,UAAI,CAAC,KAAK,WAAW;AACpB;AAAA,MACD;AAEA,YAAM,IAAI,YAAY,KAAK;AAC3B,WAAK,OAAO,GAAG,GAAG,GAAG,gBAAgB;AAKrC,UAAIO,SAAQ;AACZ,UAAI,QAAQ;AACZ,WAAK,CAAC,EAAE,QAAQ,eAAe,WAAS;AACvC,YAAI,UAAU,MAAM;AACnB;AAAA,QACD;AACA,QAAAA;AACA,YAAI,UAAU,MAAM;AAGnB,kBAAQA;AAAA,QACT;AAAA,MACD,CAAC;AAED,WAAK,OAAO,OAAO,GAAG,CAAC;AAAA,IACxB;AAUA,YAAQ,MAAM,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,IAAC;AAQtD,aAAS,KAAK,YAAY;AACzB,UAAI;AACH,YAAI,YAAY;AACf,kBAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QAC5C,OAAO;AACN,kBAAQ,QAAQ,WAAW,OAAO;AAAA,QACnC;AAAA,MACD,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAQA,aAAS,OAAO;AACf,UAAI;AACJ,UAAI;AACH,YAAI,QAAQ,QAAQ,QAAQ,OAAO;AAAA,MACpC,SAAS,OAAO;AAAA,MAGhB;AAGA,UAAI,CAAC,KAAK,OAAO,YAAY,eAAe,SAAS,SAAS;AAC7D,YAAI,QAAQ,IAAI;AAAA,MACjB;AAEA,aAAO;AAAA,IACR;AAaA,aAAS,eAAe;AACvB,UAAI;AAGH,eAAO;AAAA,MACR,SAAS,OAAO;AAAA,MAGhB;AAAA,IACD;AAEA,WAAO,UAAU,iBAAoB,OAAO;AAE5C,QAAM,EAAC,WAAU,IAAI,OAAO;AAM5B,eAAW,IAAI,SAAU,GAAG;AAC3B,UAAI;AACH,eAAO,KAAK,UAAU,CAAC;AAAA,MACxB,SAAS,OAAO;AACf,eAAO,iCAAiC,MAAM;AAAA,MAC/C;AAAA,IACD;AAAA;AAAA;;;AC/QA;AAAA;AAAA;AAEA,QAAI,SAAS,OAAO,UAAU;AAC9B,QAAI,QAAQ,OAAO,UAAU;AAC7B,QAAI,iBAAiB,OAAO;AAC5B,QAAI,OAAO,OAAO;AAElB,QAAI,UAAU,SAASC,SAAQ,KAAK;AACnC,UAAI,OAAO,MAAM,YAAY,YAAY;AACxC,eAAO,MAAM,QAAQ,GAAG;AAAA,MACzB;AAEA,aAAO,MAAM,KAAK,GAAG,MAAM;AAAA,IAC5B;AAEA,QAAIC,iBAAgB,SAASA,eAAc,KAAK;AAC/C,UAAI,CAAC,OAAO,MAAM,KAAK,GAAG,MAAM,mBAAmB;AAClD,eAAO;AAAA,MACR;AAEA,UAAI,oBAAoB,OAAO,KAAK,KAAK,aAAa;AACtD,UAAI,mBAAmB,IAAI,eAAe,IAAI,YAAY,aAAa,OAAO,KAAK,IAAI,YAAY,WAAW,eAAe;AAE7H,UAAI,IAAI,eAAe,CAAC,qBAAqB,CAAC,kBAAkB;AAC/D,eAAO;AAAA,MACR;AAIA,UAAI;AACJ,WAAK,OAAO,KAAK;AAAA,MAAO;AAExB,aAAO,OAAO,QAAQ,eAAe,OAAO,KAAK,KAAK,GAAG;AAAA,IAC1D;AAGA,QAAI,cAAc,SAASC,aAAY,QAAQ,SAAS;AACvD,UAAI,kBAAkB,QAAQ,SAAS,aAAa;AACnD,uBAAe,QAAQ,QAAQ,MAAM;AAAA,UACpC,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,OAAO,QAAQ;AAAA,UACf,UAAU;AAAA,QACX,CAAC;AAAA,MACF,OAAO;AACN,eAAO,QAAQ,IAAI,IAAI,QAAQ;AAAA,MAChC;AAAA,IACD;AAGA,QAAI,cAAc,SAASC,aAAY,KAAKC,OAAM;AACjD,UAAIA,UAAS,aAAa;AACzB,YAAI,CAAC,OAAO,KAAK,KAAKA,KAAI,GAAG;AAC5B,iBAAO;AAAA,QACR,WAAW,MAAM;AAGhB,iBAAO,KAAK,KAAKA,KAAI,EAAE;AAAA,QACxB;AAAA,MACD;AAEA,aAAO,IAAIA,KAAI;AAAA,IAChB;AAEA,WAAO,UAAU,SAASC,UAAS;AAClC,UAAI,SAASD,OAAM,KAAK,MAAM,aAAa;AAC3C,UAAI,SAAS,UAAU,CAAC;AACxB,UAAI,IAAI;AACR,UAAI,SAAS,UAAU;AACvB,UAAI,OAAO;AAGX,UAAI,OAAO,WAAW,WAAW;AAChC,eAAO;AACP,iBAAS,UAAU,CAAC,KAAK,CAAC;AAE1B,YAAI;AAAA,MACL;AACA,UAAI,UAAU,QAAS,OAAO,WAAW,YAAY,OAAO,WAAW,YAAa;AACnF,iBAAS,CAAC;AAAA,MACX;AAEA,aAAO,IAAI,QAAQ,EAAE,GAAG;AACvB,kBAAU,UAAU,CAAC;AAErB,YAAI,WAAW,MAAM;AAEpB,eAAKA,SAAQ,SAAS;AACrB,kBAAM,YAAY,QAAQA,KAAI;AAC9B,mBAAO,YAAY,SAASA,KAAI;AAGhC,gBAAI,WAAW,MAAM;AAEpB,kBAAI,QAAQ,SAASH,eAAc,IAAI,MAAM,cAAc,QAAQ,IAAI,KAAK;AAC3E,oBAAI,aAAa;AAChB,gCAAc;AACd,0BAAQ,OAAO,QAAQ,GAAG,IAAI,MAAM,CAAC;AAAA,gBACtC,OAAO;AACN,0BAAQ,OAAOA,eAAc,GAAG,IAAI,MAAM,CAAC;AAAA,gBAC5C;AAGA,4BAAY,QAAQ,EAAE,MAAMG,OAAM,UAAUC,QAAO,MAAM,OAAO,IAAI,EAAE,CAAC;AAAA,cAGxE,WAAW,OAAO,SAAS,aAAa;AACvC,4BAAY,QAAQ,EAAE,MAAMD,OAAM,UAAU,KAAK,CAAC;AAAA,cACnD;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAGA,aAAO;AAAA,IACR;AAAA;AAAA;;;AC1GA,IAAM,SAAS;AACf,IAAM,YAAY;AAGlB,IAAM,eAAe,CAAC;AA0Cf,SAAS,KAAKE,OAAM,SAAS;AAClC,QAAM,WAAW,WAAW;AAC5B,QAAMC,MAAK,SAAS,MAAM,YAAY;AACtC,SAAOA,IAAG,KAAKD,KAAI;AACrB;;;ACtDA,IAAM,KAAK;AAaJ,SAAS,WAAW,OAAO;AAChC,SAAO,OAAO,UAAU,WACpB,MAAM,SAAS,SACb,MAAM,MAAM,KAAK,IACjB,QACF,MAAM,KAAK;AACjB;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,MAAM,QAAQ,IAAI,EAAE,MAAM;AACnC;;;AChBA,yBAAsB;;;ACmBf,SAAS,kBAAkB,OAAO;AAEvC,MAAI,CAAC,SAAS,OAAO,UAAU,UAAU;AACvC,WAAO;AAAA,EACT;AAGA,MAAI,cAAc,SAAS,UAAU,OAAO;AAC1C,WAAOE,UAAS,MAAM,QAAQ;AAAA,EAChC;AAGA,MAAI,WAAW,SAAS,SAAS,OAAO;AACtC,WAAOA,UAAS,KAAK;AAAA,EACvB;AAGA,MAAI,UAAU,SAAS,YAAY,OAAO;AACxC,WAAO,MAAM,KAAK;AAAA,EACpB;AAGA,SAAO;AACT;AAMA,SAAS,MAAMC,QAAO;AACpB,SAAO,MAAMA,UAASA,OAAM,IAAI,IAAI,MAAM,MAAMA,UAASA,OAAM,MAAM;AACvE;AAMA,SAASD,UAAS,KAAK;AACrB,SAAO,MAAM,OAAO,IAAI,KAAK,IAAI,MAAM,MAAM,OAAO,IAAI,GAAG;AAC7D;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,SAAS,OAAO,UAAU,WAAW,QAAQ;AACtD;;;ACvDO,IAAM,eAAN,cAA2B,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwDtC,YAAY,eAAe,wBAAwB,QAAQ;AACzD,UAAM;AAEN,QAAI,OAAO,2BAA2B,UAAU;AAC9C,eAAS;AACT,+BAAyB;AAAA,IAC3B;AAGA,QAAI,SAAS;AAEb,QAAI,UAAU,CAAC;AACf,QAAI,cAAc;AAElB,QAAI,wBAAwB;AAE1B,UACE,UAAU,0BACV,YAAY,wBACZ;AACA,kBAAU,EAAC,OAAO,uBAAsB;AAAA,MAC1C,WAGE,WAAW,0BACX,SAAS,wBACT;AACA,kBAAU,EAAC,OAAO,uBAAsB;AAAA,MAC1C,WAES,UAAU,wBAAwB;AACzC,kBAAU;AAAA,UACR,WAAW,CAAC,sBAAsB;AAAA,UAClC,OAAO,uBAAuB;AAAA,QAChC;AAAA,MACF,OAEK;AACH,kBAAU,EAAC,GAAG,uBAAsB;AAAA,MACtC;AAAA,IACF;AAEA,QAAI,OAAO,kBAAkB,UAAU;AACrC,eAAS;AAAA,IACX,WAES,CAAC,QAAQ,SAAS,eAAe;AACxC,oBAAc;AACd,eAAS,cAAc;AACvB,cAAQ,QAAQ;AAAA,IAClB;AAEA,QAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,UAAU,OAAO,WAAW,UAAU;AACpE,YAAME,SAAQ,OAAO,QAAQ,GAAG;AAEhC,UAAIA,WAAU,IAAI;AAChB,gBAAQ,SAAS;AAAA,MACnB,OAAO;AACL,gBAAQ,SAAS,OAAO,MAAM,GAAGA,MAAK;AACtC,gBAAQ,SAAS,OAAO,MAAMA,SAAQ,CAAC;AAAA,MACzC;AAAA,IACF;AAEA,QAAI,CAAC,QAAQ,SAAS,QAAQ,aAAa,QAAQ,WAAW;AAC5D,YAAM,SAAS,QAAQ,UAAU,QAAQ,UAAU,SAAS,CAAC;AAE7D,UAAI,QAAQ;AACV,gBAAQ,QAAQ,OAAO;AAAA,MACzB;AAAA,IACF;AAEA,UAAMC,SACJ,QAAQ,SAAS,WAAW,QAAQ,QAChC,QAAQ,MAAM,QACd,QAAQ;AAQd,SAAK,YAAY,QAAQ,aAAa;AAOtC,SAAK,QAAQ,QAAQ,SAAS;AAO9B,SAAK,SAASA,SAAQA,OAAM,SAAS;AAWrC,SAAK,QAAQ;AAOb,SAAK;AAQL,SAAK,UAAU;AAOf,SAAK,OAAOA,SAAQA,OAAM,OAAO;AASjC,SAAK,OAAO,kBAAkB,QAAQ,KAAK,KAAK;AAOhD,SAAK,QAAQ,QAAQ,SAAS;AAO9B,SAAK,SAAS,KAAK;AAOnB,SAAK,SAAS,QAAQ,UAAU;AAOhC,SAAK,SAAS,QAAQ,UAAU;AAWhC,SAAK,QACH,eAAe,QAAQ,SAAS,OAAO,QAAQ,MAAM,UAAU,WAC3D,QAAQ,MAAM,QACd;AAYN,SAAK;AAOL,SAAK;AAOL,SAAK;AAUL,SAAK;AAAA,EAEP;AACF;AAEA,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,UAAU;AACjC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,OAAO;AAC9B,aAAa,UAAU,YAAY;AACnC,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,QAAQ;AAC/B,aAAa,UAAU,SAAS;AAChC,aAAa,UAAU,SAAS;;;AFvShC,IAAM,MAAM,CAAC,EAAE;AAGf,IAAM,WAAW,oBAAI,IAAI;AAEzB,IAAM,MAAM;AAaZ,IAAM,gBAAgB,oBAAI,IAAI,CAAC,SAAS,SAAS,SAAS,SAAS,IAAI,CAAC;AAExE,IAAM,mBAAmB,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AAE7C,IAAM,OAAO;AAcN,SAAS,aAAa,MAAM,SAAS;AAC1C,MAAI,CAAC,WAAW,QAAQ,aAAa,QAAW;AAC9C,UAAM,IAAI,UAAU,gCAAgC;AAAA,EACtD;AAEA,QAAM,WAAW,QAAQ,YAAY;AAErC,MAAI;AAEJ,MAAI,QAAQ,aAAa;AACvB,QAAI,OAAO,QAAQ,WAAW,YAAY;AACxC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,aAAS,kBAAkB,UAAU,QAAQ,MAAM;AAAA,EACrD,OAAO;AACL,QAAI,OAAO,QAAQ,QAAQ,YAAY;AACrC,YAAM,IAAI,UAAU,sCAAsC;AAAA,IAC5D;AAEA,QAAI,OAAO,QAAQ,SAAS,YAAY;AACtC,YAAM,IAAI,UAAU,uCAAuC;AAAA,IAC7D;AAEA,aAAS,iBAAiB,UAAU,QAAQ,KAAK,QAAQ,IAAI;AAAA,EAC/D;AAGA,QAAM,QAAQ;AAAA,IACZ,UAAU,QAAQ;AAAA,IAClB,WAAW,CAAC;AAAA,IACZ,YAAY,QAAQ,cAAc,CAAC;AAAA,IACnC;AAAA,IACA,0BAA0B,QAAQ,4BAA4B;AAAA,IAC9D,WAAW,QAAQ,kBAAkB,QAAQ,gBAAgB,IAAI;AAAA,IACjE;AAAA,IACA,oBAAoB,QAAQ,sBAAsB;AAAA,IAClD,UAAU,QAAQ,aAAa;AAAA,IAC/B,UAAU,QAAQ,YAAY;AAAA,IAC9B,QAAQ,QAAQ,UAAU,QAAQ,MAAM;AAAA,IACxC,uBAAuB,QAAQ,yBAAyB;AAAA,IACxD,uBAAuB,QAAQ,0BAA0B;AAAA,EAC3D;AAEA,QAAM,SAAS,IAAI,OAAO,MAAM,MAAS;AAGzC,MAAI,UAAU,OAAO,WAAW,UAAU;AACxC,WAAO;AAAA,EACT;AAGA,SAAO,MAAM;AAAA,IACX;AAAA,IACA,MAAM;AAAA,IACN,EAAC,UAAU,UAAU,OAAS;AAAA,IAC9B;AAAA,EACF;AACF;AAcA,SAAS,IAAI,OAAO,MAAM,KAAK;AAC7B,MAAI,KAAK,SAAS,WAAW;AAC3B,WAAO,QAAQ,OAAO,MAAM,GAAG;AAAA,EACjC;AAEA,MAAI,KAAK,SAAS,uBAAuB,KAAK,SAAS,qBAAqB;AAC1E,WAAO,cAAc,OAAO,IAAI;AAAA,EAClC;AAEA,MAAI,KAAK,SAAS,uBAAuB,KAAK,SAAS,qBAAqB;AAC1E,WAAO,cAAc,OAAO,MAAM,GAAG;AAAA,EACvC;AAEA,MAAI,KAAK,SAAS,YAAY;AAC5B,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAEA,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO,KAAK,OAAO,MAAM,GAAG;AAAA,EAC9B;AAEA,MAAI,KAAK,SAAS,QAAQ;AACxB,WAAO,KAAK,OAAO,IAAI;AAAA,EACzB;AACF;AAcA,SAAS,QAAQ,OAAO,MAAM,KAAK;AACjC,QAAM,eAAe,MAAM;AAC3B,MAAI,SAAS;AAEb,MAAI,KAAK,QAAQ,YAAY,MAAM,SAAS,aAAa,UAAU,QAAQ;AACzE,aAAS;AACT,UAAM,SAAS;AAAA,EACjB;AAEA,QAAM,UAAU,KAAK,IAAI;AAEzB,QAAM,OAAO,sBAAsB,OAAO,KAAK,SAAS,KAAK;AAC7D,QAAM,QAAQ,mBAAmB,OAAO,IAAI;AAC5C,MAAI,WAAW,eAAe,OAAO,IAAI;AAEzC,MAAI,cAAc,IAAI,KAAK,OAAO,GAAG;AACnC,eAAW,SAAS,OAAO,SAAU,OAAO;AAC1C,aAAO,OAAO,UAAU,WAAW,CAAC,WAAW,KAAK,IAAI;AAAA,IAC1D,CAAC;AAAA,EACH;AAEA,UAAQ,OAAO,OAAO,MAAM,IAAI;AAChC,cAAY,OAAO,QAAQ;AAG3B,QAAM,UAAU,IAAI;AACpB,QAAM,SAAS;AAEf,SAAO,MAAM,OAAO,MAAM,MAAM,OAAO,GAAG;AAC5C;AAYA,SAAS,cAAc,OAAO,MAAM;AAClC,MAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,MAAM,WAAW;AACpD,UAAM,UAAU,KAAK,KAAK;AAC1B,UAAM,aAAa,QAAQ,KAAK,CAAC;AACjC,OAAO,WAAW,SAAS,qBAAqB;AAGhD;AAAA;AAAA,MACE,MAAM,UAAU,mBAAmB,WAAW,UAAU;AAAA;AAAA,EAE5D;AAEA,cAAY,OAAO,KAAK,QAAQ;AAClC;AAYA,SAAS,OAAO,OAAO,MAAM;AAC3B,MAAI,KAAK,QAAQ,KAAK,KAAK,UAAU,MAAM,WAAW;AAEpD;AAAA;AAAA,MACE,MAAM,UAAU,gBAAgB,KAAK,KAAK,MAAM;AAAA;AAAA,EAEpD;AAEA,cAAY,OAAO,KAAK,QAAQ;AAClC;AAcA,SAAS,cAAc,OAAO,MAAM,KAAK;AACvC,QAAM,eAAe,MAAM;AAC3B,MAAI,SAAS;AAEb,MAAI,KAAK,SAAS,SAAS,aAAa,UAAU,QAAQ;AACxD,aAAS;AACT,UAAM,SAAS;AAAA,EACjB;AAEA,QAAM,UAAU,KAAK,IAAI;AAEzB,QAAM,OACJ,KAAK,SAAS,OACV,MAAM,WACN,sBAAsB,OAAO,KAAK,MAAM,IAAI;AAClD,QAAM,QAAQ,sBAAsB,OAAO,IAAI;AAC/C,QAAM,WAAW,eAAe,OAAO,IAAI;AAE3C,UAAQ,OAAO,OAAO,MAAM,IAAI;AAChC,cAAY,OAAO,QAAQ;AAG3B,QAAM,UAAU,IAAI;AACpB,QAAM,SAAS;AAEf,SAAO,MAAM,OAAO,MAAM,MAAM,OAAO,GAAG;AAC5C;AAcA,SAAS,KAAK,OAAO,MAAM,KAAK;AAE9B,QAAM,QAAQ,CAAC;AAEf,cAAY,OAAO,eAAe,OAAO,IAAI,CAAC;AAE9C,SAAO,MAAM,OAAO,MAAM,MAAM,UAAU,OAAO,GAAG;AACtD;AAYA,SAAS,KAAK,GAAG,MAAM;AACrB,SAAO,KAAK;AACd;AAgBA,SAAS,QAAQ,OAAO,OAAO,MAAM,MAAM;AAEzC,MAAI,OAAO,SAAS,YAAY,SAAS,MAAM,YAAY,MAAM,UAAU;AACzE,UAAM,OAAO;AAAA,EACf;AACF;AAYA,SAAS,YAAY,OAAO,UAAU;AACpC,MAAI,SAAS,SAAS,GAAG;AACvB,UAAM,QAAQ,SAAS,SAAS,IAAI,WAAW,SAAS,CAAC;AAEzD,QAAI,OAAO;AACT,YAAM,WAAW;AAAA,IACnB;AAAA,EACF;AACF;AAYA,SAAS,iBAAiB,GAAGC,MAAKC,OAAM;AACtC,SAAO;AAEP,WAAS,OAAOC,IAAG,MAAM,OAAO,KAAK;AAEnC,UAAM,mBAAmB,MAAM,QAAQ,MAAM,QAAQ;AACrD,UAAM,KAAK,mBAAmBD,QAAOD;AACrC,WAAO,MAAM,GAAG,MAAM,OAAO,GAAG,IAAI,GAAG,MAAM,KAAK;AAAA,EACpD;AACF;AAUA,SAAS,kBAAkB,UAAU,QAAQ;AAC3C,SAAO;AAEP,WAAS,OAAO,MAAM,MAAM,OAAO,KAAK;AAEtC,UAAM,mBAAmB,MAAM,QAAQ,MAAM,QAAQ;AACrD,UAAMG,SAAQ,WAAW,IAAI;AAC7B,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,QACE,cAAcA,SAAQA,OAAM,SAAS,IAAI;AAAA,QACzC,UAAU;AAAA,QACV,YAAYA,SAAQA,OAAM,OAAO;AAAA,MACnC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAYA,SAAS,mBAAmB,OAAO,MAAM;AAEvC,QAAM,QAAQ,CAAC;AAEf,MAAI;AAEJ,MAAI;AAEJ,OAAK,QAAQ,KAAK,YAAY;AAC5B,QAAI,SAAS,cAAc,IAAI,KAAK,KAAK,YAAY,IAAI,GAAG;AAC1D,YAAM,SAAS,eAAe,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC;AAEhE,UAAI,QAAQ;AACV,cAAM,CAAC,KAAK,KAAK,IAAI;AAErB,YACE,MAAM,yBACN,QAAQ,WACR,OAAO,UAAU,YACjB,iBAAiB,IAAI,KAAK,OAAO,GACjC;AACA,uBAAa;AAAA,QACf,OAAO;AACL,gBAAM,GAAG,IAAI;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,YAAY;AAEd,UAAM;AAAA;AAAA,MAA8B,MAAM,UAAU,MAAM,QAAQ,CAAC;AAAA;AACnE,UAAM,MAAM,0BAA0B,QAAQ,eAAe,WAAW,IACtE;AAAA,EACJ;AAEA,SAAO;AACT;AAYA,SAAS,sBAAsB,OAAO,MAAM;AAE1C,QAAM,QAAQ,CAAC;AAEf,aAAW,aAAa,KAAK,YAAY;AACvC,QAAI,UAAU,SAAS,6BAA6B;AAClD,UAAI,UAAU,QAAQ,UAAU,KAAK,UAAU,MAAM,WAAW;AAC9D,cAAM,UAAU,UAAU,KAAK;AAC/B,cAAM,aAAa,QAAQ,KAAK,CAAC;AACjC,WAAO,WAAW,SAAS,qBAAqB;AAChD,cAAM,mBAAmB,WAAW;AACpC,WAAO,iBAAiB,SAAS,kBAAkB;AACnD,cAAM,WAAW,iBAAiB,WAAW,CAAC;AAC9C,WAAO,SAAS,SAAS,eAAe;AAExC,eAAO;AAAA,UACL;AAAA,UACA,MAAM,UAAU,mBAAmB,SAAS,QAAQ;AAAA,QACtD;AAAA,MACF,OAAO;AACL,oBAAY,OAAO,KAAK,QAAQ;AAAA,MAClC;AAAA,IACF,OAAO;AAEL,YAAMC,QAAO,UAAU;AAEvB,UAAI;AAEJ,UAAI,UAAU,SAAS,OAAO,UAAU,UAAU,UAAU;AAC1D,YACE,UAAU,MAAM,QAChB,UAAU,MAAM,KAAK,UACrB,MAAM,WACN;AACA,gBAAM,UAAU,UAAU,MAAM,KAAK;AACrC,gBAAM,aAAa,QAAQ,KAAK,CAAC;AACjC,aAAO,WAAW,SAAS,qBAAqB;AAChD,kBAAQ,MAAM,UAAU,mBAAmB,WAAW,UAAU;AAAA,QAClE,OAAO;AACL,sBAAY,OAAO,KAAK,QAAQ;AAAA,QAClC;AAAA,MACF,OAAO;AACL,gBAAQ,UAAU,UAAU,OAAO,OAAO,UAAU;AAAA,MACtD;AAGA,YAAMA,KAAI;AAAA,MAAuC;AAAA,IACnD;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,eAAe,OAAO,MAAM;AAEnC,QAAM,WAAW,CAAC;AAClB,MAAIC,SAAQ;AAIZ,QAAM,eAAe,MAAM,WAAW,oBAAI,IAAI,IAAI;AAElD,SAAO,EAAEA,SAAQ,KAAK,SAAS,QAAQ;AACrC,UAAM,QAAQ,KAAK,SAASA,MAAK;AAEjC,QAAI;AAEJ,QAAI,MAAM,UAAU;AAClB,YAAMD,QACJ,MAAM,SAAS,YACX,MAAM,UACN,MAAM,SAAS,uBACb,MAAM,SAAS,sBACf,MAAM,OACN;AAER,UAAIA,OAAM;AACR,cAAM,QAAQ,aAAa,IAAIA,KAAI,KAAK;AACxC,cAAMA,QAAO,MAAM;AACnB,qBAAa,IAAIA,OAAM,QAAQ,CAAC;AAAA,MAClC;AAAA,IACF;AAEA,UAAM,SAAS,IAAI,OAAO,OAAO,GAAG;AACpC,QAAI,WAAW;AAAW,eAAS,KAAK,MAAM;AAAA,EAChD;AAEA,SAAO;AACT;AAcA,SAAS,eAAe,OAAO,MAAM,OAAO;AAC1C,QAAM,OAAO,KAAK,MAAM,QAAQ,IAAI;AAGpC,MACE,UAAU,QACV,UAAU,UACT,OAAO,UAAU,YAAY,OAAO,MAAM,KAAK,GAChD;AACA;AAAA,EACF;AAEA,MAAI,MAAM,QAAQ,KAAK,GAAG;AAGxB,YAAQ,KAAK,iBAAiB,UAAO,KAAK,IAAIE,WAAO,KAAK;AAAA,EAC5D;AAGA,MAAI,KAAK,aAAa,SAAS;AAC7B,QAAI,cACF,OAAO,UAAU,WAAW,QAAQ,WAAW,OAAO,OAAO,KAAK,CAAC;AAErE,QAAI,MAAM,0BAA0B,OAAO;AACzC,oBAAc,2BAA2B,WAAW;AAAA,IACtD;AAEA,WAAO,CAAC,SAAS,WAAW;AAAA,EAC9B;AAEA,SAAO;AAAA,IACL,MAAM,6BAA6B,WAAW,KAAK,QAC/C,YAAY,KAAK,QAAQ,KAAK,KAAK,WACnC,KAAK;AAAA,IACT;AAAA,EACF;AACF;AAcA,SAAS,WAAW,OAAO,OAAO;AAChC,MAAI;AACF,eAAO,mBAAAC,SAAU,OAAO,EAAC,aAAa,KAAI,CAAC;AAAA,EAC7C,SAAS,OAAO;AACd,QAAI,MAAM,oBAAoB;AAC5B,aAAO,CAAC;AAAA,IACV;AAEA,UAAM;AAAA;AAAA,MAA8B;AAAA;AACpC,UAAM,UAAU,IAAI,aAAa,kCAAkC;AAAA,MACjE,WAAW,MAAM;AAAA,MACjB;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV,CAAC;AACD,YAAQ,OAAO,MAAM,YAAY;AACjC,YAAQ,MAAM,OAAO;AAErB,UAAM;AAAA,EACR;AACF;AAcA,SAAS,sBAAsB,OAAOH,OAAM,iBAAiB;AAE3D,MAAI;AAEJ,MAAI,CAAC,iBAAiB;AACpB,aAAS,EAAC,MAAM,WAAW,OAAOA,MAAI;AAAA,EACxC,WAAWA,MAAK,SAAS,GAAG,GAAG;AAC7B,UAAM,cAAcA,MAAK,MAAM,GAAG;AAClC,QAAIC,SAAQ;AAEZ,QAAI;AAEJ,WAAO,EAAEA,SAAQ,YAAY,QAAQ;AAEnC,YAAM,OAAO,KAAiB,YAAYA,MAAK,CAAC,IAC5C,EAAC,MAAM,cAAc,MAAM,YAAYA,MAAK,EAAC,IAC7C,EAAC,MAAM,WAAW,OAAO,YAAYA,MAAK,EAAC;AAC/C,aAAO,OACH;AAAA,QACE,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU,QAAQA,UAAS,KAAK,SAAS,SAAS;AAAA,QAClD,UAAU;AAAA,MACZ,IACA;AAAA,IACN;AAEA,OAAO,MAAM,iBAAiB;AAC9B,aAAS;AAAA,EACX,OAAO;AACL,aACE,KAAiBD,KAAI,KAAK,CAAC,SAAS,KAAKA,KAAI,IACzC,EAAC,MAAM,cAAc,MAAAA,MAAI,IACzB,EAAC,MAAM,WAAW,OAAOA,MAAI;AAAA,EACrC;AAIA,MAAI,OAAO,SAAS,WAAW;AAC7B,UAAMA;AAAA;AAAA,MAAuC,OAAO;AAAA;AACpD,WAAO,IAAI,KAAK,MAAM,YAAYA,KAAI,IAAI,MAAM,WAAWA,KAAI,IAAIA;AAAA,EACrE;AAGA,MAAI,MAAM,WAAW;AACnB,WAAO,MAAM,UAAU,mBAAmB,MAAM;AAAA,EAClD;AAEA,cAAY,KAAK;AACnB;AAOA,SAAS,YAAY,OAAO,OAAO;AACjC,QAAM,UAAU,IAAI;AAAA,IAClB;AAAA,IACA;AAAA,MACE,WAAW,MAAM;AAAA,MACjB;AAAA,MACA,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AACA,UAAQ,OAAO,MAAM,YAAY;AACjC,UAAQ,MAAM,OAAO;AAErB,QAAM;AACR;AAQA,SAAS,2BAA2B,WAAW;AAE7C,QAAM,YAAY,CAAC;AAEnB,MAAI;AAEJ,OAAK,QAAQ,WAAW;AACtB,QAAI,IAAI,KAAK,WAAW,IAAI,GAAG;AAC7B,gBAAU,0BAA0B,IAAI,CAAC,IAAI,UAAU,IAAI;AAAA,IAC7D;AAAA,EACF;AAEA,SAAO;AACT;AAQA,SAAS,0BAA0B,MAAM;AACvC,MAAI,KAAK,KAAK,QAAQ,KAAK,MAAM;AAEjC,MAAI,GAAG,MAAM,GAAG,CAAC,MAAM;AAAO,SAAK,MAAM;AACzC,SAAO;AACT;AAUA,SAAS,OAAO,IAAI;AAClB,SAAO,MAAM,GAAG,YAAY;AAC9B;;;AG1wBO,IAAM,gBAAgB;AAAA,EAC3B,QAAQ,CAAC,MAAM;AAAA,EACf,MAAM,CAAC,cAAc,OAAO,OAAO,GAAG;AAAA,EACtC,MAAM,CAAC,QAAQ;AAAA,EACf,YAAY,CAAC,UAAU,OAAO;AAAA,EAC9B,MAAM,CAAC,KAAK,QAAQ,QAAQ,MAAM;AAAA,EAClC,MAAM,CAAC,UAAU;AAAA,EACjB,QAAQ;AAAA,EACR,UAAU,CAAC,MAAM;AAAA,EACjB,MAAM,CAAC,KAAK,MAAM;AAAA,EAClB,QAAQ,CAAC,OAAO;AAAA,EAChB,KAAK;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;;;AC8EA,yBAAkC;AAClC,mBAAkC;;;AC3DlC,IAAM,iBAAiB,CAAC,EAAE;;;AClCnB,IAAMI,WAAU,EAAC,UAAU,kBAAiB;AAQnD,SAAS,kBAAkB,SAAS;AAClC,QAAM,eAAe,QAAQ;AAAA,IAC3B,KAAK,OAAO,WAAW;AAAA,IACvB;AAAA,IACA;AAAA,EACF;AAEA,MAAI;AAEJ,SAAO;AAGP,WAAS,2BAA2BC,OAAM;AACxC;AAAA,MACEA,UAAS,MAAM,OAAO,mBAAmBA,KAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAIA,UAAS,MAAM,KAAK;AACtB,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,WAAO,aAAa,SAAS,cAAc,MAAM,UAAU;AAAA,EAC7D;AAGA,WAAS,iBAAiBA,OAAM;AAC9B;AAAA,MACEA,UAAS,MAAM,OAAO,CAAC,mBAAmBA,KAAI;AAAA,MAC9C;AAAA,IACF;AACA,YAAQ,MAAM,MAAM,SAAS;AAC7B,WAAO,UAAUA,KAAI;AAAA,EACvB;AAGA,WAAS,UAAUA,OAAM;AACvB,UAAM,QAAQ,QAAQ,MAAM,MAAM,WAAW;AAAA,MAC3C,aAAa,UAAU;AAAA,MACvB;AAAA,IACF,CAAC;AAED,QAAI,UAAU;AACZ,eAAS,OAAO;AAAA,IAClB;AAEA,eAAW;AAEX,WAAO,KAAKA,KAAI;AAAA,EAClB;AAGA,WAAS,KAAKA,OAAM;AAClB,QAAIA,UAAS,MAAM,KAAK;AACtB,cAAQ,KAAK,MAAM,SAAS;AAC5B,cAAQ,KAAK,MAAM,SAAS;AAC5B,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,QAAI,mBAAmBA,KAAI,GAAG;AAC5B,cAAQ,QAAQA,KAAI;AACpB,cAAQ,KAAK,MAAM,SAAS;AAC5B,aAAO;AAAA,IACT;AAGA,YAAQ,QAAQA,KAAI;AACpB,WAAO;AAAA,EACT;AACF;;;ACxEO,IAAMC,YAAW,EAAC,UAAU,mBAAkB;AAGrD,IAAM,qBAAqB,EAAC,UAAU,kBAAiB;AAQvD,SAAS,mBAAmB,SAAS;AACnC,QAAM,OAAO;AAEb,QAAM,QAAQ,CAAC;AACf,MAAI,YAAY;AAEhB,MAAI;AAEJ,MAAI;AAEJ,MAAI;AAEJ,SAAOC;AAGP,WAASA,OAAMC,OAAM;AAWnB,QAAI,YAAY,MAAM,QAAQ;AAC5B,YAAM,OAAO,MAAM,SAAS;AAC5B,WAAK,iBAAiB,KAAK,CAAC;AAC5B;AAAA,QACE,KAAK,CAAC,EAAE;AAAA,QACR;AAAA,MACF;AACA,aAAO,QAAQ;AAAA,QACb,KAAK,CAAC,EAAE;AAAA,QACR;AAAA,QACA;AAAA,MACF,EAAEA,KAAI;AAAA,IACR;AAGA,WAAO,mBAAmBA,KAAI;AAAA,EAChC;AAGA,WAAS,iBAAiBA,OAAM;AAC9B;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AAEA;AAKA,QAAI,KAAK,eAAe,YAAY;AAClC,WAAK,eAAe,aAAa;AAEjC,UAAI,WAAW;AACb,kBAAU;AAAA,MACZ;AAIA,YAAM,mBAAmB,KAAK,OAAO;AACrC,UAAI,kBAAkB;AAEtB,UAAIC;AAGJ,aAAO,mBAAmB;AACxB,YACE,KAAK,OAAO,eAAe,EAAE,CAAC,MAAM,UACpC,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE,SAAS,MAAM,WAC/C;AACA,UAAAA,SAAQ,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE;AACxC;AAAA,QACF;AAAA,MACF;AAEA,SAAOA,QAAO,oCAAoC;AAElD,qBAAe,SAAS;AAGxB,UAAIC,SAAQ;AAEZ,aAAOA,SAAQ,KAAK,OAAO,QAAQ;AACjC,aAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,EAAC,GAAGD,OAAK;AACrC,QAAAC;AAAA,MACF;AAGA;AAAA,QACE,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB;AAAA,QACA,KAAK,OAAO,MAAM,gBAAgB;AAAA,MACpC;AAGA,WAAK,OAAO,SAASA;AAErB,aAAO,mBAAmBF,KAAI;AAAA,IAChC;AAEA,WAAOD,OAAMC,KAAI;AAAA,EACnB;AAGA,WAAS,mBAAmBA,OAAM;AAMhC,QAAI,cAAc,MAAM,QAAQ;AAI9B,UAAI,CAAC,WAAW;AACd,eAAO,kBAAkBA,KAAI;AAAA,MAC/B;AAKA,UAAI,UAAU,oBAAoB,UAAU,iBAAiB,UAAU;AACrE,eAAO,UAAUA,KAAI;AAAA,MACvB;AAOA,WAAK,YAAY;AAAA,QACf,UAAU,oBAAoB,CAAC,UAAU;AAAA,MAC3C;AAAA,IACF;AAGA,SAAK,iBAAiB,CAAC;AACvB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAEA,KAAI;AAAA,EACR;AAGA,WAAS,qBAAqBA,OAAM;AAClC,QAAI;AAAW,gBAAU;AACzB,mBAAe,SAAS;AACxB,WAAO,kBAAkBA,KAAI;AAAA,EAC/B;AAGA,WAAS,sBAAsBA,OAAM;AACnC,SAAK,OAAO,KAAK,KAAK,IAAI,EAAE,IAAI,IAAI,cAAc,MAAM;AACxD,sBAAkB,KAAK,IAAI,EAAE;AAC7B,WAAO,UAAUA,KAAI;AAAA,EACvB;AAGA,WAAS,kBAAkBA,OAAM;AAE/B,SAAK,iBAAiB,CAAC;AACvB,WAAO,QAAQ;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,IACF,EAAEA,KAAI;AAAA,EACR;AAGA,WAAS,kBAAkBA,OAAM;AAC/B;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA;AACA,UAAM,KAAK,CAAC,KAAK,kBAAkB,KAAK,cAAc,CAAC;AAEvD,WAAO,kBAAkBA,KAAI;AAAA,EAC/B;AAGA,WAAS,UAAUA,OAAM;AACvB,QAAIA,UAAS,MAAM,KAAK;AACtB,UAAI;AAAW,kBAAU;AACzB,qBAAe,CAAC;AAChB,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,gBAAY,aAAa,KAAK,OAAO,KAAK,KAAK,IAAI,CAAC;AACpD,YAAQ,MAAM,MAAM,WAAW;AAAA,MAC7B,YAAY;AAAA,MACZ,aAAa,UAAU;AAAA,MACvB,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,aAAaA,KAAI;AAAA,EAC1B;AAGA,WAAS,aAAaA,OAAM;AAC1B,QAAIA,UAAS,MAAM,KAAK;AACtB,mBAAa,QAAQ,KAAK,MAAM,SAAS,GAAG,IAAI;AAChD,qBAAe,CAAC;AAChB,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,QAAI,mBAAmBA,KAAI,GAAG;AAC5B,cAAQ,QAAQA,KAAI;AACpB,mBAAa,QAAQ,KAAK,MAAM,SAAS,CAAC;AAE1C,kBAAY;AACZ,WAAK,YAAY;AACjB,aAAOD;AAAA,IACT;AAEA,YAAQ,QAAQC,KAAI;AACpB,WAAO;AAAA,EACT;AAUA,WAAS,aAAa,OAAO,WAAW;AACtC,OAAO,WAAW,oDAAoD;AACtE,UAAM,SAAS,KAAK,YAAY,KAAK;AACrC,QAAI;AAAW,aAAO,KAAK,IAAI;AAC/B,UAAM,WAAW;AACjB,QAAI;AAAY,iBAAW,OAAO;AAClC,iBAAa;AACb,cAAU,WAAW,MAAM,KAAK;AAChC,cAAU,MAAM,MAAM;AAmCtB,QAAI,KAAK,OAAO,KAAK,MAAM,MAAM,IAAI,GAAG;AACtC,UAAIE,SAAQ,UAAU,OAAO;AAE7B,aAAOA,UAAS;AACd;AAAA;AAAA,UAEE,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,SAAS;AAAA,WAEzC,CAAC,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE;AAAA,UAE3B,UAAU,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,SAAS;AAAA,UAC1C;AAGA;AAAA,QACF;AAAA,MACF;AAIA,YAAM,mBAAmB,KAAK,OAAO;AACrC,UAAI,kBAAkB;AAEtB,UAAI;AAEJ,UAAID;AAGJ,aAAO,mBAAmB;AACxB,YACE,KAAK,OAAO,eAAe,EAAE,CAAC,MAAM,UACpC,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE,SAAS,MAAM,WAC/C;AACA,cAAI,MAAM;AACR,YAAAA,SAAQ,KAAK,OAAO,eAAe,EAAE,CAAC,EAAE;AACxC;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,SAAOA,QAAO,oCAAoC;AAElD,qBAAe,SAAS;AAGxB,MAAAC,SAAQ;AAER,aAAOA,SAAQ,KAAK,OAAO,QAAQ;AACjC,aAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,MAAM,EAAC,GAAGD,OAAK;AACrC,QAAAC;AAAA,MACF;AAGA;AAAA,QACE,KAAK;AAAA,QACL,kBAAkB;AAAA,QAClB;AAAA,QACA,KAAK,OAAO,MAAM,gBAAgB;AAAA,MACpC;AAGA,WAAK,OAAO,SAASA;AAAA,IACvB;AAAA,EACF;AAQA,WAAS,eAAe,MAAM;AAC5B,QAAIA,SAAQ,MAAM;AAGlB,WAAOA,WAAU,MAAM;AACrB,YAAM,QAAQ,MAAMA,MAAK;AACzB,WAAK,iBAAiB,MAAM,CAAC;AAC7B;AAAA,QACE,MAAM,CAAC,EAAE;AAAA,QACT;AAAA,MACF;AACA,YAAM,CAAC,EAAE,KAAK,KAAK,MAAM,OAAO;AAAA,IAClC;AAEA,UAAM,SAAS;AAAA,EACjB;AAEA,WAAS,YAAY;AACnB;AAAA,MACE,KAAK;AAAA,MACL;AAAA,IACF;AACA,OAAO,WAAW,oDAAoD;AACtE,cAAU,MAAM,CAAC,MAAM,GAAG,CAAC;AAC3B,iBAAa;AACb,gBAAY;AACZ,SAAK,eAAe,aAAa;AAAA,EACnC;AACF;AAQA,SAAS,kBAAkB,SAASC,KAAI,KAAK;AAE3C;AAAA,IACE,KAAK,OAAO,WAAW,QAAQ;AAAA,IAC/B;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,QAAQ,KAAK,OAAO,WAAW,UAAUA,KAAI,GAAG;AAAA,IACxD,MAAM;AAAA,IACN,KAAK,OAAO,WAAW,QAAQ,KAAK,SAAS,cAAc,IACvD,SACA,UAAU;AAAA,EAChB;AACF;;;AC5aO,IAAM,OAAO,EAAC,UAAU,eAAc;AAQ7C,SAAS,eAAe,SAAS;AAC/B,QAAM,OAAO;AACb,QAAM,UAAU,QAAQ;AAAA;AAAA,IAEtB;AAAA,IACA;AAAA;AAAA,IAEA,QAAQ;AAAA,MACN,KAAK,OAAO,WAAW;AAAA,MACvB;AAAA,MACA;AAAA,QACE;AAAA,QACA,QAAQ;AAAA,UACN,KAAK,OAAO,WAAW;AAAA,UACvB;AAAA,UACA,QAAQ,QAAQ,SAAS,cAAc;AAAA,QACzC;AAAA,QACA,MAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAGP,WAAS,cAAcC,OAAM;AAC3B;AAAA,MACEA,UAAS,MAAM,OAAO,mBAAmBA,KAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAIA,UAAS,MAAM,KAAK;AACtB,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,eAAe;AACnC,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,MAAM,eAAe;AAClC,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AAGA,WAAS,eAAeA,OAAM;AAC5B;AAAA,MACEA,UAAS,MAAM,OAAO,mBAAmBA,KAAI;AAAA,MAC7C;AAAA,IACF;AAEA,QAAIA,UAAS,MAAM,KAAK;AACtB,cAAQ,QAAQA,KAAI;AACpB;AAAA,IACF;AAEA,YAAQ,MAAM,MAAM,UAAU;AAC9B,YAAQ,QAAQA,KAAI;AACpB,YAAQ,KAAK,MAAM,UAAU;AAC7B,SAAK,mBAAmB;AACxB,WAAO;AAAA,EACT;AACF;;;ACvEO,IAAM,WAAW,EAAC,YAAY,eAAe,EAAC;AAC9C,IAAM,SAAS,kBAAkB,QAAQ;AACzC,IAAMC,QAAO,kBAAkB,MAAM;AAQ5C,SAAS,kBAAkB,OAAO;AAChC,SAAO;AAAA,IACL,YAAY;AAAA,MACV,UAAU,SAAS,yBAAyB;AAAA,IAC9C;AAAA,IACA,UAAU;AAAA,EACZ;AAOA,WAAS,eAAe,SAAS;AAC/B,UAAM,OAAO;AACb,UAAM,aAAa,KAAK,OAAO,WAAW,KAAK;AAC/C,UAAMA,QAAO,QAAQ,QAAQ,YAAYC,QAAO,OAAO;AAEvD,WAAOA;AAGP,aAASA,OAAMC,OAAM;AACnB,aAAO,QAAQA,KAAI,IAAIF,MAAKE,KAAI,IAAI,QAAQA,KAAI;AAAA,IAClD;AAGA,aAAS,QAAQA,OAAM;AACrB,UAAIA,UAAS,MAAM,KAAK;AACtB,gBAAQ,QAAQA,KAAI;AACpB;AAAA,MACF;AAEA,cAAQ,MAAM,MAAM,IAAI;AACxB,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAGA,aAAS,KAAKA,OAAM;AAClB,UAAI,QAAQA,KAAI,GAAG;AACjB,gBAAQ,KAAK,MAAM,IAAI;AACvB,eAAOF,MAAKE,KAAI;AAAA,MAClB;AAGA,cAAQ,QAAQA,KAAI;AACpB,aAAO;AAAA,IACT;AAQA,aAAS,QAAQA,OAAM;AACrB,UAAIA,UAAS,MAAM,KAAK;AACtB,eAAO;AAAA,MACT;AAEA,YAAMC,QAAO,WAAWD,KAAI;AAC5B,UAAIE,SAAQ;AAEZ,UAAID,OAAM;AAER,WAAO,MAAM,QAAQA,KAAI,GAAG,yCAAyC;AAErE,eAAO,EAAEC,SAAQD,MAAK,QAAQ;AAC5B,gBAAM,OAAOA,MAAKC,MAAK;AACvB,cAAI,CAAC,KAAK,YAAY,KAAK,SAAS,KAAK,MAAM,KAAK,QAAQ,GAAG;AAC7D,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAQA,SAAS,eAAe,eAAe;AACrC,SAAO;AAGP,WAAS,eAAe,QAAQ,SAAS;AACvC,QAAIA,SAAQ;AAEZ,QAAI;AAIJ,WAAO,EAAEA,UAAS,OAAO,QAAQ;AAC/B,UAAI,UAAU,QAAW;AACvB,YAAI,OAAOA,MAAK,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,MAAM;AACzD,kBAAQA;AACR,UAAAA;AAAA,QACF;AAAA,MACF,WAAW,CAAC,OAAOA,MAAK,KAAK,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,MAAM;AAEjE,YAAIA,WAAU,QAAQ,GAAG;AACvB,iBAAO,KAAK,EAAE,CAAC,EAAE,MAAM,OAAOA,SAAQ,CAAC,EAAE,CAAC,EAAE;AAC5C,iBAAO,OAAO,QAAQ,GAAGA,SAAQ,QAAQ,CAAC;AAC1C,UAAAA,SAAQ,QAAQ;AAAA,QAClB;AAEA,gBAAQ;AAAA,MACV;AAAA,IACF;AAEA,WAAO,gBAAgB,cAAc,QAAQ,OAAO,IAAI;AAAA,EAC1D;AACF;AAaA,SAAS,uBAAuB,QAAQ,SAAS;AAC/C,MAAI,aAAa;AAEjB,SAAO,EAAE,cAAc,OAAO,QAAQ;AACpC,SACG,eAAe,OAAO,UACrB,OAAO,UAAU,EAAE,CAAC,EAAE,SAAS,MAAM,eACvC,OAAO,aAAa,CAAC,EAAE,CAAC,EAAE,SAAS,MAAM,MACzC;AACA,YAAM,OAAO,OAAO,aAAa,CAAC,EAAE,CAAC;AACrC,YAAM,SAAS,QAAQ,YAAY,IAAI;AACvC,UAAIA,SAAQ,OAAO;AACnB,UAAI,cAAc;AAClB,UAAI,OAAO;AAEX,UAAI;AAEJ,aAAOA,UAAS;AACd,cAAM,QAAQ,OAAOA,MAAK;AAE1B,YAAI,OAAO,UAAU,UAAU;AAC7B,wBAAc,MAAM;AAEpB,iBAAO,MAAM,WAAW,cAAc,CAAC,MAAM,MAAM,OAAO;AACxD;AACA;AAAA,UACF;AAEA,cAAI;AAAa;AACjB,wBAAc;AAAA,QAChB,WAES,UAAU,MAAM,eAAe;AACtC,iBAAO;AACP;AAAA,QACF,WAAW,UAAU,MAAM,cAAc;AAAA,QAEzC,OAAO;AAEL,UAAAA;AACA;AAAA,QACF;AAAA,MACF;AAGA,UAAI,QAAQ,4BAA4B,eAAe,OAAO,QAAQ;AACpE,eAAO;AAAA,MACT;AAEA,UAAI,MAAM;AACR,cAAM,QAAQ;AAAA,UACZ,MACE,eAAe,OAAO,UACtB,QACA,OAAO,UAAU,yBACb,MAAM,aACN,MAAM;AAAA,UACZ,OAAO;AAAA,YACL,cAAcA,SACV,cACA,KAAK,MAAM,eAAe;AAAA,YAC9B,QAAQ,KAAK,MAAM,SAASA;AAAA,YAC5B,MAAM,KAAK,IAAI;AAAA,YACf,QAAQ,KAAK,IAAI,SAAS;AAAA,YAC1B,QAAQ,KAAK,IAAI,SAAS;AAAA,UAC5B;AAAA,UACA,KAAK,EAAC,GAAG,KAAK,IAAG;AAAA,QACnB;AAEA,aAAK,MAAM,EAAC,GAAG,MAAM,MAAK;AAE1B,YAAI,KAAK,MAAM,WAAW,KAAK,IAAI,QAAQ;AACzC,iBAAO,OAAO,MAAM,KAAK;AAAA,QAC3B,OAAO;AACL,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA,CAAC,SAAS,OAAO,OAAO;AAAA,YACxB,CAAC,QAAQ,OAAO,OAAO;AAAA,UACzB;AACA,wBAAc;AAAA,QAChB;AAAA,MACF;AAEA;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;;;ACnPA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA,EAAA,YAAAC;AAAA,EAAA;AAAA;AAAA,gBAAAC;AAAA,EAAA,YAAAC;AAAA;AA8BO,IAAMC,YAAW;AAAA,EACtB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,IAAI,GAAG;AAAA,EACd,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,MAAM,GAAG;AAAA,EAChB,CAAC,MAAM,WAAW,GAAG;AACvB;AAGO,IAAM,iBAAiB;AAAA,EAC5B,CAAC,MAAM,iBAAiB,GAAG;AAC7B;AAGO,IAAM,cAAc;AAAA,EACzB,CAAC,MAAM,aAAa,GAAG;AAAA,EACvB,CAAC,MAAM,YAAY,GAAG;AAAA,EACtB,CAAC,MAAM,KAAK,GAAG;AACjB;AAGO,IAAMC,QAAO;AAAA,EAClB,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,IAAI,GAAG,CAAC,iBAAiB,aAAa;AAAA,EAC7C,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,WAAW,GAAG;AAAA,EACrB,CAAC,MAAM,KAAK,GAAG;AACjB;AAGO,IAAMC,UAAS;AAAA,EACpB,CAAC,MAAM,SAAS,GAAG;AAAA,EACnB,CAAC,MAAM,SAAS,GAAG;AACrB;AAGO,IAAMC,QAAO;AAAA,EAClB,CAAC,MAAM,cAAc,GAAG;AAAA,EACxB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,sBAAsB,GAAG;AAAA,EAChC,CAAC,MAAM,eAAe,GAAG;AAAA,EACzB,CAAC,MAAM,SAAS,GAAG;AAAA,EACnB,CAAC,MAAM,QAAQ,GAAG;AAAA,EAClB,CAAC,MAAM,QAAQ,GAAG,CAAC,UAAU,QAAQ;AAAA,EACrC,CAAC,MAAM,iBAAiB,GAAG;AAAA,EAC3B,CAAC,MAAM,SAAS,GAAG,CAAC,iBAAiB,eAAe;AAAA,EACpD,CAAC,MAAM,kBAAkB,GAAG;AAAA,EAC5B,CAAC,MAAM,UAAU,GAAG;AAAA,EACpB,CAAC,MAAM,WAAW,GAAG;AACvB;AAGO,IAAM,aAAa,EAAC,MAAM,CAAC,WAAW,QAAW,EAAC;AAGlD,IAAM,mBAAmB,EAAC,MAAM,CAAC,MAAM,UAAU,MAAM,UAAU,EAAC;AAGlE,IAAM,UAAU,EAAC,MAAM,CAAC,EAAC;;;AC7DhC,mBAAwB;AAOxB,IAAM,YAAQ,aAAAC,SAAY,WAAW;AAoB9B,SAAS,gBAAgB,QAAQ,YAAY,MAAM;AAExD,MAAIC,SAAQ;AAAA,IACV,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,MAAO,QAAQ,KAAK,QAAS;AAAA,IAC7B,QAAS,QAAQ,KAAK,UAAW;AAAA,IACjC,QAAS,QAAQ,KAAK,UAAW;AAAA,EACnC;AAEA,QAAM,cAAc,CAAC;AAErB,QAAM,uBAAuB,CAAC;AAE9B,MAAI,SAAS,CAAC;AAEd,MAAI,QAAQ,CAAC;AAEb,MAAI,WAAW;AAOf,QAAM,UAAU;AAAA,IACd,SAAS,iBAAiB,qBAAqB;AAAA,IAC/C,OAAO,iBAAiB,iBAAiB;AAAA,IACzC;AAAA,IACA;AAAA,IACA;AAAA,IACA,WAAW,iBAAiB,mBAAmB,EAAC,WAAW,KAAI,CAAC;AAAA,EAClE;AAOA,QAAM,UAAU;AAAA,IACd,MAAM,MAAM;AAAA,IACZ,gBAAgB,CAAC;AAAA,IACjB;AAAA,IACA,QAAQ,CAAC;AAAA,IACT;AAAA,IACA;AAAA,IACA,UAAU,MAAM;AAAA,IAChB;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAOA,MAAI,QAAQ,WAAW,SAAS,KAAK,SAAS,OAAO;AAOrD,MAAI;AAEJ,MAAI,WAAW,YAAY;AACzB,yBAAqB,KAAK,UAAU;AAAA,EACtC;AAEA,SAAO;AAGP,WAAS,MAAM,OAAO;AACpB,aAAS,KAAK,QAAQ,KAAK;AAE3B,SAAK;AAGL,QAAI,OAAO,OAAO,SAAS,CAAC,MAAM,MAAM,KAAK;AAC3C,aAAO,CAAC;AAAA,IACV;AAEA,cAAU,YAAY,CAAC;AAGvB,YAAQ,SAAS,WAAW,sBAAsB,QAAQ,QAAQ,OAAO;AAEzE,WAAO,QAAQ;AAAA,EACjB;AAOA,WAAS,eAAe,OAAO,YAAY;AACzC,WAAO,gBAAgB,YAAY,KAAK,GAAG,UAAU;AAAA,EACvD;AAGA,WAAS,YAAY,OAAO;AAC1B,WAAO,YAAY,QAAQ,KAAK;AAAA,EAClC;AAGA,WAAS,MAAM;AAEb,UAAM,EAAC,cAAc,QAAQ,MAAM,QAAQ,OAAM,IAAIA;AACrD,WAAO,EAAC,cAAc,QAAQ,MAAM,QAAQ,OAAM;AAAA,EACpD;AAGA,WAAS,WAAW,OAAO;AACzB,gBAAY,MAAM,IAAI,IAAI,MAAM;AAChC,4BAAwB;AACxB,UAAM,+BAA+BA,MAAK;AAAA,EAC5C;AAiBA,WAAS,OAAO;AAEd,QAAI;AAEJ,WAAOA,OAAM,SAAS,OAAO,QAAQ;AACnC,YAAM,QAAQ,OAAOA,OAAM,MAAM;AAGjC,UAAI,OAAO,UAAU,UAAU;AAC7B,qBAAaA,OAAM;AAEnB,YAAIA,OAAM,eAAe,GAAG;AAC1B,UAAAA,OAAM,eAAe;AAAA,QACvB;AAEA,eACEA,OAAM,WAAW,cACjBA,OAAM,eAAe,MAAM,QAC3B;AACA,aAAG,MAAM,WAAWA,OAAM,YAAY,CAAC;AAAA,QACzC;AAAA,MACF,OAAO;AACL,WAAG,KAAK;AAAA,MACV;AAAA,IACF;AAAA,EACF;AAUA,WAAS,GAAGC,OAAM;AAChB,OAAO,aAAa,MAAM,mCAAmC;AAC7D,eAAW;AACX,UAAM,4BAA4BA,OAAM,SAAS,MAAM,IAAI;AAC3D,mBAAeA;AACf,OAAO,OAAO,UAAU,YAAY,gBAAgB;AACpD,YAAQ,MAAMA,KAAI;AAAA,EACpB;AAGA,WAAS,QAAQA,OAAM;AACrB,OAAOA,UAAS,cAAc,4CAA4C;AAE1E,UAAM,iBAAiBA,KAAI;AAE3B;AAAA,MACE,aAAa;AAAA,MACb;AAAA,IACF;AACA;AAAA,MACEA,UAAS,OACL,QAAQ,OAAO,WAAW,KACxB,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM,SACnD,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,MACrD;AAAA,IACF;AAEA,QAAI,mBAAmBA,KAAI,GAAG;AAC5B,MAAAD,OAAM;AACN,MAAAA,OAAM,SAAS;AACf,MAAAA,OAAM,UAAUC,UAAS,MAAM,yBAAyB,IAAI;AAC5D,8BAAwB;AACxB,YAAM,6BAA6BD,MAAK;AAAA,IAC1C,WAAWC,UAAS,MAAM,cAAc;AACtC,MAAAD,OAAM;AACN,MAAAA,OAAM;AAAA,IACR;AAGA,QAAIA,OAAM,eAAe,GAAG;AAC1B,MAAAA,OAAM;AAAA,IACR,OAAO;AACL,MAAAA,OAAM;AAGN,UACEA,OAAM;AAAA;AAAA;AAAA,MAGiB,OAAOA,OAAM,MAAM,EAAG,QAC7C;AACA,QAAAA,OAAM,eAAe;AACrB,QAAAA,OAAM;AAAA,MACR;AAAA,IACF;AAGA,YAAQ,WAAWC;AAGnB,eAAW;AAAA,EACb;AAGA,WAAS,MAAM,MAAM,QAAQ;AAG3B,UAAM,QAAQ,UAAU,CAAC;AACzB,UAAM,OAAO;AACb,UAAM,QAAQ,IAAI;AAElB,OAAO,OAAO,SAAS,UAAU,sBAAsB;AACvD,OAAO,KAAK,SAAS,GAAG,2BAA2B;AACnD,UAAM,eAAe,IAAI;AAEzB,YAAQ,OAAO,KAAK,CAAC,SAAS,OAAO,OAAO,CAAC;AAE7C,UAAM,KAAK,KAAK;AAEhB,WAAO;AAAA,EACT;AAGA,WAAS,KAAK,MAAM;AAClB,OAAO,OAAO,SAAS,UAAU,sBAAsB;AACvD,OAAO,KAAK,SAAS,GAAG,2BAA2B;AAEnD,UAAM,QAAQ,MAAM,IAAI;AACxB,OAAO,OAAO,8BAA8B;AAC5C,UAAM,MAAM,IAAI;AAEhB,OAAO,SAAS,MAAM,MAAM,4CAA4C;AAExE;AAAA,MACE,EACE,MAAM,MAAM,WAAW,MAAM,IAAI,UACjC,MAAM,MAAM,iBAAiB,MAAM,IAAI;AAAA,MAEzC,gCAAgC,OAAO;AAAA,IACzC;AAEA,UAAM,cAAc,MAAM,IAAI;AAC9B,YAAQ,OAAO,KAAK,CAAC,QAAQ,OAAO,OAAO,CAAC;AAE5C,WAAO;AAAA,EACT;AAOA,WAAS,sBAAsB,WAAW,MAAM;AAC9C,cAAU,WAAW,KAAK,IAAI;AAAA,EAChC;AAOA,WAAS,kBAAkB,GAAG,MAAM;AAClC,SAAK,QAAQ;AAAA,EACf;AAUA,WAAS,iBAAiB,UAAU,QAAQ;AAC1C,WAAO;AAeP,aAAS,KAAK,YAAY,aAAa,YAAY;AAEjD,UAAI;AAEJ,UAAI;AAEJ,UAAI;AAEJ,UAAI;AAEJ,aAAO,MAAM,QAAQ,UAAU;AAAA;AAAA,QAE3B,uBAAuB,UAAU;AAAA,UACjC,cAAc;AAAA;AAAA,QAEZ,uBAAuB;AAAA;AAAA,UAA2B;AAAA,QAAW,CAAC;AAAA,UAC9D,sBAAsB,UAAU;AAUtC,eAAS,sBAAsB,KAAK;AAClC,eAAOC;AAGP,iBAASA,OAAMD,OAAM;AACnB,gBAAM,OAAOA,UAAS,QAAQ,IAAIA,KAAI;AACtC,gBAAM,MAAMA,UAAS,QAAQ,IAAI;AACjC,gBAAME,QAAO;AAAA;AAAA;AAAA,YAGX,GAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,OAAO,CAAC,IAAI,IAAI,CAAC;AAAA,YAClD,GAAI,MAAM,QAAQ,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC;AAAA,UAChD;AAEA,iBAAO,uBAAuBA,KAAI,EAAEF,KAAI;AAAA,QAC1C;AAAA,MACF;AAUA,eAAS,uBAAuBE,OAAM;AACpC,2BAAmBA;AACnB,yBAAiB;AAEjB,YAAIA,MAAK,WAAW,GAAG;AACrB,aAAO,YAAY,mCAAmC;AACtD,iBAAO;AAAA,QACT;AAEA,eAAO,gBAAgBA,MAAK,cAAc,CAAC;AAAA,MAC7C;AAUA,eAAS,gBAAgB,WAAW;AAClC,eAAOD;AAGP,iBAASA,OAAMD,OAAM;AAKnB,iBAAO,MAAM;AACb,6BAAmB;AAEnB,cAAI,CAAC,UAAU,SAAS;AACtB,oBAAQ,mBAAmB;AAAA,UAC7B;AAGA;AAAA,YACE,QAAQ,OAAO,WAAW,QAAQ;AAAA,YAClC;AAAA,UACF;AAEA,cACE,UAAU,QACV,QAAQ,OAAO,WAAW,QAAQ,KAAK,SAAS,UAAU,IAAI,GAC9D;AACA,mBAAO,IAAIA,KAAI;AAAA,UACjB;AAEA,iBAAO,UAAU,SAAS;AAAA;AAAA;AAAA;AAAA,YAIxB,SAAS,OAAO,OAAO,OAAO,OAAO,OAAO,GAAG,MAAM,IAAI;AAAA,YACzD;AAAA,YACAG;AAAA,YACA;AAAA,UACF,EAAEH,KAAI;AAAA,QACR;AAAA,MACF;AAGA,eAASG,IAAGH,OAAM;AAChB,WAAOA,UAAS,cAAc,eAAe;AAC7C,mBAAW;AACX,iBAAS,kBAAkB,IAAI;AAC/B,eAAO;AAAA,MACT;AAGA,eAAS,IAAIA,OAAM;AACjB,WAAOA,UAAS,cAAc,eAAe;AAC7C,mBAAW;AACX,aAAK,QAAQ;AAEb,YAAI,EAAE,iBAAiB,iBAAiB,QAAQ;AAC9C,iBAAO,gBAAgB,iBAAiB,cAAc,CAAC;AAAA,QACzD;AAEA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAUA,WAAS,UAAU,WAAWI,OAAM;AAClC,QAAI,UAAU,cAAc,CAAC,qBAAqB,SAAS,SAAS,GAAG;AACrE,2BAAqB,KAAK,SAAS;AAAA,IACrC;AAEA,QAAI,UAAU,SAAS;AACrB;AAAA,QACE,QAAQ;AAAA,QACRA;AAAA,QACA,QAAQ,OAAO,SAASA;AAAA,QACxB,UAAU,QAAQ,QAAQ,OAAO,MAAMA,KAAI,GAAG,OAAO;AAAA,MACvD;AAAA,IACF;AAEA,QAAI,UAAU,WAAW;AACvB,cAAQ,SAAS,UAAU,UAAU,QAAQ,QAAQ,OAAO;AAAA,IAC9D;AAEA;AAAA,MACE,UAAU,WACR,QAAQ,OAAO,WAAW,KAC1B,QAAQ,OAAO,QAAQ,OAAO,SAAS,CAAC,EAAE,CAAC,MAAM;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AAQA,WAAS,QAAQ;AACf,UAAM,aAAa,IAAI;AACvB,UAAM,gBAAgB,QAAQ;AAC9B,UAAM,wBAAwB,QAAQ;AACtC,UAAM,mBAAmB,QAAQ,OAAO;AACxC,UAAM,aAAa,MAAM,KAAK,KAAK;AAEnC,WAAO,EAAC,MAAM,kBAAkB,QAAO;AAQvC,aAAS,UAAU;AACjB,MAAAL,SAAQ;AACR,cAAQ,WAAW;AACnB,cAAQ,mBAAmB;AAC3B,cAAQ,OAAO,SAAS;AACxB,cAAQ;AACR,8BAAwB;AACxB,YAAM,2BAA2BA,MAAK;AAAA,IACxC;AAAA,EACF;AASA,WAAS,0BAA0B;AACjC,QAAIA,OAAM,QAAQ,eAAeA,OAAM,SAAS,GAAG;AACjD,MAAAA,OAAM,SAAS,YAAYA,OAAM,IAAI;AACrC,MAAAA,OAAM,UAAU,YAAYA,OAAM,IAAI,IAAI;AAAA,IAC5C;AAAA,EACF;AACF;AAYA,SAAS,YAAY,QAAQ,OAAO;AAClC,QAAM,aAAa,MAAM,MAAM;AAC/B,QAAM,mBAAmB,MAAM,MAAM;AACrC,QAAM,WAAW,MAAM,IAAI;AAC3B,QAAM,iBAAiB,MAAM,IAAI;AAEjC,MAAI;AAEJ,MAAI,eAAe,UAAU;AAC3B,OAAO,iBAAiB,IAAI,wCAAwC;AACpE,OAAO,mBAAmB,IAAI,0CAA0C;AAExE,WAAO,CAAC,OAAO,UAAU,EAAE,MAAM,kBAAkB,cAAc,CAAC;AAAA,EACpE,OAAO;AACL,WAAO,OAAO,MAAM,YAAY,QAAQ;AAExC,QAAI,mBAAmB,IAAI;AACzB,YAAM,OAAO,KAAK,CAAC;AACnB,UAAI,OAAO,SAAS,UAAU;AAC5B,aAAK,CAAC,IAAI,KAAK,MAAM,gBAAgB;AAAA,MAEvC,OAAO;AACL,WAAO,qBAAqB,GAAG,uCAAuC;AACtE,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAEA,QAAI,iBAAiB,GAAG;AAEtB,WAAK,KAAK,OAAO,QAAQ,EAAE,MAAM,GAAG,cAAc,CAAC;AAAA,IACrD;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,gBAAgB,QAAQ,YAAY;AAC3C,MAAIM,SAAQ;AAEZ,QAAM,SAAS,CAAC;AAEhB,MAAI;AAEJ,SAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,UAAM,QAAQ,OAAOA,MAAK;AAE1B,QAAI;AAEJ,QAAI,OAAO,UAAU,UAAU;AAC7B,cAAQ;AAAA,IACV;AACE,cAAQ,OAAO;AAAA,QACb,KAAK,MAAM,gBAAgB;AACzB,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,UAAU;AACnB,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,wBAAwB;AACjC,kBAAQ,OAAO,KAAK,OAAO;AAE3B;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,eAAe;AACxB,kBAAQ,aAAa,OAAO,QAAQ,OAAO;AAE3C;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,cAAc;AACvB,cAAI,CAAC,cAAc;AAAO;AAC1B,kBAAQ,OAAO;AAEf;AAAA,QACF;AAAA,QAEA,SAAS;AACP,aAAO,OAAO,UAAU,UAAU,iBAAiB;AAEnD,kBAAQ,OAAO,aAAa,KAAK;AAAA,QACnC;AAAA,MACF;AAEF,YAAQ,UAAU,MAAM;AACxB,WAAO,KAAK,KAAK;AAAA,EACnB;AAEA,SAAO,OAAO,KAAK,EAAE;AACvB;;;ACprBO,SAAS,MAAM,SAAS;AAC7B,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM;AAAA;AAAA,IACJ,kBAAkB,CAAC,oBAAmB,GAAI,SAAS,cAAc,CAAC,CAAE,CAAC;AAAA;AAIvE,QAAM,SAAS;AAAA,IACb;AAAA,IACA,SAAS,OAAOC,QAAO;AAAA,IACvB,SAAS,CAAC;AAAA,IACV,UAAU,OAAOC,SAAQ;AAAA,IACzB,MAAM,OAAO,IAAI;AAAA,IACjB,MAAM,CAAC;AAAA,IACP,QAAQ,OAAO,MAAM;AAAA,IACrB,MAAM,OAAOC,KAAI;AAAA,EACnB;AAEA,SAAO;AAQP,WAAS,OAAO,SAAS;AACvB,WAAO;AAEP,aAAS,QAAQ,MAAM;AACrB,aAAO,gBAAgB,QAAQ,SAAS,IAAI;AAAA,IAC9C;AAAA,EACF;AACF;;;AC7CO,SAAS,YAAY,QAAQ;AAClC,SAAO,CAAC,YAAY,MAAM,GAAG;AAAA,EAE7B;AAEA,SAAO;AACT;;;ACCA,IAAM,SAAS;AAMR,SAAS,aAAa;AAC3B,MAAI,SAAS;AACb,MAAI,SAAS;AAEb,MAAIC,SAAQ;AAEZ,MAAI;AAEJ,SAAO;AAIP,WAAS,aAAa,OAAO,UAAU,KAAK;AAE1C,UAAM,SAAS,CAAC;AAEhB,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAIC;AAEJ,YACE,UACC,OAAO,UAAU,WACd,MAAM,SAAS,IACf,IAAI,YAAY,YAAY,MAAS,EAAE,OAAO,KAAK;AAEzD,oBAAgB;AAChB,aAAS;AAET,QAAID,QAAO;AAET,UAAI,MAAM,WAAW,CAAC,MAAM,MAAM,iBAAiB;AACjD;AAAA,MACF;AAEA,MAAAA,SAAQ;AAAA,IACV;AAEA,WAAO,gBAAgB,MAAM,QAAQ;AACnC,aAAO,YAAY;AACnB,cAAQ,OAAO,KAAK,KAAK;AACzB,oBACE,SAAS,MAAM,UAAU,SAAY,MAAM,QAAQ,MAAM;AAC3D,MAAAC,QAAO,MAAM,WAAW,WAAW;AAEnC,UAAI,CAAC,OAAO;AACV,iBAAS,MAAM,MAAM,aAAa;AAClC;AAAA,MACF;AAEA,UACEA,UAAS,MAAM,MACf,kBAAkB,eAClB,kBACA;AACA,eAAO,KAAK,MAAM,sBAAsB;AACxC,2BAAmB;AAAA,MACrB,OAAO;AACL,YAAI,kBAAkB;AACpB,iBAAO,KAAK,MAAM,cAAc;AAChC,6BAAmB;AAAA,QACrB;AAEA,YAAI,gBAAgB,aAAa;AAC/B,iBAAO,KAAK,MAAM,MAAM,eAAe,WAAW,CAAC;AACnD,oBAAU,cAAc;AAAA,QAC1B;AAEA,gBAAQA,OAAM;AAAA,UACZ,KAAK,MAAM,KAAK;AACd,mBAAO,KAAK,MAAM,oBAAoB;AACtC;AAEA;AAAA,UACF;AAAA,UAEA,KAAK,MAAM,IAAI;AACb,mBAAO,KAAK,KAAK,SAAS,UAAU,OAAO,IAAI,UAAU;AACzD,mBAAO,KAAK,MAAM,aAAa;AAC/B,mBAAO,WAAW;AAAM,qBAAO,KAAK,MAAM,YAAY;AAEtD;AAAA,UACF;AAAA,UAEA,KAAK,MAAM,IAAI;AACb,mBAAO,KAAK,MAAM,QAAQ;AAC1B,qBAAS;AAET;AAAA,UACF;AAAA,UAEA,SAAS;AACP,+BAAmB;AACnB,qBAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAEA,sBAAgB,cAAc;AAAA,IAChC;AAEA,QAAI,KAAK;AACP,UAAI;AAAkB,eAAO,KAAK,MAAM,cAAc;AACtD,UAAI;AAAQ,eAAO,KAAK,MAAM;AAC9B,aAAO,KAAK,MAAM,GAAG;AAAA,IACvB;AAEA,WAAO;AAAA,EACT;AACF;;;ACzFA,IAAMC,OAAM,CAAC,EAAE;AAyBR,SAAS,aAAa,OAAO,UAAU,SAAS;AACrD,MAAI,OAAO,aAAa,UAAU;AAChC,cAAU;AACV,eAAW;AAAA,EACb;AAEA,SAAO,SAAS,OAAO;AAAA,IACrB;AAAA,MACE,MAAM,OAAO,EACV,SAAS,EACT,MAAM,WAAW,EAAE,OAAO,UAAU,IAAI,CAAC;AAAA,IAC9C;AAAA,EACF;AACF;AAOA,SAAS,SAAS,SAAS;AAEzB,QAAM,SAAS;AAAA,IACb,YAAY,CAAC;AAAA,IACb,gBAAgB,CAAC,YAAY,YAAY,WAAW,aAAa,QAAQ;AAAA,IACzE,OAAO;AAAA,MACL,UAAU,OAAOC,KAAI;AAAA,MACrB,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,YAAY,OAAOC,QAAO;AAAA,MAC1B,YAAY,OAAOC,WAAU;AAAA,MAC7B,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,YAAY,OAAO,QAAQ;AAAA,MAC3B,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,cAAc,OAAO,UAAU,MAAM;AAAA,MACrC,UAAU,OAAOC,WAAU,MAAM;AAAA,MACjC,cAAc;AAAA,MACd,MAAM;AAAA,MACN,eAAe;AAAA,MACf,YAAY,OAAOC,WAAU;AAAA,MAC7B,6BAA6B;AAAA,MAC7B,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,UAAU,OAAOC,SAAQ;AAAA,MACzB,iBAAiB,OAAOC,UAAS;AAAA,MACjC,mBAAmB,OAAOA,UAAS;AAAA,MACnC,UAAU,OAAOC,OAAM,MAAM;AAAA,MAC7B,cAAc;AAAA,MACd,UAAU,OAAOA,OAAM,MAAM;AAAA,MAC7B,cAAc;AAAA,MACd,OAAO,OAAOC,MAAK;AAAA,MACnB,OAAO;AAAA,MACP,MAAM,OAAOR,KAAI;AAAA,MACjB,UAAU,OAAOS,SAAQ;AAAA,MACzB,eAAe;AAAA,MACf,aAAa,OAAOC,OAAM,kBAAkB;AAAA,MAC5C,eAAe,OAAOA,KAAI;AAAA,MAC1B,WAAW,OAAOC,UAAS;AAAA,MAC3B,WAAW;AAAA,MACX,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,qBAAqB;AAAA,MACrB,eAAe,OAAOV,QAAO;AAAA,MAC7B,QAAQ,OAAOW,OAAM;AAAA,MACrB,eAAe,OAAOC,cAAa;AAAA,IACrC;AAAA,IACA,MAAM;AAAA,MACJ,YAAY,OAAO;AAAA,MACnB,oBAAoB;AAAA,MACpB,UAAU,OAAO;AAAA,MACjB,eAAe;AAAA,MACf,kBAAkB;AAAA,MAClB,YAAY,OAAO;AAAA,MACnB,sBAAsB;AAAA,MACtB,qCAAqC;AAAA,MACrC,iCAAiC;AAAA,MACjC,yBAAyB;AAAA,MACzB,oBAAoB;AAAA,MACpB,YAAY,OAAO,gBAAgB;AAAA,MACnC,iBAAiB;AAAA,MACjB,qBAAqB;AAAA,MACrB,qBAAqB;AAAA,MACrB,eAAe;AAAA,MACf,cAAc,OAAO,kBAAkB;AAAA,MACvC,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,MAAM;AAAA,MACN,YAAY,OAAO;AAAA,MACnB,6BAA6B;AAAA,MAC7B,uBAAuB;AAAA,MACvB,uBAAuB;AAAA,MACvB,UAAU,OAAO;AAAA,MACjB,iBAAiB,OAAO,eAAe;AAAA,MACvC,mBAAmB,OAAO,eAAe;AAAA,MACzC,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,UAAU,OAAO,cAAc;AAAA,MAC/B,cAAc;AAAA,MACd,OAAO,OAAO,WAAW;AAAA,MACzB,OAAO;AAAA,MACP,WAAW;AAAA,MACX,YAAY;AAAA,MACZ,MAAM,OAAO,UAAU;AAAA,MACvB,UAAU,OAAO;AAAA,MACjB,aAAa,OAAO;AAAA,MACpB,eAAe,OAAO;AAAA,MACtB,WAAW,OAAO;AAAA,MAClB,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,qBAAqB;AAAA,MACrB,UAAU;AAAA,MACV,eAAe,OAAO,mBAAmB;AAAA,MACzC,2BAA2B;AAAA,MAC3B,mBAAmB;AAAA,MACnB,QAAQ,OAAO;AAAA,MACf,eAAe,OAAO;AAAA,IACxB;AAAA,EACF;AAEA,YAAU,SAAS,WAAW,CAAC,GAAG,mBAAmB,CAAC,CAAC;AAGvD,QAAM,OAAO,CAAC;AAEd,SAAOC;AAUP,WAASA,SAAQ,QAAQ;AAEvB,QAAI,OAAO,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAEtC,UAAM,UAAU;AAAA,MACd,OAAO,CAAC,IAAI;AAAA,MACZ,YAAY,CAAC;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,UAAM,YAAY,CAAC;AACnB,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAG9B,UACE,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,eAChC,OAAOA,MAAK,EAAE,CAAC,EAAE,SAAS,MAAM,eAChC;AACA,YAAI,OAAOA,MAAK,EAAE,CAAC,MAAM,SAAS;AAChC,oBAAU,KAAKA,MAAK;AAAA,QACtB,OAAO;AACL,gBAAM,OAAO,UAAU,IAAI;AAC3B,aAAO,OAAO,SAAS,UAAU,0BAA0B;AAC3D,UAAAA,SAAQ,YAAY,QAAQ,MAAMA,MAAK;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAEA,IAAAA,SAAQ;AAER,WAAO,EAAEA,SAAQ,OAAO,QAAQ;AAC9B,YAAM,UAAU,OAAO,OAAOA,MAAK,EAAE,CAAC,CAAC;AAEvC,UAAIhB,KAAI,KAAK,SAAS,OAAOgB,MAAK,EAAE,CAAC,EAAE,IAAI,GAAG;AAC5C,gBAAQ,OAAOA,MAAK,EAAE,CAAC,EAAE,IAAI,EAAE;AAAA,UAC7B,OAAO;AAAA,YACL,EAAC,gBAAgB,OAAOA,MAAK,EAAE,CAAC,EAAE,eAAc;AAAA,YAChD;AAAA,UACF;AAAA,UACA,OAAOA,MAAK,EAAE,CAAC;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAGA,QAAI,QAAQ,WAAW,SAAS,GAAG;AACjC,YAAM,OAAO,QAAQ,WAAW,QAAQ,WAAW,SAAS,CAAC;AAC7D,YAAM,UAAU,KAAK,CAAC,KAAK;AAC3B,cAAQ,KAAK,SAAS,QAAW,KAAK,CAAC,CAAC;AAAA,IAC1C;AAGA,SAAK,WAAW;AAAA,MACd,OAAOC;AAAA,QACL,OAAO,SAAS,IAAI,OAAO,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAC;AAAA,MACzE;AAAA,MACA,KAAKA;AAAA,QACH,OAAO,SAAS,IACZ,OAAO,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,MAC7B,EAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAC;AAAA,MACpC;AAAA,IACF;AAGA,IAAAD,SAAQ;AACR,WAAO,EAAEA,SAAQ,OAAO,WAAW,QAAQ;AACzC,aAAO,OAAO,WAAWA,MAAK,EAAE,IAAI,KAAK;AAAA,IAC3C;AAEA,WAAO;AAAA,EACT;AAQA,WAAS,YAAY,QAAQE,QAAO,QAAQ;AAC1C,QAAIF,SAAQE,SAAQ;AACpB,QAAI,mBAAmB;AACvB,QAAI,aAAa;AAEjB,QAAIR;AAEJ,QAAI;AAEJ,QAAI;AAEJ,QAAI;AAEJ,WAAO,EAAEM,UAAS,QAAQ;AACxB,YAAM,QAAQ,OAAOA,MAAK;AAE1B,cAAQ,MAAM,CAAC,EAAE,MAAM;AAAA,QACrB,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM,YAAY;AACrB,cAAI,MAAM,CAAC,MAAM,SAAS;AACxB;AAAA,UACF,OAAO;AACL;AAAA,UACF;AAEA,qBAAW;AAEX;AAAA,QACF;AAAA,QAEA,KAAK,MAAM,iBAAiB;AAC1B,cAAI,MAAM,CAAC,MAAM,SAAS;AACxB,gBACEN,aACA,CAAC,YACD,CAAC,oBACD,CAAC,qBACD;AACA,oCAAsBM;AAAA,YACxB;AAEA,uBAAW;AAAA,UACb;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM;AAAA,QACX,KAAK,MAAM,0BAA0B;AAGnC;AAAA,QACF;AAAA,QAEA,SAAS;AACP,qBAAW;AAAA,QACb;AAAA,MACF;AAEA,UACG,CAAC,oBACA,MAAM,CAAC,MAAM,WACb,MAAM,CAAC,EAAE,SAAS,MAAM,kBACzB,qBAAqB,MACpB,MAAM,CAAC,MAAM,WACZ,MAAM,CAAC,EAAE,SAAS,MAAM,iBACvB,MAAM,CAAC,EAAE,SAAS,MAAM,cAC5B;AACA,YAAIN,WAAU;AACZ,cAAI,YAAYM;AAChB,sBAAY;AAEZ,iBAAO,aAAa;AAClB,kBAAM,YAAY,OAAO,SAAS;AAElC,gBACE,UAAU,CAAC,EAAE,SAAS,MAAM,cAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,iBAC5B;AACA,kBAAI,UAAU,CAAC,MAAM;AAAQ;AAE7B,kBAAI,WAAW;AACb,uBAAO,SAAS,EAAE,CAAC,EAAE,OAAO,MAAM;AAClC,6BAAa;AAAA,cACf;AAEA,wBAAU,CAAC,EAAE,OAAO,MAAM;AAC1B,0BAAY;AAAA,YACd,WACE,UAAU,CAAC,EAAE,SAAS,MAAM,cAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,oBAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,8BAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,oBAC5B,UAAU,CAAC,EAAE,SAAS,MAAM,gBAC5B;AAAA,YAEF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAEA,cACE,wBACC,CAAC,aAAa,sBAAsB,YACrC;AACA,YAAAN,UAAS,UAAU;AAAA,UACrB;AAGA,UAAAA,UAAS,MAAM,OAAO;AAAA,YACpB,CAAC;AAAA,YACD,YAAY,OAAO,SAAS,EAAE,CAAC,EAAE,QAAQ,MAAM,CAAC,EAAE;AAAA,UACpD;AAEA,iBAAO,OAAO,aAAaM,QAAO,GAAG,CAAC,QAAQN,WAAU,MAAM,CAAC,CAAC,CAAC;AACjE,UAAAM;AACA;AAAA,QACF;AAGA,YAAI,MAAM,CAAC,EAAE,SAAS,MAAM,gBAAgB;AAE1C,gBAAM,OAAO;AAAA,YACX,MAAM;AAAA,YACN,SAAS;AAAA,YACT,OAAO,OAAO,OAAO,CAAC,GAAG,MAAM,CAAC,EAAE,KAAK;AAAA;AAAA,YAEvC,KAAK;AAAA,UACP;AACA,UAAAN,YAAW;AACX,iBAAO,OAAOM,QAAO,GAAG,CAAC,SAAS,MAAM,MAAM,CAAC,CAAC,CAAC;AACjD,UAAAA;AACA;AACA,gCAAsB;AACtB,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAEA,WAAOE,MAAK,EAAE,CAAC,EAAE,UAAU;AAC3B,WAAO;AAAA,EACT;AAYA,WAAS,OAAO,QAAQ,KAAK;AAC3B,WAAO;AAOP,aAAS,KAAK,OAAO;AACnB,YAAM,KAAK,MAAM,OAAO,KAAK,GAAG,KAAK;AACrC,UAAI;AAAK,YAAI,KAAK,MAAM,KAAK;AAAA,IAC/B;AAAA,EACF;AAKA,WAAS,SAAS;AAChB,SAAK,MAAM,KAAK,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC,CAAC;AAAA,EAClD;AAKA,WAAS,MAAM,MAAM,OAAO,cAAc;AACxC,UAAM,SAAS,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC/C,OAAO,QAAQ,mBAAmB;AAClC,OAAO,cAAc,QAAQ,mBAAmB;AAEhD,UAAM,WAAW,OAAO;AACxB,aAAS,KAAK,IAAI;AAClB,SAAK,MAAM,KAAK,IAAI;AACpB,SAAK,WAAW,KAAK,CAAC,OAAO,gBAAgB,MAAS,CAAC;AACvD,SAAK,WAAW;AAAA,MACd,OAAOD,OAAM,MAAM,KAAK;AAAA;AAAA,MAExB,KAAK;AAAA,IACP;AAAA,EACF;AAUA,WAAS,OAAO,KAAK;AACnB,WAAO;AAOP,aAAS,MAAM,OAAO;AACpB,UAAI;AAAK,YAAI,KAAK,MAAM,KAAK;AAC7B,WAAK,KAAK,MAAM,KAAK;AAAA,IACvB;AAAA,EACF;AAKA,WAAS,KAAK,OAAO,aAAa;AAChC,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,OAAO,MAAM,iBAAiB;AAC9B,UAAM,OAAO,KAAK,WAAW,IAAI;AAEjC,QAAI,CAAC,MAAM;AACT,YAAM,IAAI;AAAA,QACR,mBACE,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,MACJ;AAAA,IACF,WAAW,KAAK,CAAC,EAAE,SAAS,MAAM,MAAM;AACtC,UAAI,aAAa;AACf,oBAAY,KAAK,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,MACvC,OAAO;AACL,cAAM,UAAU,KAAK,CAAC,KAAK;AAC3B,gBAAQ,KAAK,MAAM,OAAO,KAAK,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAEA,OAAO,KAAK,SAAS,YAAY,8BAA8B;AAC/D,OAAO,KAAK,UAAU,mCAAmC;AACzD,SAAK,SAAS,MAAMA,OAAM,MAAM,GAAG;AAAA,EACrC;AAKA,WAAS,SAAS;AAChB,WAAO,SAAS,KAAK,MAAM,IAAI,CAAC;AAAA,EAClC;AAUA,WAAS,qBAAqB;AAC5B,SAAK,KAAK,8BAA8B;AAAA,EAC1C;AAMA,WAAS,qBAAqB,OAAO;AACnC,QAAI,KAAK,KAAK,6BAA6B;AACzC,YAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,SAAO,UAAU,yBAAyB;AAC1C,SAAO,SAAS,SAAS,QAAQ,wBAAwB;AACzD,eAAS,QAAQ,OAAO;AAAA,QACtB,KAAK,eAAe,KAAK;AAAA,QACzB,UAAU;AAAA,MACZ;AACA,WAAK,KAAK,8BAA8B;AAAA,IAC1C;AAAA,EACF;AAMA,WAAS,4BAA4B;AACnC,UAAME,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AACrD,SAAK,OAAOA;AAAA,EACd;AAMA,WAAS,4BAA4B;AACnC,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AACrD,SAAK,OAAOA;AAAA,EACd;AAMA,WAAS,wBAAwB;AAE/B,QAAI,KAAK,KAAK;AAAgB;AAC9B,SAAK,OAAO;AACZ,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,mBAAmB;AAC1B,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,QAAQA,MAAK,QAAQ,4BAA4B,EAAE;AACxD,SAAK,KAAK,iBAAiB;AAAA,EAC7B;AAMA,WAAS,qBAAqB;AAC5B,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,QAAQA,MAAK,QAAQ,gBAAgB,EAAE;AAAA,EAC9C;AAMA,WAAS,4BAA4B,OAAO;AAC1C,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,cAAc,8BAA8B;AAEjE,SAAK,QAAQ;AACb,SAAK,aAAa;AAAA,MAChB,KAAK,eAAe,KAAK;AAAA,IAC3B,EAAE,YAAY;AAAA,EAChB;AAMA,WAAS,8BAA8B;AACrC,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,cAAc,8BAA8B;AAEjE,SAAK,QAAQA;AAAA,EACf;AAMA,WAAS,oCAAoC;AAC3C,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,cAAc,8BAA8B;AAEjE,SAAK,MAAMA;AAAA,EACb;AAMA,WAAS,yBAAyB,OAAO;AACvC,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,WAAW,2BAA2B;AAE3D,QAAI,CAAC,KAAK,OAAO;AACf,YAAM,QAAQ,KAAK,eAAe,KAAK,EAAE;AAEzC;AAAA,QACE,UAAU,KACR,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU,KACV,UAAU;AAAA,QACZ;AAAA,MACF;AAEA,WAAK,QAAQ;AAAA,IACf;AAAA,EACF;AAMA,WAAS,0BAA0B;AACjC,SAAK,KAAK,+BAA+B;AAAA,EAC3C;AAMA,WAAS,gCAAgC,OAAO;AAC9C,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,WAAW,2BAA2B;AAE3D,SAAK,QACH,KAAK,eAAe,KAAK,EAAE,YAAY,CAAC,MAAM,MAAM,WAAW,IAAI;AAAA,EACvE;AAMA,WAAS,sBAAsB;AAC7B,SAAK,KAAK,+BAA+B;AAAA,EAC3C;AAOA,WAAS,YAAY,OAAO;AAC1B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,cAAc,MAAM,0BAA0B;AAErD,UAAM,WAAW,KAAK;AAEtB,QAAI,OAAO,SAAS,SAAS,SAAS,CAAC;AAEvC,QAAI,CAAC,QAAQ,KAAK,SAAS,QAAQ;AAEjC,aAAOC,MAAK;AACZ,WAAK,WAAW;AAAA,QACd,OAAOH,OAAM,MAAM,KAAK;AAAA;AAAA,QAExB,KAAK;AAAA,MACP;AACA,eAAS,KAAK,IAAI;AAAA,IACpB;AAEA,SAAK,MAAM,KAAK,IAAI;AAAA,EACtB;AAOA,WAAS,WAAW,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,OAAO,MAAM,sCAAsC;AACnD,OAAO,WAAW,MAAM,yCAAyC;AACjE,OAAO,KAAK,UAAU,0CAA0C;AAChE,SAAK,SAAS,KAAK,eAAe,KAAK;AACvC,SAAK,SAAS,MAAMA,OAAM,MAAM,GAAG;AAAA,EACrC;AAOA,WAAS,iBAAiB,OAAO;AAC/B,UAAM,UAAU,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAChD,OAAO,SAAS,iBAAiB;AAGjC,QAAI,KAAK,KAAK,aAAa;AACzB,SAAO,cAAc,SAAS,mBAAmB;AACjD,YAAM,OAAO,QAAQ,SAAS,QAAQ,SAAS,SAAS,CAAC;AACzD,SAAO,KAAK,UAAU,2CAA2C;AACjE,WAAK,SAAS,MAAMA,OAAM,MAAM,GAAG;AACnC,WAAK,KAAK,cAAc;AACxB;AAAA,IACF;AAEA,QACE,CAAC,KAAK,KAAK,gCACX,OAAO,eAAe,SAAS,QAAQ,IAAI,GAC3C;AACA,kBAAY,KAAK,MAAM,KAAK;AAC5B,iBAAW,KAAK,MAAM,KAAK;AAAA,IAC7B;AAAA,EACF;AAOA,WAAS,kBAAkB;AACzB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAOA,WAAS,iBAAiB;AACxB,UAAME,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,QAAQA;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,QAAQA;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,cAAc,+BAA+B;AAElE,SAAK,QAAQA;AAAA,EACf;AAOA,WAAS,aAAa;AACpB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAMrD,QAAI,KAAK,KAAK,aAAa;AAEzB,YAAM,gBAAgB,KAAK,KAAK,iBAAiB;AAEjD,WAAK,QAAQ;AAEb,WAAK,gBAAgB;AAErB,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd,OAAO;AAEL,aAAO,KAAK;AAEZ,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,cAAc;AACrB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,SAAS,yBAAyB;AAMvD,QAAI,KAAK,KAAK,aAAa;AAEzB,YAAM,gBAAgB,KAAK,KAAK,iBAAiB;AAEjD,WAAK,QAAQ;AAEb,WAAK,gBAAgB;AAErB,aAAO,KAAK;AACZ,aAAO,KAAK;AAAA,IACd,OAAO;AAEL,aAAO,KAAK;AAEZ,aAAO,KAAK;AAAA,IACd;AAEA,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,gBAAgB,OAAO;AAC9B,UAAME,UAAS,KAAK,eAAe,KAAK;AACxC,UAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,OAAO,UAAU,4BAA4B;AAC7C;AAAA,MACE,SAAS,SAAS,WAAW,SAAS,SAAS;AAAA,MAC/C;AAAA,IACF;AAIA,aAAS,QAAQ,aAAaA,OAAM;AAEpC,aAAS,aAAa,oBAAoBA,OAAM,EAAE,YAAY;AAAA,EAChE;AAOA,WAAS,cAAc;AACrB,UAAM,WAAW,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AACjD,OAAO,UAAU,wBAAwB;AACzC,OAAO,SAAS,SAAS,YAAY,4BAA4B;AACjE,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC;AAAA,MACE,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAGA,SAAK,KAAK,cAAc;AAExB,QAAI,KAAK,SAAS,QAAQ;AAExB,YAAM,WAAW,SAAS;AAE1B,WAAK,WAAW;AAAA,IAClB,OAAO;AACL,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AAOA,WAAS,kCAAkC;AACzC,UAAMF,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC;AAAA,MACE,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AACA,SAAK,MAAMA;AAAA,EACb;AAOA,WAAS,4BAA4B;AACnC,UAAMA,QAAO,KAAK,OAAO;AACzB,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC;AAAA,MACE,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AACA,SAAK,QAAQA;AAAA,EACf;AAOA,WAAS,iBAAiB;AACxB,SAAK,KAAK,cAAc;AAAA,EAC1B;AAOA,WAAS,mBAAmB;AAC1B,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,sBAAsB,OAAO;AACpC,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC;AAAA,MACE,KAAK,SAAS,WAAW,KAAK,SAAS;AAAA,MACvC;AAAA,IACF;AAIA,SAAK,QAAQ;AAEb,SAAK,aAAa;AAAA,MAChB,KAAK,eAAe,KAAK;AAAA,IAC3B,EAAE,YAAY;AACd,SAAK,KAAK,gBAAgB;AAAA,EAC5B;AAOA,WAAS,+BAA+B,OAAO;AAC7C;AAAA,MACE,MAAM,SAAS,qCACb,MAAM,SAAS;AAAA,IACnB;AACA,SAAK,KAAK,yBAAyB,MAAM;AAAA,EAC3C;AAMA,WAAS,8BAA8B,OAAO;AAC5C,UAAMA,QAAO,KAAK,eAAe,KAAK;AACtC,UAAM,OAAO,KAAK,KAAK;AAEvB,QAAI;AAEJ,QAAI,MAAM;AACR,cAAQ;AAAA,QACNA;AAAA,QACA,SAAS,MAAM,kCACX,UAAU,qBACV,UAAU;AAAA,MAChB;AACA,WAAK,KAAK,yBAAyB;AAAA,IACrC,OAAO;AACL,YAAM,SAAS,8BAA8BA,KAAI;AACjD,SAAO,WAAW,OAAO,8BAA8B;AACvD,cAAQ;AAAA,IACV;AAEA,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,iBAAiB;AAC9B,OAAO,WAAW,MAAM,uBAAuB;AAC/C,SAAK,SAAS;AAAA,EAChB;AAMA,WAAS,yBAAyB,OAAO;AACvC,UAAM,OAAO,KAAK,MAAM,IAAI;AAC5B,OAAO,MAAM,iBAAiB;AAC9B,OAAO,KAAK,UAAU,0BAA0B;AAChD,SAAK,SAAS,MAAMF,OAAM,MAAM,GAAG;AAAA,EACrC;AAMA,WAAS,uBAAuB,OAAO;AACrC,eAAW,KAAK,MAAM,KAAK;AAC3B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,MAAM,KAAK,eAAe,KAAK;AAAA,EACtC;AAMA,WAAS,oBAAoB,OAAO;AAClC,eAAW,KAAK,MAAM,KAAK;AAC3B,UAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,CAAC;AAC7C,OAAO,MAAM,wBAAwB;AACrC,OAAO,KAAK,SAAS,QAAQ,wBAAwB;AAErD,SAAK,MAAM,YAAY,KAAK,eAAe,KAAK;AAAA,EAClD;AAOA,WAASd,cAAa;AACpB,WAAO,EAAC,MAAM,cAAc,UAAU,CAAC,EAAC;AAAA,EAC1C;AAGA,WAAS,WAAW;AAClB,WAAO,EAAC,MAAM,QAAQ,MAAM,MAAM,MAAM,MAAM,OAAO,GAAE;AAAA,EACzD;AAGA,WAASC,YAAW;AAClB,WAAO,EAAC,MAAM,cAAc,OAAO,GAAE;AAAA,EACvC;AAGA,WAASC,cAAa;AACpB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,MACZ,OAAO;AAAA,MACP,OAAO;AAAA,MACP,KAAK;AAAA,IACP;AAAA,EACF;AAGA,WAASC,YAAW;AAClB,WAAO,EAAC,MAAM,YAAY,UAAU,CAAC,EAAC;AAAA,EACxC;AAGA,WAASJ,WAAU;AACjB,WAAO;AAAA,MACL,MAAM;AAAA;AAAA,MAEN,OAAO;AAAA,MACP,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAGA,WAASK,aAAY;AACnB,WAAO,EAAC,MAAM,QAAO;AAAA,EACvB;AAGA,WAASC,QAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,GAAE;AAAA,EACjC;AAGA,WAASC,SAAQ;AACf,WAAO,EAAC,MAAM,SAAS,OAAO,MAAM,KAAK,IAAI,KAAK,KAAI;AAAA,EACxD;AAGA,WAASR,QAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,MAAM,KAAK,IAAI,UAAU,CAAC,EAAC;AAAA,EAC1D;AAMA,WAASU,MAAK,OAAO;AACnB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAM,SAAS;AAAA,MACxB,OAAO;AAAA,MACP,QAAQ,MAAM;AAAA,MACd,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAMA,WAASD,UAAS,OAAO;AACvB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,QAAQ,MAAM;AAAA,MACd,SAAS;AAAA,MACT,UAAU,CAAC;AAAA,IACb;AAAA,EACF;AAGA,WAASE,aAAY;AACnB,WAAO,EAAC,MAAM,aAAa,UAAU,CAAC,EAAC;AAAA,EACzC;AAGA,WAASC,UAAS;AAChB,WAAO,EAAC,MAAM,UAAU,UAAU,CAAC,EAAC;AAAA,EACtC;AAGA,WAASO,QAAO;AACd,WAAO,EAAC,MAAM,QAAQ,OAAO,GAAE;AAAA,EACjC;AAGA,WAASN,iBAAgB;AACvB,WAAO,EAAC,MAAM,gBAAe;AAAA,EAC/B;AACF;AAUA,SAASG,OAAM,GAAG;AAChB,SAAO,EAAC,MAAM,EAAE,MAAM,QAAQ,EAAE,QAAQ,QAAQ,EAAE,OAAM;AAC1D;AAOA,SAAS,UAAU,UAAU,YAAY;AACvC,MAAID,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,WAAW,QAAQ;AAClC,UAAM,QAAQ,WAAWA,MAAK;AAE9B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAU,UAAU,KAAK;AAAA,IAC3B,OAAO;AACL,gBAAU,UAAU,KAAK;AAAA,IAC3B;AAAA,EACF;AACF;AAOA,SAAS,UAAU,UAAUM,YAAW;AAEtC,MAAI;AAEJ,OAAK,OAAOA,YAAW;AACrB,QAAItB,KAAI,KAAKsB,YAAW,GAAG,GAAG;AAC5B,cAAQ,KAAK;AAAA,QACX,KAAK,kBAAkB;AACrB,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,qBAAS,GAAG,EAAE,KAAK,GAAG,KAAK;AAAA,UAC7B;AAEA;AAAA,QACF;AAAA,QAEA,KAAK,cAAc;AACjB,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,qBAAS,GAAG,EAAE,KAAK,GAAG,KAAK;AAAA,UAC7B;AAEA;AAAA,QACF;AAAA,QAEA,KAAK;AAAA,QACL,KAAK,QAAQ;AACX,gBAAM,QAAQA,WAAU,GAAG;AAC3B,cAAI,OAAO;AACT,mBAAO,OAAO,SAAS,GAAG,GAAG,KAAK;AAAA,UACpC;AAEA;AAAA,QACF;AAAA,MAEF;AAAA,IACF;AAAA,EACF;AACF;AAGA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,MAAM;AACR,UAAM,IAAI;AAAA,MACR,mBACE,KAAK,OACL,QACA,kBAAkB,EAAC,OAAO,KAAK,OAAO,KAAK,KAAK,IAAG,CAAC,IACpD,4BACA,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,IACJ;AAAA,EACF,OAAO;AACL,UAAM,IAAI;AAAA,MACR,sCACE,MAAM,OACN,QACA,kBAAkB,EAAC,OAAO,MAAM,OAAO,KAAK,MAAM,IAAG,CAAC,IACtD;AAAA,IACJ;AAAA,EACF;AACF;;;AC9yCe,SAAR,YAA6B,SAAS;AAG3C,QAAM,OAAO;AAEb,OAAK,SAAS;AAKd,WAAS,OAAO,KAAK;AACnB,WAAO,aAAa,KAAK;AAAA,MACvB,GAAG,KAAK,KAAK,UAAU;AAAA,MACvB,GAAG;AAAA;AAAA;AAAA;AAAA,MAIH,YAAY,KAAK,KAAK,qBAAqB,KAAK,CAAC;AAAA,MACjD,iBAAiB,KAAK,KAAK,wBAAwB,KAAK,CAAC;AAAA,IAC3D,CAAC;AAAA,EACH;AACF;;;ACvBO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,GAAG,IAAI;AAAA,EAC5C;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AAC5E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,CAAC,MAAM,UAAU,MAAM,MAAM,GAAG,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AACpE;;;ACLO,SAAS,KAAK,OAAO,MAAM;AAChC,QAAM,QAAQ,KAAK,QAAQ,KAAK,QAAQ,OAAO;AAE/C,QAAM,aAAa,CAAC;AAEpB,MAAI,KAAK,MAAM;AACb,eAAW,YAAY,CAAC,cAAc,KAAK,IAAI;AAAA,EACjD;AAIA,MAAI,SAAS;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,MAAK,CAAC;AAAA,EAClC;AAEA,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,EAAC,MAAM,KAAK,KAAI;AAAA,EAChC;AAEA,QAAM,MAAM,MAAM,MAAM;AACxB,WAAS,MAAM,UAAU,MAAM,MAAM;AAGrC,WAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,CAAC,GAAG,UAAU,CAAC,MAAM,EAAC;AAC7E,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO;AACT;;;AC9BO,SAAS,cAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACVO,SAAS,SAAS,OAAO,MAAM;AAEpC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACXO,SAAS,kBAAkB,OAAO,MAAM;AAC7C,QAAM,gBACJ,OAAO,MAAM,QAAQ,kBAAkB,WACnC,MAAM,QAAQ,gBACd;AACN,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAMC,SAAQ,MAAM,cAAc,QAAQ,EAAE;AAE5C,MAAI;AAEJ,MAAI,eAAe,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,iBAAiB,QAAW;AAC9B,mBAAe;AACf,UAAM,cAAc,KAAK,EAAE;AAC3B,cAAU,MAAM,cAAc;AAAA,EAChC,OAAO;AACL,cAAUA,SAAQ;AAAA,EACpB;AAEA,kBAAgB;AAChB,QAAM,eAAe,IAAI,IAAI,YAAY;AAGzC,QAAMC,QAAO;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY;AAAA,MACV,MAAM,MAAM,gBAAgB,QAAQ;AAAA,MACpC,IACE,gBACA,WACA,UACC,eAAe,IAAI,MAAM,eAAe;AAAA,MAC3C,iBAAiB;AAAA,MACjB,iBAAiB,CAAC,gBAAgB;AAAA,IACpC;AAAA,IACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,OAAO,EAAC,CAAC;AAAA,EACnD;AACA,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,MAAM;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,GAAG;AACrB,SAAO,MAAM,UAAU,MAAM,GAAG;AAClC;;;AClDO,SAAS,QAAQ,OAAO,MAAM;AAEnC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,MAAM,KAAK;AAAA,IACpB,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACRO,SAASC,MAAK,OAAO,MAAM;AAChC,MAAI,MAAM,QAAQ,oBAAoB;AAEpC,UAAM,SAAS,EAAC,MAAM,OAAO,OAAO,KAAK,MAAK;AAC9C,UAAM,MAAM,MAAM,MAAM;AACxB,WAAO,MAAM,UAAU,MAAM,MAAM;AAAA,EACrC;AAEA,SAAO;AACT;;;ACRO,SAAS,OAAO,OAAO,MAAM;AAClC,QAAM,UAAU,KAAK;AACrB,MAAI,SAAS;AAEb,MAAI,YAAY,aAAa;AAC3B,cAAU;AAAA,EACZ,WAAW,YAAY,QAAQ;AAC7B,cAAU,OAAO,KAAK,SAAS,KAAK,cAAc;AAAA,EACpD;AAEA,MAAI,KAAK,SAAS,kBAAkB;AAClC,WAAO,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,KAAK,MAAM,OAAM,CAAC;AAAA,EACzD;AAEA,QAAM,WAAW,MAAM,IAAI,IAAI;AAC/B,QAAM,OAAO,SAAS,CAAC;AAEvB,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,QAAQ,MAAM,KAAK;AAAA,EAC1B,OAAO;AACL,aAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,EAC7C;AAEA,QAAM,OAAO,SAAS,SAAS,SAAS,CAAC;AAEzC,MAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,SAAK,SAAS;AAAA,EAChB,OAAO;AACL,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,OAAM,CAAC;AAAA,EAC7C;AAEA,SAAO;AACT;;;ACjCO,SAAS,eAAe,OAAO,MAAM;AAC1C,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAMC,cAAa,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,CAACA,aAAY;AACf,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,KAAK,aAAaA,YAAW,OAAO,EAAE,GAAG,KAAK,KAAK,IAAG;AAE1E,MAAIA,YAAW,UAAU,QAAQA,YAAW,UAAU,QAAW;AAC/D,eAAW,QAAQA,YAAW;AAAA,EAChC;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACrBO,SAAS,MAAM,OAAO,MAAM;AAEjC,QAAM,aAAa,EAAC,KAAK,aAAa,KAAK,GAAG,EAAC;AAE/C,MAAI,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAW;AAC/C,eAAW,MAAM,KAAK;AAAA,EACxB;AAEA,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,UAAU,CAAC,EAAC;AACzE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACfO,SAAS,WAAW,OAAO,MAAM;AAEtC,QAAMC,QAAO,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAM,QAAQ,aAAa,GAAG,EAAC;AACvE,QAAM,MAAM,MAAMA,KAAI;AAGtB,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAACA,KAAI;AAAA,EACjB;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACbO,SAAS,cAAc,OAAO,MAAM;AACzC,QAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAC/C,QAAMC,cAAa,MAAM,eAAe,IAAI,EAAE;AAE9C,MAAI,CAACA,aAAY;AACf,WAAO,OAAO,OAAO,IAAI;AAAA,EAC3B;AAGA,QAAM,aAAa,EAAC,MAAM,aAAaA,YAAW,OAAO,EAAE,EAAC;AAE5D,MAAIA,YAAW,UAAU,QAAQA,YAAW,UAAU,QAAW;AAC/D,eAAW,QAAQA,YAAW;AAAA,EAChC;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AC1BO,SAAS,KAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,EAAC,MAAM,aAAa,KAAK,GAAG,EAAC;AAEhD,MAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,QAAW;AACnD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,IACA,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACZO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,QAAM,QAAQ,SAAS,UAAU,MAAM,IAAI,cAAc,IAAI;AAE7D,QAAM,aAAa,CAAC;AAEpB,QAAM,WAAW,CAAC;AAElB,MAAI,OAAO,KAAK,YAAY,WAAW;AACrC,UAAM,OAAO,QAAQ,CAAC;AAEtB,QAAIC;AAEJ,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,MAAAA,aAAY;AAAA,IACd,OAAO;AACL,MAAAA,aAAY,EAAC,MAAM,WAAW,SAAS,KAAK,YAAY,CAAC,GAAG,UAAU,CAAC,EAAC;AACxE,cAAQ,QAAQA,UAAS;AAAA,IAC3B;AAEA,QAAIA,WAAU,SAAS,SAAS,GAAG;AACjC,MAAAA,WAAU,SAAS,QAAQ,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,IACvD;AAEA,IAAAA,WAAU,SAAS,QAAQ;AAAA,MACzB,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,MAAM,YAAY,SAAS,KAAK,SAAS,UAAU,KAAI;AAAA,MACpE,UAAU,CAAC;AAAA,IACb,CAAC;AAID,eAAW,YAAY,CAAC,gBAAgB;AAAA,EAC1C;AAEA,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQA,MAAK;AAG3B,QACE,SACAA,WAAU,KACV,MAAM,SAAS,aACf,MAAM,YAAY,KAClB;AACA,eAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,IAC3C;AAEA,QAAI,MAAM,SAAS,aAAa,MAAM,YAAY,OAAO,CAAC,OAAO;AAC/D,eAAS,KAAK,GAAG,MAAM,QAAQ;AAAA,IACjC,OAAO;AACL,eAAS,KAAK,KAAK;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,OAAO,QAAQ,QAAQ,SAAS,CAAC;AAGvC,MAAI,SAAS,SAAS,KAAK,SAAS,aAAa,KAAK,YAAY,MAAM;AACtE,aAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EAC3C;AAGA,QAAM,SAAS,EAAC,MAAM,WAAW,SAAS,MAAM,YAAY,SAAQ;AACpE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAMA,SAAS,UAAU,MAAM;AACvB,MAAI,QAAQ;AACZ,MAAI,KAAK,SAAS,QAAQ;AACxB,YAAQ,KAAK,UAAU;AACvB,UAAM,WAAW,KAAK;AACtB,QAAIA,SAAQ;AAEZ,WAAO,CAAC,SAAS,EAAEA,SAAQ,SAAS,QAAQ;AAC1C,cAAQ,cAAc,SAASA,MAAK,CAAC;AAAA,IACvC;AAAA,EACF;AAEA,SAAO;AACT;AAMA,SAAS,cAAc,MAAM;AAC3B,QAAM,SAAS,KAAK;AAEpB,SAAO,WAAW,QAAQ,WAAW,SACjC,KAAK,SAAS,SAAS,IACvB;AACN;;;ACxGO,SAASC,MAAK,OAAO,MAAM;AAEhC,QAAM,aAAa,CAAC;AACpB,QAAM,UAAU,MAAM,IAAI,IAAI;AAC9B,MAAIC,SAAQ;AAEZ,MAAI,OAAO,KAAK,UAAU,YAAY,KAAK,UAAU,GAAG;AACtD,eAAW,QAAQ,KAAK;AAAA,EAC1B;AAGA,SAAO,EAAEA,SAAQ,QAAQ,QAAQ;AAC/B,UAAM,QAAQ,QAAQA,MAAK;AAE3B,QACE,MAAM,SAAS,aACf,MAAM,YAAY,QAClB,MAAM,cACN,MAAM,QAAQ,MAAM,WAAW,SAAS,KACxC,MAAM,WAAW,UAAU,SAAS,gBAAgB,GACpD;AACA,iBAAW,YAAY,CAAC,oBAAoB;AAC5C;AAAA,IACF;AAAA,EACF;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS,KAAK,UAAU,OAAO;AAAA,IAC/B;AAAA,IACA,UAAU,MAAM,KAAK,SAAS,IAAI;AAAA,EACpC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpCO,SAAS,UAAU,OAAO,MAAM;AAErC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACTO,SAASC,MAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,IAAI,IAAI,CAAC,EAAC;AACnE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACNO,SAAS,OAAO,OAAO,MAAM;AAElC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACXO,SAAS,MAAM,OAAO,MAAM;AACjC,QAAM,OAAO,MAAM,IAAI,IAAI;AAC3B,QAAM,WAAW,KAAK,MAAM;AAE5B,QAAM,eAAe,CAAC;AAEtB,MAAI,UAAU;AAEZ,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI;AAAA,IACvC;AACA,UAAM,MAAM,KAAK,SAAS,CAAC,GAAG,IAAI;AAClC,iBAAa,KAAK,IAAI;AAAA,EACxB;AAEA,MAAI,KAAK,SAAS,GAAG;AAEnB,UAAM,OAAO;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,MAAM,KAAK,MAAM,IAAI;AAAA,IACjC;AAEA,UAAMC,SAAQ,WAAW,KAAK,SAAS,CAAC,CAAC;AACzC,UAAM,MAAM,SAAS,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC,CAAC;AAC5D,QAAIA,UAAS;AAAK,WAAK,WAAW,EAAC,OAAAA,QAAO,IAAG;AAC7C,iBAAa,KAAK,IAAI;AAAA,EACxB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,cAAc,IAAI;AAAA,EACzC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACpCO,SAAS,SAAS,OAAO,MAAM,QAAQ;AAC5C,QAAM,WAAW,SAAS,OAAO,WAAW;AAE5C,QAAM,WAAW,WAAW,SAAS,QAAQ,IAAI,IAAI;AACrD,QAAM,UAAU,aAAa,IAAI,OAAO;AAExC,QAAM,QAAQ,UAAU,OAAO,SAAS,UAAU,OAAO,QAAQ;AACjE,QAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,SAAS;AACpD,MAAI,YAAY;AAEhB,QAAM,QAAQ,CAAC;AAEf,SAAO,EAAE,YAAY,QAAQ;AAE3B,UAAM,OAAO,KAAK,SAAS,SAAS;AAEpC,UAAM,aAAa,CAAC;AACpB,UAAM,aAAa,QAAQ,MAAM,SAAS,IAAI;AAE9C,QAAI,YAAY;AACd,iBAAW,QAAQ;AAAA,IACrB;AAGA,QAAIC,UAAS,EAAC,MAAM,WAAW,SAAS,YAAY,UAAU,CAAC,EAAC;AAEhE,QAAI,MAAM;AACR,MAAAA,QAAO,WAAW,MAAM,IAAI,IAAI;AAChC,YAAM,MAAM,MAAMA,OAAM;AACxB,MAAAA,UAAS,MAAM,UAAU,MAAMA,OAAM;AAAA,IACvC;AAEA,UAAM,KAAKA,OAAM;AAAA,EACnB;AAGA,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,KAAK,OAAO,IAAI;AAAA,EAClC;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACjDO,SAAS,UAAU,OAAO,MAAM;AAIrC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;AC/BA,IAAM,MAAM;AACZ,IAAM,QAAQ;AAWP,SAAS,UAAU,OAAO;AAC/B,QAAM,SAAS,OAAO,KAAK;AAC3B,QAAMC,UAAS;AACf,MAAI,QAAQA,QAAO,KAAK,MAAM;AAC9B,MAAI,OAAO;AAEX,QAAM,QAAQ,CAAC;AAEf,SAAO,OAAO;AACZ,UAAM;AAAA,MACJ,SAAS,OAAO,MAAM,MAAM,MAAM,KAAK,GAAG,OAAO,GAAG,IAAI;AAAA,MACxD,MAAM,CAAC;AAAA,IACT;AAEA,WAAO,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC9B,YAAQA,QAAO,KAAK,MAAM;AAAA,EAC5B;AAEA,QAAM,KAAK,SAAS,OAAO,MAAM,IAAI,GAAG,OAAO,GAAG,KAAK,CAAC;AAExD,SAAO,MAAM,KAAK,EAAE;AACtB;AAYA,SAAS,SAAS,OAAOC,QAAO,KAAK;AACnC,MAAI,aAAa;AACjB,MAAI,WAAW,MAAM;AAErB,MAAIA,QAAO;AACT,QAAIC,QAAO,MAAM,YAAY,UAAU;AAEvC,WAAOA,UAAS,OAAOA,UAAS,OAAO;AACrC;AACA,MAAAA,QAAO,MAAM,YAAY,UAAU;AAAA,IACrC;AAAA,EACF;AAEA,MAAI,KAAK;AACP,QAAIA,QAAO,MAAM,YAAY,WAAW,CAAC;AAEzC,WAAOA,UAAS,OAAOA,UAAS,OAAO;AACrC;AACA,MAAAA,QAAO,MAAM,YAAY,WAAW,CAAC;AAAA,IACvC;AAAA,EACF;AAEA,SAAO,WAAW,aAAa,MAAM,MAAM,YAAY,QAAQ,IAAI;AACrE;;;ACjDO,SAASC,MAAK,OAAO,MAAM;AAEhC,QAAM,SAAS,EAAC,MAAM,QAAQ,OAAO,UAAU,OAAO,KAAK,KAAK,CAAC,EAAC;AAClE,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACLO,SAASC,eAAc,OAAO,MAAM;AAEzC,QAAM,SAAS;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,CAAC;AAAA,EACb;AACA,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;;;ACAO,IAAM,WAAW;AAAA,EACtB;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA;AAAA;AAAA,EAEA,MAAAC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,MAAAC;AAAA,EACA,eAAAC;AAAA,EACA,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,oBAAoB;AACtB;AAGA,SAAS,SAAS;AAChB,SAAO;AACT;;;ACiBO,SAAS,2BAA2B,GAAG,kBAAkB;AAE9D,QAAM,SAAS,CAAC,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAE1C,MAAI,mBAAmB,GAAG;AACxB,WAAO,KAAK;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,OAAO,gBAAgB,EAAC,CAAC;AAAA,IAC5D,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAaO,SAAS,yBAAyB,gBAAgB,kBAAkB;AACzE,SACE,wBACC,iBAAiB,MACjB,mBAAmB,IAAI,MAAM,mBAAmB;AAErD;AAWO,SAAS,OAAO,OAAO;AAC5B,QAAM,gBACJ,OAAO,MAAM,QAAQ,kBAAkB,WACnC,MAAM,QAAQ,gBACd;AACN,QAAM,sBACJ,MAAM,QAAQ,uBAAuB;AACvC,QAAM,oBACJ,MAAM,QAAQ,qBAAqB;AACrC,QAAM,gBAAgB,MAAM,QAAQ,iBAAiB;AACrD,QAAM,uBAAuB,MAAM,QAAQ,wBAAwB;AACnE,QAAM,0BAA0B,MAAM,QAAQ,2BAA2B;AAAA,IACvE,WAAW,CAAC,SAAS;AAAA,EACvB;AAEA,QAAM,YAAY,CAAC;AACnB,MAAI,iBAAiB;AAErB,SAAO,EAAE,iBAAiB,MAAM,cAAc,QAAQ;AACpD,UAAMC,cAAa,MAAM,aAAa;AAAA,MACpC,MAAM,cAAc,cAAc;AAAA,IACpC;AAEA,QAAI,CAACA,aAAY;AACf;AAAA,IACF;AAEA,UAAMC,WAAU,MAAM,IAAID,WAAU;AACpC,UAAM,KAAK,OAAOA,YAAW,UAAU,EAAE,YAAY;AACrD,UAAM,SAAS,aAAa,GAAG,YAAY,CAAC;AAC5C,QAAI,mBAAmB;AAEvB,UAAM,iBAAiB,CAAC;AACxB,UAAM,SAAS,MAAM,eAAe,IAAI,EAAE;AAG1C,WAAO,WAAW,UAAa,EAAE,oBAAoB,QAAQ;AAC3D,UAAI,eAAe,SAAS,GAAG;AAC7B,uBAAe,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAChD;AAEA,UAAI,WACF,OAAO,wBAAwB,WAC3B,sBACA,oBAAoB,gBAAgB,gBAAgB;AAE1D,UAAI,OAAO,aAAa,UAAU;AAChC,mBAAW,EAAC,MAAM,QAAQ,OAAO,SAAQ;AAAA,MAC3C;AAEA,qBAAe,KAAK;AAAA,QAClB,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,UACV,MACE,MACA,gBACA,WACA,UACC,mBAAmB,IAAI,MAAM,mBAAmB;AAAA,UACnD,qBAAqB;AAAA,UACrB,WACE,OAAO,sBAAsB,WACzB,oBACA,kBAAkB,gBAAgB,gBAAgB;AAAA,UACxD,WAAW,CAAC,uBAAuB;AAAA,QACrC;AAAA,QACA,UAAU,MAAM,QAAQ,QAAQ,IAAI,WAAW,CAAC,QAAQ;AAAA,MAC1D,CAAC;AAAA,IACH;AAEA,UAAM,OAAOC,SAAQA,SAAQ,SAAS,CAAC;AAEvC,QAAI,QAAQ,KAAK,SAAS,aAAa,KAAK,YAAY,KAAK;AAC3D,YAAM,WAAW,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AACvD,UAAI,YAAY,SAAS,SAAS,QAAQ;AACxC,iBAAS,SAAS;AAAA,MACpB,OAAO;AACL,aAAK,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,IAAG,CAAC;AAAA,MAC/C;AAEA,WAAK,SAAS,KAAK,GAAG,cAAc;AAAA,IACtC,OAAO;AACL,MAAAA,SAAQ,KAAK,GAAG,cAAc;AAAA,IAChC;AAGA,UAAMC,YAAW;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,MACT,YAAY,EAAC,IAAI,gBAAgB,QAAQ,OAAM;AAAA,MAC/C,UAAU,MAAM,KAAKD,UAAS,IAAI;AAAA,IACpC;AAEA,UAAM,MAAMD,aAAYE,SAAQ;AAEhC,cAAU,KAAKA,SAAQ;AAAA,EACzB;AAEA,MAAI,UAAU,WAAW,GAAG;AAC1B;AAAA,EACF;AAEA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,EAAC,eAAe,MAAM,WAAW,CAAC,WAAW,EAAC;AAAA,IAC1D,UAAU;AAAA,MACR;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY;AAAA,UACV,GAAG,YAAgB,uBAAuB;AAAA,UAC1C,IAAI;AAAA,QACN;AAAA,QACA,UAAU,CAAC,EAAC,MAAM,QAAQ,OAAO,cAAa,CAAC;AAAA,MACjD;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,MAC1B;AAAA,QACE,MAAM;AAAA,QACN,SAAS;AAAA,QACT,YAAY,CAAC;AAAA,QACb,UAAU,MAAM,KAAK,WAAW,IAAI;AAAA,MACtC;AAAA,MACA,EAAC,MAAM,QAAQ,OAAO,KAAI;AAAA,IAC5B;AAAA,EACF;AACF;;;ACxEA,IAAMC,OAAM,CAAC,EAAE;AAGf,IAAMC,gBAAe,CAAC;AAYf,SAAS,YAAY,MAAM,SAAS;AACzC,QAAM,WAAW,WAAWA;AAE5B,QAAM,iBAAiB,oBAAI,IAAI;AAE/B,QAAM,eAAe,oBAAI,IAAI;AAE7B,QAAM,iBAAiB,oBAAI,IAAI;AAI/B,QAAMC,YAAW,EAAC,GAAG,UAAiB,GAAG,SAAS,SAAQ;AAG1D,QAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,eAAe,CAAC;AAAA,IAChB,UAAAA;AAAA,IACA,KAAAC;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,QAAM,MAAM,SAAU,MAAM;AAC1B,QAAI,KAAK,SAAS,gBAAgB,KAAK,SAAS,sBAAsB;AACpE,YAAM,MAAM,KAAK,SAAS,eAAe,iBAAiB;AAC1D,YAAM,KAAK,OAAO,KAAK,UAAU,EAAE,YAAY;AAI/C,UAAI,CAAC,IAAI,IAAI,EAAE,GAAG;AAEhB,YAAI,IAAI,IAAI,IAAI;AAAA,MAClB;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AAYP,WAASA,KAAI,MAAM,QAAQ;AACzB,UAAM,OAAO,KAAK;AAClB,UAAM,SAAS,MAAM,SAAS,IAAI;AAElC,QAAIH,KAAI,KAAK,MAAM,UAAU,IAAI,KAAK,QAAQ;AAC5C,aAAO,OAAO,OAAO,MAAM,MAAM;AAAA,IACnC;AAEA,QAAI,MAAM,QAAQ,eAAe,MAAM,QAAQ,YAAY,SAAS,IAAI,GAAG;AACzE,UAAI,cAAc,MAAM;AACtB,cAAM,EAAC,UAAU,GAAG,QAAO,IAAI;AAC/B,cAAM,SAAS,YAAgB,OAAO;AAEtC,eAAO,WAAW,MAAM,IAAI,IAAI;AAEhC,eAAO;AAAA,MACT;AAGA,aAAO,YAAgB,IAAI;AAAA,IAC7B;AAEA,UAAM,UAAU,MAAM,QAAQ,kBAAkB;AAEhD,WAAO,QAAQ,OAAO,MAAM,MAAM;AAAA,EACpC;AAUA,WAAS,IAAI,QAAQ;AAEnB,UAAMI,UAAS,CAAC;AAEhB,QAAI,cAAc,QAAQ;AACxB,YAAM,QAAQ,OAAO;AACrB,UAAIC,SAAQ;AACZ,aAAO,EAAEA,SAAQ,MAAM,QAAQ;AAC7B,cAAM,SAAS,MAAM,IAAI,MAAMA,MAAK,GAAG,MAAM;AAG7C,YAAI,QAAQ;AACV,cAAIA,UAAS,MAAMA,SAAQ,CAAC,EAAE,SAAS,SAAS;AAC9C,gBAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,QAAQ;AACpD,qBAAO,QAAQ,uBAAuB,OAAO,KAAK;AAAA,YACpD;AAEA,gBAAI,CAAC,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,WAAW;AACvD,oBAAM,OAAO,OAAO,SAAS,CAAC;AAE9B,kBAAI,QAAQ,KAAK,SAAS,QAAQ;AAChC,qBAAK,QAAQ,uBAAuB,KAAK,KAAK;AAAA,cAChD;AAAA,YACF;AAAA,UACF;AAEA,cAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,YAAAD,QAAO,KAAK,GAAG,MAAM;AAAA,UACvB,OAAO;AACL,YAAAA,QAAO,KAAK,MAAM;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAOA;AAAA,EACT;AACF;AAYA,SAAS,MAAM,MAAM,IAAI;AACvB,MAAI,KAAK;AAAU,OAAG,WAAW,SAAS,IAAI;AAChD;AAcA,SAAS,UAAU,MAAM,IAAI;AAE3B,MAAI,SAAS;AAGb,MAAI,QAAQ,KAAK,MAAM;AACrB,UAAM,QAAQ,KAAK,KAAK;AACxB,UAAM,YAAY,KAAK,KAAK;AAC5B,UAAM,cAAc,KAAK,KAAK;AAE9B,QAAI,OAAO,UAAU,UAAU;AAG7B,UAAI,OAAO,SAAS,WAAW;AAC7B,eAAO,UAAU;AAAA,MACnB,OAKK;AAGH,cAAM,WAAW,cAAc,SAAS,OAAO,WAAW,CAAC,MAAM;AACjE,iBAAS,EAAC,MAAM,WAAW,SAAS,OAAO,YAAY,CAAC,GAAG,SAAQ;AAAA,MACrE;AAAA,IACF;AAEA,QAAI,OAAO,SAAS,aAAa,aAAa;AAC5C,aAAO,OAAO,OAAO,YAAY,YAAgB,WAAW,CAAC;AAAA,IAC/D;AAEA,QACE,cAAc,UACd,OAAO,YACP,cAAc,QACd,cAAc,QACd;AACA,aAAO,WAAW;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,sBAAsB,OAAO,MAAM;AAC1C,QAAM,OAAO,KAAK,QAAQ,CAAC;AAE3B,QAAM,SACJ,WAAW,QACX,EAAEJ,KAAI,KAAK,MAAM,aAAa,KAAKA,KAAI,KAAK,MAAM,WAAW,KACzD,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAK,IAChC;AAAA,IACE,MAAM;AAAA,IACN,SAAS;AAAA,IACT,YAAY,CAAC;AAAA,IACb,UAAU,MAAM,IAAI,IAAI;AAAA,EAC1B;AAEN,QAAM,MAAM,MAAM,MAAM;AACxB,SAAO,MAAM,UAAU,MAAM,MAAM;AACrC;AAcO,SAAS,KAAK,OAAO,OAAO;AAEjC,QAAM,SAAS,CAAC;AAChB,MAAIK,SAAQ;AAEZ,MAAI,OAAO;AACT,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO,EAAEA,SAAQ,MAAM,QAAQ;AAC7B,QAAIA;AAAO,aAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAClD,WAAO,KAAK,MAAMA,MAAK,CAAC;AAAA,EAC1B;AAEA,MAAI,SAAS,MAAM,SAAS,GAAG;AAC7B,WAAO,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,CAAC;AAAA,EACzC;AAEA,SAAO;AACT;AAUA,SAAS,uBAAuB,OAAO;AACrC,MAAIA,SAAQ;AACZ,MAAIC,QAAO,MAAM,WAAWD,MAAK;AAEjC,SAAOC,UAAS,KAAKA,UAAS,IAAI;AAChC,IAAAD;AACA,IAAAC,QAAO,MAAM,WAAWD,MAAK;AAAA,EAC/B;AAEA,SAAO,MAAM,MAAMA,MAAK;AAC1B;;;ACjYO,SAAS,OAAO,MAAM,SAAS;AACpC,QAAM,QAAQ,YAAY,MAAM,OAAO;AACvC,QAAM,OAAO,MAAM,IAAI,MAAM,MAAS;AACtC,QAAM,OAAO,OAAO,KAAK;AAEzB,QAAM,SAAS,MAAM,QAAQ,IAAI,IAC7B,EAAC,MAAM,QAAQ,UAAU,KAAI,IAC7B,QAAQ,EAAC,MAAM,QAAQ,UAAU,CAAC,EAAC;AAEvC,MAAI,MAAM;AAIR,OAAO,cAAc,MAAM;AAC3B,WAAO,SAAS,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAI,GAAG,IAAI;AAAA,EACxD;AAEA,SAAO;AACT;;;AC8Be,SAAR,aAA8B,aAAa,SAAS;AACzD,MAAI,eAAe,SAAS,aAAa;AAIvC,WAAO,eAAgB,MAAM,MAAM;AAEjC,YAAM;AAAA;AAAA,QACJ,OAAO,MAAM,EAAC,MAAM,GAAG,QAAO,CAAC;AAAA;AAEjC,YAAM,YAAY,IAAI,UAAU,IAAI;AAAA,IACtC;AAAA,EACF;AAKA,SAAO,SAAU,MAAM,MAAM;AAM3B;AAAA;AAAA,MACE,OAAO,MAAM,EAAC,MAAM,GAAI,eAAe,QAAQ,CAAC;AAAA;AAAA,EAEpD;AACF;;;AC3JO,SAAS,KAAK,OAAO;AAC1B,MAAI,OAAO;AACT,UAAM;AAAA,EACR;AACF;;;ACkVA,oBAAmB;;;AC7VJ,SAAR,cAA+B,OAAO;AAC5C,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,WAAO;AAAA,EACR;AAEA,QAAM,YAAY,OAAO,eAAe,KAAK;AAC7C,UAAQ,cAAc,QAAQ,cAAc,OAAO,aAAa,OAAO,eAAe,SAAS,MAAM,SAAS,EAAE,OAAO,eAAe,UAAU,EAAE,OAAO,YAAY;AACtK;;;AC+BO,SAAS,SAAS;AAEvB,QAAM,MAAM,CAAC;AAEb,QAAM,WAAW,EAAC,KAAK,IAAG;AAE1B,SAAO;AAGP,WAAS,OAAOE,SAAQ;AACtB,QAAI,kBAAkB;AAEtB,UAAM,WAAWA,QAAO,IAAI;AAE5B,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,UAAU,6CAA6C,QAAQ;AAAA,IAC3E;AAEA,SAAK,MAAM,GAAGA,OAAM;AAQpB,aAAS,KAAK,UAAU,QAAQ;AAC9B,YAAM,KAAK,IAAI,EAAE,eAAe;AAChC,UAAIC,SAAQ;AAEZ,UAAI,OAAO;AACT,iBAAS,KAAK;AACd;AAAA,MACF;AAGA,aAAO,EAAEA,SAAQD,QAAO,QAAQ;AAC9B,YAAI,OAAOC,MAAK,MAAM,QAAQ,OAAOA,MAAK,MAAM,QAAW;AACzD,iBAAOA,MAAK,IAAID,QAAOC,MAAK;AAAA,QAC9B;AAAA,MACF;AAGA,MAAAD,UAAS;AAGT,UAAI,IAAI;AACN,QAAAE,MAAK,IAAI,IAAI,EAAE,GAAG,MAAM;AAAA,MAC1B,OAAO;AACL,iBAAS,MAAM,GAAG,MAAM;AAAA,MAC1B;AAAA,IACF;AAAA,EACF;AAGA,WAAS,IAAI,YAAY;AACvB,QAAI,OAAO,eAAe,YAAY;AACpC,YAAM,IAAI;AAAA,QACR,iDAAiD;AAAA,MACnD;AAAA,IACF;AAEA,QAAI,KAAK,UAAU;AACnB,WAAO;AAAA,EACT;AACF;AAkCO,SAASA,MAAK,YAAY,UAAU;AAEzC,MAAI;AAEJ,SAAO;AAQP,WAAS,WAAW,YAAY;AAC9B,UAAM,oBAAoB,WAAW,SAAS,WAAW;AAEzD,QAAI;AAEJ,QAAI,mBAAmB;AACrB,iBAAW,KAAK,IAAI;AAAA,IACtB;AAEA,QAAI;AACF,eAAS,WAAW,MAAM,MAAM,UAAU;AAAA,IAC5C,SAAS,OAAO;AACd,YAAM;AAAA;AAAA,QAAkC;AAAA;AAMxC,UAAI,qBAAqB,QAAQ;AAC/B,cAAM;AAAA,MACR;AAEA,aAAO,KAAK,SAAS;AAAA,IACvB;AAEA,QAAI,CAAC,mBAAmB;AACtB,UAAI,UAAU,OAAO,QAAQ,OAAO,OAAO,SAAS,YAAY;AAC9D,eAAO,KAAK,MAAM,IAAI;AAAA,MACxB,WAAW,kBAAkB,OAAO;AAClC,aAAK,MAAM;AAAA,MACb,OAAO;AACL,aAAK,MAAM;AAAA,MACb;AAAA,IACF;AAAA,EACF;AAOA,WAAS,KAAK,UAAU,QAAQ;AAC9B,QAAI,CAAC,QAAQ;AACX,eAAS;AACT,eAAS,OAAO,GAAG,MAAM;AAAA,IAC3B;AAAA,EACF;AAOA,WAAS,KAAK,OAAO;AACnB,SAAK,MAAM,KAAK;AAAA,EAClB;AACF;;;AC1JO,IAAM,UAAU,EAAC,UAAU,SAAS,SAAS,MAAM,KAAK,IAAG;AAclE,SAAS,SAAS,MAAMC,UAAS;AAC/B,MAAIA,aAAY,UAAa,OAAOA,aAAY,UAAU;AACxD,UAAM,IAAI,UAAU,iCAAiC;AAAA,EACvD;AAEA,aAAW,IAAI;AACf,MAAIC,SAAQ;AACZ,MAAI,MAAM;AACV,MAAIC,SAAQ,KAAK;AAEjB,MAAI;AAEJ,MACEF,aAAY,UACZA,SAAQ,WAAW,KACnBA,SAAQ,SAAS,KAAK,QACtB;AACA,WAAOE,UAAS;AACd,UAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAG5C,YAAI,cAAc;AAChB,UAAAD,SAAQC,SAAQ;AAChB;AAAA,QACF;AAAA,MACF,WAAW,MAAM,GAAG;AAGlB,uBAAe;AACf,cAAMA,SAAQ;AAAA,MAChB;AAAA,IACF;AAEA,WAAO,MAAM,IAAI,KAAK,KAAK,MAAMD,QAAO,GAAG;AAAA,EAC7C;AAEA,MAAID,aAAY,MAAM;AACpB,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB;AACvB,MAAI,eAAeA,SAAQ,SAAS;AAEpC,SAAOE,UAAS;AACd,QAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAG5C,UAAI,cAAc;AAChB,QAAAD,SAAQC,SAAQ;AAChB;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,mBAAmB,GAAG;AAGxB,uBAAe;AACf,2BAAmBA,SAAQ;AAAA,MAC7B;AAEA,UAAI,eAAe,IAAI;AAErB,YAAI,KAAK,YAAYA,MAAK,MAAMF,SAAQ,YAAY,cAAc,GAAG;AACnE,cAAI,eAAe,GAAG;AAGpB,kBAAME;AAAA,UACR;AAAA,QACF,OAAO;AAGL,yBAAe;AACf,gBAAM;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAID,WAAU,KAAK;AACjB,UAAM;AAAA,EACR,WAAW,MAAM,GAAG;AAClB,UAAM,KAAK;AAAA,EACb;AAEA,SAAO,KAAK,MAAMA,QAAO,GAAG;AAC9B;AAUA,SAAS,QAAQ,MAAM;AACrB,aAAW,IAAI;AAEf,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO;AAAA,EACT;AAEA,MAAI,MAAM;AACV,MAAIC,SAAQ,KAAK;AAEjB,MAAI;AAGJ,SAAO,EAAEA,QAAO;AACd,QAAI,KAAK,YAAYA,MAAK,MAAM,IAAc;AAC5C,UAAI,gBAAgB;AAClB,cAAMA;AACN;AAAA,MACF;AAAA,IACF,WAAW,CAAC,gBAAgB;AAE1B,uBAAiB;AAAA,IACnB;AAAA,EACF;AAEA,SAAO,MAAM,IACT,KAAK,YAAY,CAAC,MAAM,KACtB,MACA,MACF,QAAQ,KAAK,KAAK,YAAY,CAAC,MAAM,KACnC,OACA,KAAK,MAAM,GAAG,GAAG;AACzB;AAUA,SAAS,QAAQ,MAAM;AACrB,aAAW,IAAI;AAEf,MAAIA,SAAQ,KAAK;AAEjB,MAAI,MAAM;AACV,MAAI,YAAY;AAChB,MAAI,WAAW;AAGf,MAAI,cAAc;AAElB,MAAI;AAEJ,SAAOA,UAAS;AACd,UAAMC,QAAO,KAAK,YAAYD,MAAK;AAEnC,QAAIC,UAAS,IAAc;AAGzB,UAAI,gBAAgB;AAClB,oBAAYD,SAAQ;AACpB;AAAA,MACF;AAEA;AAAA,IACF;AAEA,QAAI,MAAM,GAAG;AAGX,uBAAiB;AACjB,YAAMA,SAAQ;AAAA,IAChB;AAEA,QAAIC,UAAS,IAAc;AAEzB,UAAI,WAAW,GAAG;AAChB,mBAAWD;AAAA,MACb,WAAW,gBAAgB,GAAG;AAC5B,sBAAc;AAAA,MAChB;AAAA,IACF,WAAW,WAAW,IAAI;AAGxB,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MACE,WAAW,KACX,MAAM;AAAA,EAEN,gBAAgB;AAAA,EAEf,gBAAgB,KAAK,aAAa,MAAM,KAAK,aAAa,YAAY,GACvE;AACA,WAAO;AAAA,EACT;AAEA,SAAO,KAAK,MAAM,UAAU,GAAG;AACjC;AAUA,SAAS,QAAQ,UAAU;AACzB,MAAIA,SAAQ;AAEZ,MAAI;AAEJ,SAAO,EAAEA,SAAQ,SAAS,QAAQ;AAChC,eAAW,SAASA,MAAK,CAAC;AAE1B,QAAI,SAASA,MAAK,GAAG;AACnB,eACE,WAAW,SAAY,SAASA,MAAK,IAAI,SAAS,MAAM,SAASA,MAAK;AAAA,IAC1E;AAAA,EACF;AAEA,SAAO,WAAW,SAAY,MAAM,UAAU,MAAM;AACtD;AAYA,SAAS,UAAU,MAAM;AACvB,aAAW,IAAI;AAEf,QAAM,WAAW,KAAK,YAAY,CAAC,MAAM;AAGzC,MAAI,QAAQ,gBAAgB,MAAM,CAAC,QAAQ;AAE3C,MAAI,MAAM,WAAW,KAAK,CAAC,UAAU;AACnC,YAAQ;AAAA,EACV;AAEA,MAAI,MAAM,SAAS,KAAK,KAAK,YAAY,KAAK,SAAS,CAAC,MAAM,IAAY;AACxE,aAAS;AAAA,EACX;AAEA,SAAO,WAAW,MAAM,QAAQ;AAClC;AAYA,SAAS,gBAAgB,MAAM,gBAAgB;AAC7C,MAAI,SAAS;AACb,MAAI,oBAAoB;AACxB,MAAI,YAAY;AAChB,MAAI,OAAO;AACX,MAAIA,SAAQ;AAEZ,MAAIC;AAEJ,MAAI;AAEJ,SAAO,EAAED,UAAS,KAAK,QAAQ;AAC7B,QAAIA,SAAQ,KAAK,QAAQ;AACvB,MAAAC,QAAO,KAAK,YAAYD,MAAK;AAAA,IAC/B,WAAWC,UAAS,IAAc;AAChC;AAAA,IACF,OAAO;AACL,MAAAA,QAAO;AAAA,IACT;AAEA,QAAIA,UAAS,IAAc;AACzB,UAAI,cAAcD,SAAQ,KAAK,SAAS,GAAG;AAAA,MAE3C,WAAW,cAAcA,SAAQ,KAAK,SAAS,GAAG;AAChD,YACE,OAAO,SAAS,KAChB,sBAAsB,KACtB,OAAO,YAAY,OAAO,SAAS,CAAC,MAAM,MAC1C,OAAO,YAAY,OAAO,SAAS,CAAC,MAAM,IAC1C;AACA,cAAI,OAAO,SAAS,GAAG;AACrB,6BAAiB,OAAO,YAAY,GAAG;AAEvC,gBAAI,mBAAmB,OAAO,SAAS,GAAG;AACxC,kBAAI,iBAAiB,GAAG;AACtB,yBAAS;AACT,oCAAoB;AAAA,cACtB,OAAO;AACL,yBAAS,OAAO,MAAM,GAAG,cAAc;AACvC,oCAAoB,OAAO,SAAS,IAAI,OAAO,YAAY,GAAG;AAAA,cAChE;AAEA,0BAAYA;AACZ,qBAAO;AACP;AAAA,YACF;AAAA,UACF,WAAW,OAAO,SAAS,GAAG;AAC5B,qBAAS;AACT,gCAAoB;AACpB,wBAAYA;AACZ,mBAAO;AACP;AAAA,UACF;AAAA,QACF;AAEA,YAAI,gBAAgB;AAClB,mBAAS,OAAO,SAAS,IAAI,SAAS,QAAQ;AAC9C,8BAAoB;AAAA,QACtB;AAAA,MACF,OAAO;AACL,YAAI,OAAO,SAAS,GAAG;AACrB,oBAAU,MAAM,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QACjD,OAAO;AACL,mBAAS,KAAK,MAAM,YAAY,GAAGA,MAAK;AAAA,QAC1C;AAEA,4BAAoBA,SAAQ,YAAY;AAAA,MAC1C;AAEA,kBAAYA;AACZ,aAAO;AAAA,IACT,WAAWC,UAAS,MAAgB,OAAO,IAAI;AAC7C;AAAA,IACF,OAAO;AACL,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;AAUA,SAAS,WAAW,MAAM;AACxB,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI;AAAA,MACR,qCAAqC,KAAK,UAAU,IAAI;AAAA,IAC1D;AAAA,EACF;AACF;;;ACpaO,IAAM,UAAU,EAAC,IAAG;AAE3B,SAAS,MAAM;AACb,SAAO;AACT;;;ACYO,SAAS,MAAM,eAAe;AACnC,SAAO;AAAA,IACL,kBAAkB,QAChB,OAAO,kBAAkB,YACzB,UAAU,iBACV,cAAc,QACd,cAAc,iBACd,cAAc;AAAA,IAEd,cAAc,SAAS;AAAA,EAC3B;AACF;;;AClBO,SAAS,UAAU,MAAM;AAC9B,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,IAAI,IAAI,IAAI;AAAA,EACrB,WAAW,CAAC,MAAM,IAAI,GAAG;AAEvB,UAAM,QAAQ,IAAI;AAAA,MAChB,iFACE,OACA;AAAA,IACJ;AACA,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,MAAI,KAAK,aAAa,SAAS;AAE7B,UAAM,QAAQ,IAAI,UAAU,gCAAgC;AAC5D,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,SAAO,oBAAoB,IAAI;AACjC;AAUA,SAAS,oBAAoB,KAAK;AAChC,MAAI,IAAI,aAAa,IAAI;AAEvB,UAAM,QAAQ,IAAI;AAAA,MAChB;AAAA,IACF;AACA,UAAM,OAAO;AACb,UAAM;AAAA,EACR;AAEA,QAAM,WAAW,IAAI;AACrB,MAAIC,SAAQ;AAEZ,SAAO,EAAEA,SAAQ,SAAS,QAAQ;AAChC,QACE,SAAS,YAAYA,MAAK,MAAM,MAChC,SAAS,YAAYA,SAAQ,CAAC,MAAM,IACpC;AACA,YAAM,QAAQ,SAAS,YAAYA,SAAQ,CAAC;AAC5C,UAAI,UAAU,MAAgB,UAAU,KAAe;AAErD,cAAM,QAAQ,IAAI;AAAA,UAChB;AAAA,QACF;AACA,cAAM,OAAO;AACb,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,SAAO,mBAAmB,QAAQ;AACpC;;;ACvDA,IAAM;AAAA;AAAA,EAA8B;AAAA,IAClC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA;AAEO,IAAM,QAAN,MAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBjB,YAAY,OAAO;AAEjB,QAAI;AAEJ,QAAI,CAAC,OAAO;AACV,gBAAU,CAAC;AAAA,IACb,WAAW,MAAM,KAAK,GAAG;AACvB,gBAAU,EAAC,MAAM,MAAK;AAAA,IACxB,WAAW,OAAO,UAAU,YAAY,aAAa,KAAK,GAAG;AAC3D,gBAAU,EAAC,MAAK;AAAA,IAClB,OAAO;AACL,gBAAU;AAAA,IACZ;AAWA,SAAK,MAAM,SAAS,UAAU,KAAK,QAAQ,IAAI;AAU/C,SAAK,OAAO,CAAC;AASb,SAAK,UAAU,CAAC;AAOhB,SAAK,WAAW,CAAC;AAOjB,SAAK;AAYL,SAAK;AAUL,SAAK;AASL,SAAK;AAIL,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,MAAM,QAAQ;AAC7B,YAAMC,SAAQ,MAAMD,MAAK;AAIzB,UACEC,UAAS,WACT,QAAQA,MAAK,MAAM,UACnB,QAAQA,MAAK,MAAM,MACnB;AAEA,aAAKA,MAAK,IAAIA,WAAU,YAAY,CAAC,GAAG,QAAQA,MAAK,CAAC,IAAI,QAAQA,MAAK;AAAA,MACzE;AAAA,IACF;AAGA,QAAI;AAGJ,SAAK,SAAS,SAAS;AAErB,UAAI,CAAC,MAAM,SAAS,KAAK,GAAG;AAE1B,aAAK,KAAK,IAAI,QAAQ,KAAK;AAAA,MAC7B;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,WAAW;AACb,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,SAAS,KAAK,IAAI,IAC1B;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,SAASC,WAAU;AACrB,mBAAeA,WAAU,UAAU;AACnC,eAAWA,WAAU,UAAU;AAC/B,SAAK,OAAO,QAAQ,KAAK,KAAK,WAAW,IAAIA,SAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,QAAQ,KAAK,IAAI,IACzB;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,IAAI,QAAQC,UAAS;AACnB,IAAAC,YAAW,KAAK,UAAU,SAAS;AACnC,SAAK,OAAO,QAAQ,KAAKD,YAAW,IAAI,KAAK,QAAQ;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,UAAU;AACZ,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,QAAQ,KAAK,IAAI,IACzB;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,QAAQE,UAAS;AACnB,eAAWA,UAAS,SAAS;AAC7B,IAAAD,YAAW,KAAK,SAAS,SAAS;AAElC,QAAIC,UAAS;AACX,UAAIA,SAAQ,YAAY,CAAC,MAAM,IAAc;AAC3C,cAAM,IAAI,MAAM,+BAA+B;AAAA,MACjD;AAEA,UAAIA,SAAQ,SAAS,KAAK,CAAC,GAAG;AAC5B,cAAM,IAAI,MAAM,wCAAwC;AAAA,MAC1D;AAAA,IACF;AAEA,SAAK,OAAO,QAAQ,KAAK,KAAK,SAAS,KAAK,QAAQA,YAAW,GAAG;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,MAAM;AACb,QAAI,MAAM,IAAI,GAAG;AACf,aAAO,UAAU,IAAI;AAAA,IACvB;AAEA,mBAAe,MAAM,MAAM;AAE3B,QAAI,KAAK,SAAS,MAAM;AACtB,WAAK,QAAQ,KAAK,IAAI;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO;AACT,WAAO,OAAO,KAAK,SAAS,WACxB,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAO,IACxC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,MAAM;AACb,mBAAe,MAAM,MAAM;AAC3B,eAAW,MAAM,MAAM;AACvB,SAAK,OAAO,QAAQ,KAAK,KAAK,WAAW,IAAI,QAAQ,KAAK,WAAW,GAAG;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+DA,KAAK,eAAe,wBAAwB,QAAQ;AAElD,UAAM,UAAU,KAAK,QAAQ,eAAe,wBAAwB,MAAM;AAE1E,YAAQ,QAAQ;AAEhB,UAAM;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4DA,KAAK,eAAe,wBAAwB,QAAQ;AAElD,UAAM,UAAU,KAAK,QAAQ,eAAe,wBAAwB,MAAM;AAE1E,YAAQ,QAAQ;AAEhB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4DA,QAAQ,eAAe,wBAAwB,QAAQ;AACrD,UAAM,UAAU,IAAI;AAAA;AAAA,MAElB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,QAAI,KAAK,MAAM;AACb,cAAQ,OAAO,KAAK,OAAO,MAAM,QAAQ;AACzC,cAAQ,OAAO,KAAK;AAAA,IACtB;AAEA,YAAQ,QAAQ;AAEhB,SAAK,SAAS,KAAK,OAAO;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,SAAS,UAAU;AACjB,QAAI,KAAK,UAAU,QAAW;AAC5B,aAAO;AAAA,IACT;AAEA,QAAI,OAAO,KAAK,UAAU,UAAU;AAClC,aAAO,KAAK;AAAA,IACd;AAEA,UAAM,UAAU,IAAI,YAAY,YAAY,MAAS;AACrD,WAAO,QAAQ,OAAO,KAAK,KAAK;AAAA,EAClC;AACF;AAYA,SAAS,WAAW,MAAMC,OAAM;AAC9B,MAAI,QAAQ,KAAK,SAAS,QAAQ,GAAG,GAAG;AACtC,UAAM,IAAI;AAAA,MACR,MAAMA,QAAO,yCAAyC,QAAQ,MAAM;AAAA,IACtE;AAAA,EACF;AACF;AAYA,SAAS,eAAe,MAAMA,OAAM;AAClC,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,MAAMA,QAAO,mBAAmB;AAAA,EAClD;AACF;AAYA,SAASF,YAAW,MAAME,OAAM;AAC9B,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,cAAcA,QAAO,iCAAiC;AAAA,EACxE;AACF;AAUA,SAAS,aAAa,OAAO;AAC3B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAAA,EACpB;AACF;;;ACloBO,IAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYP,SAAU,UAAU;AAClB,UAAM,OAAO;AACb,UAAM,SAAS,KAAK;AACpB,UAAM;AAAA;AAAA;AAAA;AAAA,MAGJ,OAAO;AAAA;AAET,UAAM,QAAQ,MAAM,QAAQ;AAE5B,UAAM,QAAQ,WAAY;AACxB,aAAO,MAAM,MAAM,OAAO,SAAS;AAAA,IACrC;AAEA,WAAO,eAAe,OAAO,KAAK;AAclC,WAAO;AAAA,EACT;AAAA;;;ARiUN,IAAMC,OAAM,CAAC,EAAE;AAeR,IAAM,YAAN,MAAM,mBAAkB,iBAAiB;AAAA;AAAA;AAAA;AAAA,EAI9C,cAAc;AAEZ,UAAM,MAAM;AAeZ,SAAK,WAAW;AAYhB,SAAK,SAAS;AAad,SAAK,YAAY,CAAC;AAalB,SAAK,WAAW;AAShB,SAAK,cAAc;AASnB,SAAK,SAAS;AASd,SAAK,YAAY,CAAC;AAUlB,SAAK,SAAS;AASd,SAAK,eAAe,OAAO;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO;AAEL,UAAM;AAAA;AAAA,MAEF,IAAI,WAAU;AAAA;AAElB,QAAIC,SAAQ;AAEZ,WAAO,EAAEA,SAAQ,KAAK,UAAU,QAAQ;AACtC,YAAM,WAAW,KAAK,UAAUA,MAAK;AACrC,kBAAY,IAAI,GAAG,QAAQ;AAAA,IAC7B;AAEA,gBAAY,SAAK,cAAAC,SAAO,MAAM,CAAC,GAAG,KAAK,SAAS,CAAC;AAEjD,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA6DA,KAAK,KAAK,OAAO;AACf,QAAI,OAAO,QAAQ,UAAU;AAE3B,UAAI,UAAU,WAAW,GAAG;AAC1B,uBAAe,QAAQ,KAAK,MAAM;AAClC,aAAK,UAAU,GAAG,IAAI;AACtB,eAAO;AAAA,MACT;AAGA,aAAQF,KAAI,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK,UAAU,GAAG,KAAM;AAAA,IACnE;AAGA,QAAI,KAAK;AACP,qBAAe,QAAQ,KAAK,MAAM;AAClC,WAAK,YAAY;AACjB,aAAO;AAAA,IACT;AAGA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,SAAS;AACP,QAAI,KAAK,QAAQ;AACf,aAAO;AAAA,IACT;AAKA,UAAM;AAAA;AAAA;AAAA,MAAyD;AAAA;AAE/D,WAAO,EAAE,KAAK,cAAc,KAAK,UAAU,QAAQ;AACjD,YAAM,CAAC,UAAU,GAAG,OAAO,IAAI,KAAK,UAAU,KAAK,WAAW;AAE9D,UAAI,QAAQ,CAAC,MAAM,OAAO;AACxB;AAAA,MACF;AAEA,UAAI,QAAQ,CAAC,MAAM,MAAM;AACvB,gBAAQ,CAAC,IAAI;AAAA,MACf;AAEA,YAAM,cAAc,SAAS,KAAK,MAAM,GAAG,OAAO;AAElD,UAAI,OAAO,gBAAgB,YAAY;AACrC,aAAK,aAAa,IAAI,WAAW;AAAA,MACnC;AAAA,IACF;AAEA,SAAK,SAAS;AACd,SAAK,cAAc,OAAO;AAE1B,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,MAAM;AACV,SAAK,OAAO;AACZ,UAAM,WAAW,MAAM,IAAI;AAC3B,UAAM,SAAS,KAAK,UAAU,KAAK;AACnC,iBAAa,SAAS,MAAM;AAC5B,WAAO,OAAO,OAAO,QAAQ,GAAG,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA4CA,QAAQ,MAAM,MAAM;AAClB,UAAM,OAAO;AAEb,SAAK,OAAO;AACZ,iBAAa,WAAW,KAAK,UAAU,KAAK,MAAM;AAClD,mBAAe,WAAW,KAAK,YAAY,KAAK,QAAQ;AAExD,WAAO,OAAO,SAAS,QAAW,IAAI,IAAI,IAAI,QAAQ,QAAQ;AAQ9D,aAAS,SAAS,SAAS,QAAQ;AACjC,YAAM,WAAW,MAAM,IAAI;AAG3B,YAAM;AAAA;AAAA;AAAA,QAEsB,KAAK,MAAM,QAAQ;AAAA;AAG/C,WAAK,IAAI,WAAW,UAAU,SAAU,OAAO,MAAMG,OAAM;AACzD,YAAI,SAAS,CAAC,QAAQ,CAACA,OAAM;AAC3B,iBAAO,SAAS,KAAK;AAAA,QACvB;AAIA,cAAM;AAAA;AAAA;AAAA,UAEsB;AAAA;AAG5B,cAAM,gBAAgB,KAAK,UAAU,aAAaA,KAAI;AAEtD,YAAI,gBAAgB,aAAa,GAAG;AAClC,UAAAA,MAAK,QAAQ;AAAA,QACf,OAAO;AACL,UAAAA,MAAK,SAAS;AAAA,QAChB;AAEA;AAAA,UAAS;AAAA;AAAA,UAAsDA;AAAA,QAAK;AAAA,MACtE,CAAC;AAOD,eAAS,SAAS,OAAOA,OAAM;AAC7B,YAAI,SAAS,CAACA,OAAM;AAClB,iBAAO,KAAK;AAAA,QACd,WAAW,SAAS;AAClB,kBAAQA,KAAI;AAAA,QACd,OAAO;AACL,aAAO,MAAM,uCAAuC;AACpD,eAAK,QAAWA,KAAI;AAAA,QACtB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiCA,YAAY,MAAM;AAEhB,QAAI,WAAW;AAEf,QAAI;AAEJ,SAAK,OAAO;AACZ,iBAAa,eAAe,KAAK,UAAU,KAAK,MAAM;AACtD,mBAAe,eAAe,KAAK,YAAY,KAAK,QAAQ;AAE5D,SAAK,QAAQ,MAAM,QAAQ;AAC3B,eAAW,eAAe,WAAW,QAAQ;AAC7C,OAAO,QAAQ,6CAA6C;AAE5D,WAAO;AAKP,aAAS,SAAS,OAAOA,OAAM;AAC7B,iBAAW;AACX,WAAK,KAAK;AACV,eAASA;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAwCA,IAAI,MAAM,MAAM,MAAM;AACpB,eAAW,IAAI;AACf,SAAK,OAAO;AAEZ,UAAM,eAAe,KAAK;AAE1B,QAAI,CAAC,QAAQ,OAAO,SAAS,YAAY;AACvC,aAAO;AACP,aAAO;AAAA,IACT;AAEA,WAAO,OAAO,SAAS,QAAW,IAAI,IAAI,IAAI,QAAQ,QAAQ;AAW9D,aAAS,SAAS,SAAS,QAAQ;AACjC;AAAA,QACE,OAAO,SAAS;AAAA,QAChB;AAAA,MACF;AACA,YAAM,WAAW,MAAM,IAAI;AAC3B,mBAAa,IAAI,MAAM,UAAU,QAAQ;AAQzC,eAAS,SAAS,OAAO,YAAYA,OAAM;AACzC,cAAM;AAAA;AAAA,UAEF,cAAc;AAAA;AAGlB,YAAI,OAAO;AACT,iBAAO,KAAK;AAAA,QACd,WAAW,SAAS;AAClB,kBAAQ,aAAa;AAAA,QACvB,OAAO;AACL,aAAO,MAAM,uCAAuC;AACpD,eAAK,QAAW,eAAeA,KAAI;AAAA,QACrC;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,QAAQ,MAAM,MAAM;AAElB,QAAI,WAAW;AAEf,QAAI;AAEJ,SAAK,IAAI,MAAM,MAAM,QAAQ;AAE7B,eAAW,WAAW,OAAO,QAAQ;AACrC,OAAO,QAAQ,6CAA6C;AAC5D,WAAO;AAKP,aAAS,SAAS,OAAOC,OAAM;AAC7B,WAAK,KAAK;AACV,eAASA;AACT,iBAAW;AAAA,IACb;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+BA,UAAU,MAAM,MAAM;AACpB,SAAK,OAAO;AACZ,UAAM,WAAW,MAAM,IAAI;AAC3B,UAAMC,YAAW,KAAK,YAAY,KAAK;AACvC,mBAAe,aAAaA,SAAQ;AACpC,eAAW,IAAI;AAEf,WAAOA,UAAS,MAAM,QAAQ;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2DA,IAAI,UAAU,YAAY;AACxB,UAAM,YAAY,KAAK;AACvB,UAAM,YAAY,KAAK;AAEvB,mBAAe,OAAO,KAAK,MAAM;AAEjC,QAAI,UAAU,QAAQ,UAAU,QAAW;AAAA,IAE3C,WAAW,OAAO,UAAU,YAAY;AACtC,gBAAU,OAAO,UAAU;AAAA,IAC7B,WAAW,OAAO,UAAU,UAAU;AACpC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAQ,KAAK;AAAA,MACf,OAAO;AACL,kBAAU,KAAK;AAAA,MACjB;AAAA,IACF,OAAO;AACL,YAAM,IAAI,UAAU,iCAAiC,QAAQ,GAAG;AAAA,IAClE;AAEA,WAAO;AAMP,aAAS,IAAIC,QAAO;AAClB,UAAI,OAAOA,WAAU,YAAY;AAC/B,kBAAUA,QAAO,CAAC,CAAC;AAAA,MACrB,WAAW,OAAOA,WAAU,UAAU;AACpC,YAAI,MAAM,QAAQA,MAAK,GAAG;AACxB,gBAAM,CAAC,QAAQ,GAAGC,WAAU;AAAA;AAAA,YACkBD;AAAA;AAC9C,oBAAU,QAAQC,WAAU;AAAA,QAC9B,OAAO;AACL,oBAAUD,MAAK;AAAA,QACjB;AAAA,MACF,OAAO;AACL,cAAM,IAAI,UAAU,iCAAiCA,SAAQ,GAAG;AAAA,MAClE;AAAA,IACF;AAMA,aAAS,UAAU,QAAQ;AACzB,UAAI,EAAE,aAAa,WAAW,EAAE,cAAc,SAAS;AACrD,cAAM,IAAI;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,OAAO,OAAO;AAEtB,UAAI,OAAO,UAAU;AACnB,kBAAU,eAAW,cAAAJ,SAAO,MAAM,UAAU,UAAU,OAAO,QAAQ;AAAA,MACvE;AAAA,IACF;AAMA,aAAS,QAAQ,SAAS;AACxB,UAAID,SAAQ;AAEZ,UAAI,YAAY,QAAQ,YAAY,QAAW;AAAA,MAE/C,WAAW,MAAM,QAAQ,OAAO,GAAG;AACjC,eAAO,EAAEA,SAAQ,QAAQ,QAAQ;AAC/B,gBAAM,QAAQ,QAAQA,MAAK;AAC3B,cAAI,KAAK;AAAA,QACX;AAAA,MACF,OAAO;AACL,cAAM,IAAI,UAAU,sCAAsC,UAAU,GAAG;AAAA,MACzE;AAAA,IACF;AAOA,aAAS,UAAU,QAAQM,aAAY;AACrC,UAAIN,SAAQ;AACZ,UAAI,aAAa;AAEjB,aAAO,EAAEA,SAAQ,UAAU,QAAQ;AACjC,YAAI,UAAUA,MAAK,EAAE,CAAC,MAAM,QAAQ;AAClC,uBAAaA;AACb;AAAA,QACF;AAAA,MACF;AAEA,UAAI,eAAe,IAAI;AACrB,kBAAU,KAAK,CAAC,QAAQ,GAAGM,WAAU,CAAC;AAAA,MACxC,WAGSA,YAAW,SAAS,GAAG;AAC9B,YAAI,CAAC,SAAS,GAAG,IAAI,IAAIA;AACzB,cAAM,iBAAiB,UAAU,UAAU,EAAE,CAAC;AAC9C,YAAI,cAAW,cAAc,KAAK,cAAW,OAAO,GAAG;AACrD,wBAAU,cAAAL,SAAO,MAAM,gBAAgB,OAAO;AAAA,QAChD;AAEA,kBAAU,UAAU,IAAI,CAAC,QAAQ,SAAS,GAAG,IAAI;AAAA,MACnD;AAAA,IACF;AAAA,EACF;AACF;AA8BO,IAAM,UAAU,IAAI,UAAU,EAAE,OAAO;AAS9C,SAAS,aAAaM,OAAM,OAAO;AACjC,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,IAAI,UAAU,aAAaA,QAAO,oBAAoB;AAAA,EAC9D;AACF;AASA,SAAS,eAAeA,OAAM,OAAO;AACnC,MAAI,OAAO,UAAU,YAAY;AAC/B,UAAM,IAAI,UAAU,aAAaA,QAAO,sBAAsB;AAAA,EAChE;AACF;AASA,SAAS,eAAeA,OAAM,QAAQ;AACpC,MAAI,QAAQ;AACV,UAAM,IAAI;AAAA,MACR,kBACEA,QACA;AAAA,IACJ;AAAA,EACF;AACF;AAQA,SAAS,WAAW,MAAM;AAGxB,MAAI,CAAC,cAAW,IAAI,KAAK,OAAO,KAAK,SAAS,UAAU;AACtD,UAAM,IAAI,UAAU,yBAAyB,OAAO,GAAG;AAAA,EAEzD;AACF;AAUA,SAAS,WAAWA,OAAM,WAAW,UAAU;AAC7C,MAAI,CAAC,UAAU;AACb,UAAM,IAAI;AAAA,MACR,MAAMA,QAAO,4BAA4B,YAAY;AAAA,IACvD;AAAA,EACF;AACF;AAMA,SAAS,MAAM,OAAO;AACpB,SAAO,gBAAgB,KAAK,IAAI,QAAQ,IAAI,MAAM,KAAK;AACzD;AAMA,SAAS,gBAAgB,OAAO;AAC9B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,aAAa,SACb,cAAc;AAAA,EAClB;AACF;AAMA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,OAAO,UAAU,YAAYC,cAAa,KAAK;AACxD;AAUA,SAASA,cAAa,OAAO;AAC3B,SAAO;AAAA,IACL,SACE,OAAO,UAAU,YACjB,gBAAgB,SAChB,gBAAgB;AAAA,EACpB;AACF;;;A5CtrCA,IAAM,YACJ;AAGF,IAAM,eAAe,CAAC;AAEtB,IAAM,2BAA2B,EAAC,oBAAoB,KAAI;AAC1D,IAAM,eAAe;AAIrB,IAAM,eAAe;AAAA,EACnB,EAAC,MAAM,cAAc,IAAI,uCAAsC;AAAA,EAC/D,EAAC,MAAM,sBAAsB,IAAI,uCAAsC;AAAA,EACvE;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,EAAC,MAAM,aAAa,IAAI,mBAAkB;AAAA,EAC1C;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,IACJ,IAAI;AAAA,EACN;AAAA,EACA,EAAC,MAAM,cAAc,IAAI,uCAAsC;AAAA,EAC/D,EAAC,MAAM,uBAAuB,IAAI,8BAA6B;AAAA,EAC/D;AAAA,IACE,MAAM;AAAA,IACN,IAAI;AAAA,EACN;AAAA,EACA,EAAC,MAAM,cAAc,IAAI,oBAAmB;AAAA,EAC5C,EAAC,MAAM,WAAW,IAAI,mCAAmC,IAAI,gBAAe;AAAA,EAC5E,EAAC,MAAM,gBAAgB,IAAI,uBAAsB;AAAA,EACjD,EAAC,MAAM,aAAa,IAAI,kCAAkC,IAAI,aAAY;AAAA,EAC1E,EAAC,MAAM,UAAU,IAAI,6BAA6B,IAAI,WAAU;AAAA,EAChE,EAAC,MAAM,aAAa,IAAI,oBAAmB;AAAA,EAC3C,EAAC,MAAM,qBAAqB,IAAI,qBAAqB,IAAI,eAAc;AAAA,EACvE,EAAC,MAAM,oBAAoB,IAAI,qBAAqB,IAAI,eAAc;AACxE;AAcO,SAAS,SAAS,SAAS;AAChC,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,SAAO,KAAK,UAAU,QAAQ,UAAU,MAAM,IAAI,GAAG,IAAI,GAAG,OAAO;AACrE;AAeA,eAAsB,cAAc,SAAS;AAC3C,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,OAAO,WAAW,OAAO;AAC/B,QAAM,OAAO,MAAM,UAAU,IAAI,UAAU,MAAM,IAAI,GAAG,IAAI;AAC5D,SAAO,KAAK,MAAM,OAAO;AAC3B;AAeO,SAAS,cAAc,SAAS;AACrC,QAAM,YAAY,gBAAgB,OAAO;AACzC,QAAM,CAAC,OAAO,QAAQ,QAAI;AAAA;AAAA,IACU;AAAA,EACpC;AACA,QAAM,CAAC,MAAM,OAAO,QAAI;AAAA;AAAA,IAA0C;AAAA,EAAU;AAE5E;AAAA,IACE,WAAY;AACV,UAAI,YAAY;AAChB,YAAM,OAAO,WAAW,OAAO;AAE/B,gBAAU,IAAI,UAAU,MAAM,IAAI,GAAG,MAAM,SAAUC,QAAOC,OAAM;AAChE,YAAI,CAAC,WAAW;AACd,mBAASD,MAAK;AACd,kBAAQC,KAAI;AAAA,QACd;AAAA,MACF,CAAC;AAMD,aAAO,WAAY;AACjB,oBAAY;AAAA,MACd;AAAA,IACF;AAAA,IACA;AAAA,MACE,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAEA,MAAI;AAAO,UAAM;AAEjB,SAAO,OAAO,KAAK,MAAM,OAAO,IAAI,QAAQ;AAC9C;AAUA,SAAS,gBAAgB,SAAS;AAChC,QAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,QAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,QAAM,sBAAsB,QAAQ,sBAChC,EAAC,GAAG,QAAQ,qBAAqB,GAAG,yBAAwB,IAC5D;AAEJ,QAAM,YAAY,QAAQ,EACvB,IAAI,WAAW,EACf,IAAI,aAAa,EACjB,IAAI,cAAc,mBAAmB,EACrC,IAAI,aAAa;AAEpB,SAAO;AACT;AAUA,SAAS,WAAW,SAAS;AAC3B,QAAM,WAAW,QAAQ,YAAY;AACrC,QAAM,OAAO,IAAI,MAAM;AAEvB,MAAI,OAAO,aAAa,UAAU;AAChC,SAAK,QAAQ;AAAA,EACf,OAAO;AACL;AAAA,MACE,uBACE,WACA;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;AAYA,SAAS,KAAK,MAAM,SAAS;AAC3B,QAAM,kBAAkB,QAAQ;AAChC,QAAM,eAAe,QAAQ;AAC7B,QAAM,aAAa,QAAQ;AAC3B,QAAM,qBAAqB,QAAQ;AACnC,QAAM,WAAW,QAAQ;AACzB,QAAM,mBAAmB,QAAQ;AACjC,QAAM,eAAe,QAAQ,gBAAgB;AAE7C,aAAW,eAAe,cAAc;AACtC,QAAI,OAAO,OAAO,SAAS,YAAY,IAAI,GAAG;AAC5C;AAAA,QACE,iBACE,YAAY,OACZ,cACC,YAAY,KACT,UAAU,YAAY,KAAK,cAC3B,eACJ,YACA,YACA,MACA,YAAY,KACZ;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAEA,MAAI,mBAAmB,oBAAoB;AACzC;AAAA,MACE;AAAA,IACF;AAAA,EACF;AAEA,QAAM,MAAM,SAAS;AAErB,SAAO,aAAa,MAAM;AAAA,IACxB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,UAAU;AAAA,EACZ,CAAC;AAGD,WAAS,UAAU,MAAMC,QAAO,QAAQ;AACtC,QAAI,KAAK,SAAS,SAAS,UAAU,OAAOA,WAAU,UAAU;AAC9D,UAAI,UAAU;AACZ,eAAO,SAAS,OAAOA,QAAO,CAAC;AAAA,MACjC,OAAO;AACL,eAAO,SAASA,MAAK,IAAI,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAK;AAAA,MAC3D;AAEA,aAAOA;AAAA,IACT;AAEA,QAAI,KAAK,SAAS,WAAW;AAE3B,UAAI;AAEJ,WAAK,OAAO,eAAe;AACzB,YACE,OAAO,OAAO,eAAe,GAAG,KAChC,OAAO,OAAO,KAAK,YAAY,GAAG,GAClC;AACA,gBAAM,QAAQ,KAAK,WAAW,GAAG;AACjC,gBAAM,OAAO,cAAc,GAAG;AAC9B,cAAI,SAAS,QAAQ,KAAK,SAAS,KAAK,OAAO,GAAG;AAChD,iBAAK,WAAW,GAAG,IAAI,aAAa,OAAO,SAAS,EAAE,GAAG,KAAK,IAAI;AAAA,UACpE;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,QAAI,KAAK,SAAS,WAAW;AAC3B,UAAI,SAAS,kBACT,CAAC,gBAAgB,SAAS,KAAK,OAAO,IACtC,qBACE,mBAAmB,SAAS,KAAK,OAAO,IACxC;AAEN,UAAI,CAAC,UAAU,gBAAgB,OAAOA,WAAU,UAAU;AACxD,iBAAS,CAAC,aAAa,MAAMA,QAAO,MAAM;AAAA,MAC5C;AAEA,UAAI,UAAU,UAAU,OAAOA,WAAU,UAAU;AACjD,YAAI,oBAAoB,KAAK,UAAU;AACrC,iBAAO,SAAS,OAAOA,QAAO,GAAG,GAAG,KAAK,QAAQ;AAAA,QACnD,OAAO;AACL,iBAAO,SAAS,OAAOA,QAAO,CAAC;AAAA,QACjC;AAEA,eAAOA;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAWO,SAAS,oBAAoB,OAAO;AAIzC,QAAM,QAAQ,MAAM,QAAQ,GAAG;AAC/B,QAAM,eAAe,MAAM,QAAQ,GAAG;AACtC,QAAM,aAAa,MAAM,QAAQ,GAAG;AACpC,QAAM,QAAQ,MAAM,QAAQ,GAAG;AAE/B;AAAA;AAAA,IAEE,UAAU;AAAA,IAET,UAAU,MAAM,QAAQ,SACxB,iBAAiB,MAAM,QAAQ,gBAC/B,eAAe,MAAM,QAAQ;AAAA,IAE9B,aAAa,KAAK,MAAM,MAAM,GAAG,KAAK,CAAC;AAAA,IACvC;AACA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;", "names": ["position", "start", "whitespace", "re", "parse", "name", "createDebug", "disable", "debug", "index", "extend", "search", "name", "index", "isArray", "isPlainObject", "setProperty", "getProperty", "name", "extend", "name", "re", "position", "point", "index", "start", "jsx", "jsxs", "_", "point", "name", "index", "stringify", "styleToJs", "content", "code", "document", "start", "code", "point", "index", "ok", "code", "text", "start", "code", "list", "index", "document", "flow", "string", "text", "document", "flow", "string", "text", "createDebug", "point", "code", "start", "list", "ok", "from", "index", "content", "document", "text", "start", "code", "own", "link", "heading", "blockQuote", "codeText", "definition", "emphasis", "hardBreak", "html", "image", "listItem", "list", "paragraph", "strong", "thematicBreak", "compile", "index", "point", "start", "data", "text", "string", "extension", "index", "link", "html", "definition", "text", "definition", "paragraph", "index", "list", "index", "root", "start", "result", "search", "start", "code", "text", "thematicBreak", "html", "list", "root", "text", "thematicBreak", "definition", "content", "listItem", "own", "emptyOptions", "handlers", "one", "values", "index", "code", "values", "index", "wrap", "extname", "start", "index", "code", "index", "index", "field", "basename", "dirname", "assertPath", "extname", "name", "own", "index", "extend", "file", "tree", "compiler", "value", "parameters", "name", "isUint8Array", "error", "tree", "index"]}