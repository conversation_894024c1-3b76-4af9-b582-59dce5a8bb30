import {
  esm_default,
  position
} from "./chunk-WA7BIZIN.js";
import "./chunk-C3M7BXFS.js";

// node_modules/hast-util-sanitize/lib/schema.js
var aria = ["ariaDescribedBy", "ariaLabel", "ariaLabelledBy"];
var defaultSchema = {
  ancestors: {
    tbody: ["table"],
    td: ["table"],
    th: ["table"],
    thead: ["table"],
    tfoot: ["table"],
    tr: ["table"]
  },
  attributes: {
    a: [
      ...aria,
      // Note: these 3 are used by GFM footnotes, they do work on all links.
      "dataFootnoteBackref",
      "dataFootnoteRef",
      ["className", "data-footnote-backref"],
      "href"
    ],
    blockquote: ["cite"],
    // Note: this class is not normally allowed by GH, when manually writing
    // `code` as HTML in markdown, they adds it some other way.
    // We can’t do that, so we have to allow it.
    code: [["className", /^language-./]],
    del: ["cite"],
    div: ["itemScope", "itemType"],
    dl: [...aria],
    // Note: this is used by GFM footnotes.
    h2: [["className", "sr-only"]],
    img: [...aria, "longDesc", "src"],
    // Note: `input` is not normally allowed by GH, when manually writing
    // it in markdown, they add it from tasklists some other way.
    // We can’t do that, so we have to allow it.
    input: [
      ["disabled", true],
      ["type", "checkbox"]
    ],
    ins: ["cite"],
    // Note: this class is not normally allowed by GH, when manually writing
    // `li` as HTML in markdown, they adds it some other way.
    // We can’t do that, so we have to allow it.
    li: [["className", "task-list-item"]],
    // Note: this class is not normally allowed by GH, when manually writing
    // `ol` as HTML in markdown, they adds it some other way.
    // We can’t do that, so we have to allow it.
    ol: [...aria, ["className", "contains-task-list"]],
    q: ["cite"],
    section: ["dataFootnotes", ["className", "footnotes"]],
    source: ["srcSet"],
    summary: [...aria],
    table: [...aria],
    // Note: this class is not normally allowed by GH, when manually writing
    // `ol` as HTML in markdown, they adds it some other way.
    // We can’t do that, so we have to allow it.
    ul: [...aria, ["className", "contains-task-list"]],
    "*": [
      "abbr",
      "accept",
      "acceptCharset",
      "accessKey",
      "action",
      "align",
      "alt",
      "axis",
      "border",
      "cellPadding",
      "cellSpacing",
      "char",
      "charOff",
      "charSet",
      "checked",
      "clear",
      "colSpan",
      "color",
      "cols",
      "compact",
      "coords",
      "dateTime",
      "dir",
      // Note: `disabled` is technically allowed on all elements by GH.
      // But it is useless on everything except `input`.
      // Because `input`s are normally not allowed, but we allow them for
      // checkboxes due to tasklists, we allow `disabled` only there.
      "encType",
      "frame",
      "hSpace",
      "headers",
      "height",
      "hrefLang",
      "htmlFor",
      "id",
      "isMap",
      "itemProp",
      "label",
      "lang",
      "maxLength",
      "media",
      "method",
      "multiple",
      "name",
      "noHref",
      "noShade",
      "noWrap",
      "open",
      "prompt",
      "readOnly",
      "rev",
      "rowSpan",
      "rows",
      "rules",
      "scope",
      "selected",
      "shape",
      "size",
      "span",
      "start",
      "summary",
      "tabIndex",
      "title",
      "useMap",
      "vAlign",
      "value",
      "width"
    ]
  },
  clobber: ["ariaDescribedBy", "ariaLabelledBy", "id", "name"],
  clobberPrefix: "user-content-",
  protocols: {
    cite: ["http", "https"],
    href: ["http", "https", "irc", "ircs", "mailto", "xmpp"],
    longDesc: ["http", "https"],
    src: ["http", "https"]
  },
  required: {
    input: { disabled: true, type: "checkbox" }
  },
  strip: ["script"],
  tagNames: [
    "a",
    "b",
    "blockquote",
    "br",
    "code",
    "dd",
    "del",
    "details",
    "div",
    "dl",
    "dt",
    "em",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "hr",
    "i",
    "img",
    // Note: `input` is not normally allowed by GH, when manually writing
    // it in markdown, they add it from tasklists some other way.
    // We can’t do that, so we have to allow it.
    "input",
    "ins",
    "kbd",
    "li",
    "ol",
    "p",
    "picture",
    "pre",
    "q",
    "rp",
    "rt",
    "ruby",
    "s",
    "samp",
    "section",
    "source",
    "span",
    "strike",
    "strong",
    "sub",
    "summary",
    "sup",
    "table",
    "tbody",
    "td",
    "tfoot",
    "th",
    "thead",
    "tr",
    "tt",
    "ul",
    "var"
  ]
};

// node_modules/hast-util-sanitize/lib/index.js
var own = {}.hasOwnProperty;
function sanitize(node, options) {
  let result = { type: "root", children: [] };
  const state = {
    schema: options ? { ...defaultSchema, ...options } : defaultSchema,
    stack: []
  };
  const replace = transform(state, node);
  if (replace) {
    if (Array.isArray(replace)) {
      if (replace.length === 1) {
        result = replace[0];
      } else {
        result.children = replace;
      }
    } else {
      result = replace;
    }
  }
  return result;
}
function transform(state, node) {
  if (node && typeof node === "object") {
    const unsafe = (
      /** @type {Record<string, Readonly<unknown>>} */
      node
    );
    const type = typeof unsafe.type === "string" ? unsafe.type : "";
    switch (type) {
      case "comment": {
        return comment(state, unsafe);
      }
      case "doctype": {
        return doctype(state, unsafe);
      }
      case "element": {
        return element(state, unsafe);
      }
      case "root": {
        return root(state, unsafe);
      }
      case "text": {
        return text(state, unsafe);
      }
      default:
    }
  }
}
function comment(state, unsafe) {
  if (state.schema.allowComments) {
    const result = typeof unsafe.value === "string" ? unsafe.value : "";
    const index = result.indexOf("-->");
    const value = index < 0 ? result : result.slice(0, index);
    const node = { type: "comment", value };
    patch(node, unsafe);
    return node;
  }
}
function doctype(state, unsafe) {
  if (state.schema.allowDoctypes) {
    const node = { type: "doctype" };
    patch(node, unsafe);
    return node;
  }
}
function element(state, unsafe) {
  const name = typeof unsafe.tagName === "string" ? unsafe.tagName : "";
  state.stack.push(name);
  const content = (
    /** @type {Array<ElementContent>} */
    children(state, unsafe.children)
  );
  const properties_ = properties(state, unsafe.properties);
  state.stack.pop();
  let safeElement = false;
  if (name && name !== "*" && (!state.schema.tagNames || state.schema.tagNames.includes(name))) {
    safeElement = true;
    if (state.schema.ancestors && own.call(state.schema.ancestors, name)) {
      const ancestors = state.schema.ancestors[name];
      let index = -1;
      safeElement = false;
      while (++index < ancestors.length) {
        if (state.stack.includes(ancestors[index])) {
          safeElement = true;
        }
      }
    }
  }
  if (!safeElement) {
    return state.schema.strip && !state.schema.strip.includes(name) ? content : void 0;
  }
  const node = {
    type: "element",
    tagName: name,
    properties: properties_,
    children: content
  };
  patch(node, unsafe);
  return node;
}
function root(state, unsafe) {
  const content = (
    /** @type {Array<RootContent>} */
    children(state, unsafe.children)
  );
  const node = { type: "root", children: content };
  patch(node, unsafe);
  return node;
}
function text(_, unsafe) {
  const value = typeof unsafe.value === "string" ? unsafe.value : "";
  const node = { type: "text", value };
  patch(node, unsafe);
  return node;
}
function children(state, children2) {
  const results = [];
  if (Array.isArray(children2)) {
    const childrenUnknown = (
      /** @type {Array<Readonly<unknown>>} */
      children2
    );
    let index = -1;
    while (++index < childrenUnknown.length) {
      const value = transform(state, childrenUnknown[index]);
      if (value) {
        if (Array.isArray(value)) {
          results.push(...value);
        } else {
          results.push(value);
        }
      }
    }
  }
  return results;
}
function properties(state, properties2) {
  const tagName = state.stack[state.stack.length - 1];
  const attributes = state.schema.attributes;
  const required = state.schema.required;
  const specific = attributes && own.call(attributes, tagName) ? attributes[tagName] : void 0;
  const defaults = attributes && own.call(attributes, "*") ? attributes["*"] : void 0;
  const properties_ = (
    /** @type {Readonly<Record<string, Readonly<unknown>>>} */
    properties2 && typeof properties2 === "object" ? properties2 : {}
  );
  const result = {};
  let key;
  for (key in properties_) {
    if (own.call(properties_, key)) {
      const unsafe = properties_[key];
      let safe = propertyValue(
        state,
        findDefinition(specific, key),
        key,
        unsafe
      );
      if (safe === null || safe === void 0) {
        safe = propertyValue(state, findDefinition(defaults, key), key, unsafe);
      }
      if (safe !== null && safe !== void 0) {
        result[key] = safe;
      }
    }
  }
  if (required && own.call(required, tagName)) {
    const properties3 = required[tagName];
    for (key in properties3) {
      if (own.call(properties3, key) && !own.call(result, key)) {
        result[key] = properties3[key];
      }
    }
  }
  return result;
}
function propertyValue(state, definition, key, value) {
  return definition ? Array.isArray(value) ? propertyValueMany(state, definition, key, value) : propertyValuePrimitive(state, definition, key, value) : void 0;
}
function propertyValueMany(state, definition, key, values) {
  let index = -1;
  const result = [];
  while (++index < values.length) {
    const value = propertyValuePrimitive(state, definition, key, values[index]);
    if (typeof value === "number" || typeof value === "string") {
      result.push(value);
    }
  }
  return result;
}
function propertyValuePrimitive(state, definition, key, value) {
  if (typeof value !== "boolean" && typeof value !== "number" && typeof value !== "string") {
    return;
  }
  if (!safeProtocol(state, key, value)) {
    return;
  }
  if (typeof definition === "object" && definition.length > 1) {
    let ok = false;
    let index = 0;
    while (++index < definition.length) {
      const allowed = definition[index];
      if (allowed && typeof allowed === "object" && "flags" in allowed) {
        if (allowed.test(String(value))) {
          ok = true;
          break;
        }
      } else if (allowed === value) {
        ok = true;
        break;
      }
    }
    if (!ok)
      return;
  }
  return state.schema.clobber && state.schema.clobberPrefix && state.schema.clobber.includes(key) ? state.schema.clobberPrefix + value : value;
}
function safeProtocol(state, key, value) {
  const protocols = state.schema.protocols && own.call(state.schema.protocols, key) ? state.schema.protocols[key] : void 0;
  if (!protocols || protocols.length === 0) {
    return true;
  }
  const url = String(value);
  const colon = url.indexOf(":");
  const questionMark = url.indexOf("?");
  const numberSign = url.indexOf("#");
  const slash = url.indexOf("/");
  if (colon < 0 || // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.
  slash > -1 && colon > slash || questionMark > -1 && colon > questionMark || numberSign > -1 && colon > numberSign) {
    return true;
  }
  let index = -1;
  while (++index < protocols.length) {
    const protocol = protocols[index];
    if (colon === protocol.length && url.slice(0, protocol.length) === protocol) {
      return true;
    }
  }
  return false;
}
function patch(node, unsafe) {
  const cleanPosition = position(
    // @ts-expect-error: looks like a node.
    unsafe
  );
  if (unsafe.data) {
    node.data = esm_default(unsafe.data);
  }
  if (cleanPosition)
    node.position = cleanPosition;
}
function findDefinition(definitions, key) {
  let dataDefault;
  let index = -1;
  if (definitions) {
    while (++index < definitions.length) {
      const entry = definitions[index];
      const name = typeof entry === "string" ? entry : entry[0];
      if (name === key) {
        return entry;
      }
      if (name === "data*")
        dataDefault = entry;
    }
  }
  if (key.length > 4 && key.slice(0, 4).toLowerCase() === "data") {
    return dataDefault;
  }
}

// node_modules/rehype-sanitize/lib/index.js
function rehypeSanitize(options) {
  return function(tree) {
    const result = (
      /** @type {Root} */
      sanitize(tree, options)
    );
    return result;
  };
}
export {
  rehypeSanitize as default,
  defaultSchema
};
//# sourceMappingURL=rehype-sanitize.js.map
