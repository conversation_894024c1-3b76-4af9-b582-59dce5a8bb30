{"version": 3, "sources": ["../../jose/dist/webapi/util/base64url.js", "../../jose/dist/webapi/lib/buffer_utils.js", "../../jose/dist/webapi/lib/base64.js", "../../jose/dist/webapi/util/errors.js", "../../jose/dist/webapi/lib/iv.js", "../../jose/dist/webapi/lib/check_iv_length.js", "../../jose/dist/webapi/lib/check_cek_length.js", "../../jose/dist/webapi/lib/crypto_key.js", "../../jose/dist/webapi/lib/invalid_key_input.js", "../../jose/dist/webapi/lib/is_key_like.js", "../../jose/dist/webapi/lib/decrypt.js", "../../jose/dist/webapi/lib/is_disjoint.js", "../../jose/dist/webapi/lib/is_object.js", "../../jose/dist/webapi/lib/aeskw.js", "../../jose/dist/webapi/lib/digest.js", "../../jose/dist/webapi/lib/ecdhes.js", "../../jose/dist/webapi/lib/pbes2kw.js", "../../jose/dist/webapi/lib/check_key_length.js", "../../jose/dist/webapi/lib/rsaes.js", "../../jose/dist/webapi/lib/cek.js", "../../jose/dist/webapi/lib/asn1.js", "../../jose/dist/webapi/lib/jwk_to_key.js", "../../jose/dist/webapi/key/import.js", "../../jose/dist/webapi/lib/encrypt.js", "../../jose/dist/webapi/lib/aesgcmkw.js", "../../jose/dist/webapi/lib/decrypt_key_management.js", "../../jose/dist/webapi/lib/validate_crit.js", "../../jose/dist/webapi/lib/validate_algorithms.js", "../../jose/dist/webapi/lib/is_jwk.js", "../../jose/dist/webapi/lib/normalize_key.js", "../../jose/dist/webapi/lib/check_key_type.js", "../../jose/dist/webapi/jwe/flattened/decrypt.js", "../../jose/dist/webapi/jwe/compact/decrypt.js", "../../jose/dist/webapi/jwe/general/decrypt.js", "../../jose/dist/webapi/lib/private_symbols.js", "../../jose/dist/webapi/lib/key_to_jwk.js", "../../jose/dist/webapi/key/export.js", "../../jose/dist/webapi/lib/encrypt_key_management.js", "../../jose/dist/webapi/jwe/flattened/encrypt.js", "../../jose/dist/webapi/jwe/general/encrypt.js", "../../jose/dist/webapi/lib/subtle_dsa.js", "../../jose/dist/webapi/lib/get_sign_verify_key.js", "../../jose/dist/webapi/lib/verify.js", "../../jose/dist/webapi/jws/flattened/verify.js", "../../jose/dist/webapi/jws/compact/verify.js", "../../jose/dist/webapi/jws/general/verify.js", "../../jose/dist/webapi/lib/epoch.js", "../../jose/dist/webapi/lib/secs.js", "../../jose/dist/webapi/lib/jwt_claims_set.js", "../../jose/dist/webapi/jwt/verify.js", "../../jose/dist/webapi/jwt/decrypt.js", "../../jose/dist/webapi/jwe/compact/encrypt.js", "../../jose/dist/webapi/lib/sign.js", "../../jose/dist/webapi/jws/flattened/sign.js", "../../jose/dist/webapi/jws/compact/sign.js", "../../jose/dist/webapi/jws/general/sign.js", "../../jose/dist/webapi/jwt/sign.js", "../../jose/dist/webapi/jwt/encrypt.js", "../../jose/dist/webapi/jwk/thumbprint.js", "../../jose/dist/webapi/jwk/embedded.js", "../../jose/dist/webapi/jwks/local.js", "../../jose/dist/webapi/jwks/remote.js", "../../jose/dist/webapi/jwt/unsecured.js", "../../jose/dist/webapi/util/decode_protected_header.js", "../../jose/dist/webapi/util/decode_jwt.js", "../../jose/dist/webapi/key/generate_key_pair.js", "../../jose/dist/webapi/key/generate_secret.js", "../../jose/dist/webapi/index.js"], "sourcesContent": ["import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n", "export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n", "export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n", "export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3));\n", "import { JWEInvalid } from '../util/errors.js';\nimport { bitLength } from './iv.js';\nexport default (enc, iv) => {\n    if (iv.length << 3 !== bitLength(enc)) {\n        throw new JWEInvalid('Invalid Initialization Vector length');\n    }\n};\n", "import { JWEInvalid } from '../util/errors.js';\nexport default (cek, expected) => {\n    const actual = cek.byteLength << 3;\n    if (actual !== expected) {\n        throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n    }\n};\n", "function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n", "function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n", "export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n", "import { concat, uint64be } from './buffer_utils.js';\nimport checkIvLength from './check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { JOSENotSupported, JWEDecryptionFailed, JWEInvalid } from '../util/errors.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nimport { isCryptoKey } from './is_key_like.js';\nasync function timingSafeEqual(a, b) {\n    if (!(a instanceof Uint8Array)) {\n        throw new TypeError('First argument must be a buffer');\n    }\n    if (!(b instanceof Uint8Array)) {\n        throw new TypeError('Second argument must be a buffer');\n    }\n    const algorithm = { name: 'HMAC', hash: 'SHA-256' };\n    const key = (await crypto.subtle.generateKey(algorithm, false, ['sign']));\n    const aHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, a));\n    const bHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, b));\n    let out = 0;\n    let i = -1;\n    while (++i < 32) {\n        out |= aHmac[i] ^ bHmac[i];\n    }\n    return out === 0;\n}\nasync function cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['decrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const expectedTag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    let macCheckPassed;\n    try {\n        macCheckPassed = await timingSafeEqual(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        plaintext = new Uint8Array(await crypto.subtle.decrypt({ iv, name: 'AES-CBC' }, encKey, ciphertext));\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nasync function gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['decrypt']);\n    }\n    else {\n        checkEncCryptoKey(cek, enc, 'decrypt');\n        encKey = cek;\n    }\n    try {\n        return new Uint8Array(await crypto.subtle.decrypt({\n            additionalData: aad,\n            iv,\n            name: 'AES-GCM',\n            tagLength: 128,\n        }, encKey, concat(ciphertext, tag)));\n    }\n    catch {\n        throw new JWEDecryptionFailed();\n    }\n}\nexport default async (enc, cek, ciphertext, iv, tag, aad) => {\n    if (!isCryptoKey(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (!iv) {\n        throw new JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new JWEInvalid('JWE Authentication Tag missing');\n    }\n    checkIvLength(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array)\n                checkCekLength(cek, parseInt(enc.slice(-3), 10));\n            return cbcDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array)\n                checkCekLength(cek, parseInt(enc.slice(1, 4), 10));\n            return gcmDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n", "export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n", "function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n", "import { checkEnc<PERSON>rypto<PERSON><PERSON> } from './crypto_key.js';\nfunction checkKeySize(key, alg) {\n    if (key.algorithm.length !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction getCryptoKey(key, alg, usage) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'AES-KW', true, [usage]);\n    }\n    checkEncCryptoKey(key, alg, usage);\n    return key;\n}\nexport async function wrap(alg, key, cek) {\n    const cryptoKey = await getCryptoKey(key, alg, 'wrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.importKey('raw', cek, { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.wrapKey('raw', cryptoKeyCek, cryptoKey, 'AES-KW'));\n}\nexport async function unwrap(alg, key, encryptedKey) {\n    const cryptoKey = await getCryptoKey(key, alg, 'unwrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.unwrapKey('raw', encryptedKey, cryptoKey, 'AES-KW', { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.exportKey('raw', cryptoKeyCek));\n}\n", "export default async (algorithm, data) => {\n    const subtleDigest = `SHA-${algorithm.slice(-3)}`;\n    return new Uint8Array(await crypto.subtle.digest(subtleDigest, data));\n};\n", "import { encoder, concat, uint32be } from './buffer_utils.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport digest from './digest.js';\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(secret, bits, value) {\n    const iterations = Math.ceil((bits >> 3) / 32);\n    const res = new Uint8Array(iterations * 32);\n    for (let iter = 0; iter < iterations; iter++) {\n        const buf = new Uint8Array(4 + secret.length + value.length);\n        buf.set(uint32be(iter + 1));\n        buf.set(secret, 4);\n        buf.set(value, 4 + secret.length);\n        res.set(await digest('sha256', buf), iter * 32);\n    }\n    return res.slice(0, bits >> 3);\n}\nexport async function deriveKey(publicKey, privateKey, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    checkEncCryptoKey(publicKey, 'ECDH');\n    checkEncCryptoKey(privateKey, 'ECDH', 'deriveBits');\n    const value = concat(lengthAndInput(encoder.encode(algorithm)), lengthAndInput(apu), lengthAndInput(apv), uint32be(keyLength));\n    let length;\n    if (publicKey.algorithm.name === 'X25519') {\n        length = 256;\n    }\n    else {\n        length =\n            Math.ceil(parseInt(publicKey.algorithm.namedCurve.slice(-3), 10) / 8) << 3;\n    }\n    const sharedSecret = new Uint8Array(await crypto.subtle.deriveBits({\n        name: publicKey.algorithm.name,\n        public: publicKey,\n    }, privateKey, length));\n    return concatKdf(sharedSecret, keyLength, value);\n}\nexport function allowed(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n        case 'P-384':\n        case 'P-521':\n            return true;\n        default:\n            return key.algorithm.name === 'X25519';\n    }\n}\n", "import { encode as b64u } from '../util/base64url.js';\nimport * as aeskw from './aeskw.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport { concat, encoder } from './buffer_utils.js';\nimport { JWEInvalid } from '../util/errors.js';\nfunction getCryptoKey(key, alg) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'PBKDF2', false, ['deriveBits']);\n    }\n    checkEncCryptoKey(key, alg, 'deriveBits');\n    return key;\n}\nconst concatSalt = (alg, p2sInput) => concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\nasync function deriveKey(p2s, alg, p2c, key) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10);\n    const subtleAlg = {\n        hash: `SHA-${alg.slice(8, 11)}`,\n        iterations: p2c,\n        name: 'PBKDF2',\n        salt,\n    };\n    const cryptoKey = await getCryptoKey(key, alg);\n    return new Uint8Array(await crypto.subtle.deriveBits(subtleAlg, cryptoKey, keylen));\n}\nexport async function wrap(alg, key, cek, p2c = 2048, p2s = crypto.getRandomValues(new Uint8Array(16))) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    const encryptedKey = await aeskw.wrap(alg.slice(-6), derived, cek);\n    return { encryptedKey, p2c, p2s: b64u(p2s) };\n}\nexport async function unwrap(alg, key, encryptedKey, p2c, p2s) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    return aeskw.unwrap(alg.slice(-6), derived, encryptedKey);\n}\n", "export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n", "import { checkEnc<PERSON>rypto<PERSON><PERSON> } from './crypto_key.js';\nimport checkKeyLength from './check_key_length.js';\nimport { JOSENotSupported } from '../util/errors.js';\nconst subtleAlgorithm = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return 'RSA-OAEP';\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\nexport async function encrypt(alg, key, cek) {\n    checkEncCryptoKey(key, alg, 'encrypt');\n    checkKeyLength(alg, key);\n    return new Uint8Array(await crypto.subtle.encrypt(subtleAlgorithm(alg), key, cek));\n}\nexport async function decrypt(alg, key, encryptedKey) {\n    checkEncCryptoKey(key, alg, 'decrypt');\n    checkKeyLength(alg, key);\n    return new Uint8Array(await crypto.subtle.decrypt(subtleAlgorithm(alg), key, encryptedKey));\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3));\n", "import invalidKeyInput from './invalid_key_input.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nconst formatPEM = (b64, descriptor) => {\n    const newlined = (b64.match(/.{1,64}/g) || []).join('\\n');\n    return `-----BEGIN ${descriptor}-----\\n${newlined}\\n-----END ${descriptor}-----`;\n};\nconst genericExport = async (keyType, keyFormat, key) => {\n    if (isKeyObject(key)) {\n        if (key.type !== keyType) {\n            throw new TypeError(`key is not a ${keyType} key`);\n        }\n        return key.export({ format: 'pem', type: keyFormat });\n    }\n    if (!isCryptoKey(key)) {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('CryptoKey is not extractable');\n    }\n    if (key.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return formatPEM(encodeBase64(new Uint8Array(await crypto.subtle.exportKey(keyFormat, key))), `${keyType.toUpperCase()} KEY`);\n};\nexport const toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexport const toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst findOid = (keyData, oid, from = 0) => {\n    if (from === 0) {\n        oid.unshift(oid.length);\n        oid.unshift(0x06);\n    }\n    const i = keyData.indexOf(oid[0], from);\n    if (i === -1)\n        return false;\n    const sub = keyData.subarray(i, i + oid.length);\n    if (sub.length !== oid.length)\n        return false;\n    return sub.every((value, index) => value === oid[index]) || findOid(keyData, oid, i + 1);\n};\nconst getNamedCurve = (keyData) => {\n    switch (true) {\n        case findOid(keyData, [0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07]):\n            return 'P-256';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x22]):\n            return 'P-384';\n        case findOid(keyData, [0x2b, 0x81, 0x04, 0x00, 0x23]):\n            return 'P-521';\n        default:\n            return undefined;\n    }\n};\nconst genericImport = async (replace, keyFormat, pem, alg, options) => {\n    let algorithm;\n    let keyUsages;\n    const keyData = new Uint8Array(atob(pem.replace(replace, ''))\n        .split('')\n        .map((c) => c.charCodeAt(0)));\n    const isPublic = keyFormat === 'spki';\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            algorithm = { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            algorithm = {\n                name: 'RSA-OAEP',\n                hash: `SHA-${parseInt(alg.slice(-3), 10) || 1}`,\n            };\n            keyUsages = isPublic ? ['encrypt', 'wrapKey'] : ['decrypt', 'unwrapKey'];\n            break;\n        case 'ES256':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES384':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ES512':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            const namedCurve = getNamedCurve(keyData);\n            algorithm = namedCurve?.startsWith('P-') ? { name: 'ECDH', namedCurve } : { name: 'X25519' };\n            keyUsages = isPublic ? [] : ['deriveBits'];\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = { name: 'Ed25519' };\n            keyUsages = isPublic ? ['verify'] : ['sign'];\n            break;\n        default:\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (Algorithm) value');\n    }\n    return crypto.subtle.importKey(keyFormat, keyData, algorithm, options?.extractable ?? (isPublic ? true : false), keyUsages);\n};\nexport const fromPKCS8 = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g, 'pkcs8', pem, alg, options);\n};\nexport const fromSPKI = (pem, alg, options) => {\n    return genericImport(/(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g, 'spki', pem, alg, options);\n};\nfunction getElement(seq) {\n    const result = [];\n    let next = 0;\n    while (next < seq.length) {\n        const nextPart = parseElement(seq.subarray(next));\n        result.push(nextPart);\n        next += nextPart.byteLength;\n    }\n    return result;\n}\nfunction parseElement(bytes) {\n    let position = 0;\n    let tag = bytes[0] & 0x1f;\n    position++;\n    if (tag === 0x1f) {\n        tag = 0;\n        while (bytes[position] >= 0x80) {\n            tag = tag * 128 + bytes[position] - 0x80;\n            position++;\n        }\n        tag = tag * 128 + bytes[position] - 0x80;\n        position++;\n    }\n    let length = 0;\n    if (bytes[position] < 0x80) {\n        length = bytes[position];\n        position++;\n    }\n    else if (length === 0x80) {\n        length = 0;\n        while (bytes[position + length] !== 0 || bytes[position + length + 1] !== 0) {\n            if (length > bytes.byteLength) {\n                throw new TypeError('invalid indefinite form length');\n            }\n            length++;\n        }\n        const byteLength = position + length + 2;\n        return {\n            byteLength,\n            contents: bytes.subarray(position, position + length),\n            raw: bytes.subarray(0, byteLength),\n        };\n    }\n    else {\n        const numberOfDigits = bytes[position] & 0x7f;\n        position++;\n        length = 0;\n        for (let i = 0; i < numberOfDigits; i++) {\n            length = length * 256 + bytes[position];\n            position++;\n        }\n    }\n    const byteLength = position + length;\n    return {\n        byteLength,\n        contents: bytes.subarray(position, byteLength),\n        raw: bytes.subarray(0, byteLength),\n    };\n}\nfunction spkiFromX509(buf) {\n    const tbsCertificate = getElement(getElement(parseElement(buf).contents)[0].contents);\n    return encodeBase64(tbsCertificate[tbsCertificate[0].raw[0] === 0xa0 ? 6 : 5].raw);\n}\nlet createPublicKey;\nfunction getSPKI(x509) {\n    try {\n        createPublicKey ??= globalThis.process?.getBuiltinModule?.('node:crypto')?.createPublicKey;\n    }\n    catch {\n        createPublicKey = 0;\n    }\n    if (createPublicKey) {\n        try {\n            return new createPublicKey(x509).export({ format: 'pem', type: 'spki' });\n        }\n        catch { }\n    }\n    const pem = x509.replace(/(?:-----(?:BEGIN|END) CERTIFICATE-----|\\s)/g, '');\n    const raw = decodeBase64(pem);\n    return formatPEM(spkiFromX509(raw), 'PUBLIC KEY');\n}\nexport const fromX509 = (pem, alg, options) => {\n    let spki;\n    try {\n        spki = getSPKI(pem);\n    }\n    catch (cause) {\n        throw new TypeError('Failed to parse the X.509 certificate', { cause });\n    }\n    return fromSPKI(spki, alg, options);\n};\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    delete keyData.alg;\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d ? false : true), jwk.key_ops ?? keyUsages);\n};\n", "import { decode as decodeBase64URL } from '../util/base64url.js';\nimport { fromSPKI, fromPKCS8, fromX509 } from '../lib/asn1.js';\nimport toCryptoKey from '../lib/jwk_to_key.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nexport async function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return fromSPKI(spki, alg, options);\n}\nexport async function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return fromX509(x509, alg, options);\n}\nexport async function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return fromPKCS8(pkcs8, alg, options);\n}\nexport async function importJWK(jwk, alg, options) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    let ext;\n    alg ??= jwk.alg;\n    ext ??= options?.extractable ?? jwk.ext;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return decodeBase64URL(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n        case 'EC':\n        case 'OKP':\n            return toCryptoKey({ ...jwk, alg, ext });\n        default:\n            throw new JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n", "import { concat, uint64be } from './buffer_utils.js';\nimport checkIvLength from './check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nimport generateIv from './iv.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './is_key_like.js';\nasync function cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['encrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const ciphertext = new Uint8Array(await crypto.subtle.encrypt({\n        iv,\n        name: 'AES-CBC',\n    }, encKey, plaintext));\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const tag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    return { ciphertext, tag, iv };\n}\nasync function gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['encrypt']);\n    }\n    else {\n        checkEncCryptoKey(cek, enc, 'encrypt');\n        encKey = cek;\n    }\n    const encrypted = new Uint8Array(await crypto.subtle.encrypt({\n        additionalData: aad,\n        iv,\n        name: 'AES-GCM',\n        tagLength: 128,\n    }, encKey, plaintext));\n    const tag = encrypted.slice(-16);\n    const ciphertext = encrypted.slice(0, -16);\n    return { ciphertext, tag, iv };\n}\nexport default async (enc, plaintext, cek, iv, aad) => {\n    if (!isCryptoKey(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (iv) {\n        checkIvLength(enc, iv);\n    }\n    else {\n        iv = generateIv(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array) {\n                checkCekLength(cek, parseInt(enc.slice(-3), 10));\n            }\n            return cbcEncrypt(enc, plaintext, cek, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array) {\n                checkCekLength(cek, parseInt(enc.slice(1, 4), 10));\n            }\n            return gcmEncrypt(enc, plaintext, cek, iv, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n", "import encrypt from './encrypt.js';\nimport decrypt from './decrypt.js';\nimport { encode as b64u } from '../util/base64url.js';\nexport async function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await encrypt(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: b64u(wrapped.iv),\n        tag: b64u(wrapped.tag),\n    };\n}\nexport async function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return decrypt(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n", "import * as aeskw from './aeskw.js';\nimport * as ecdhes from './ecdhes.js';\nimport * as pbes2kw from './pbes2kw.js';\nimport * as rsaes from './rsaes.js';\nimport { decode as b64u } from '../util/base64url.js';\nimport { JOSENotSupported, JWEInvalid } from '../util/errors.js';\nimport { bitLength as cekLength } from '../lib/cek.js';\nimport { importJWK } from '../key/import.js';\nimport isObject from './is_object.js';\nimport { unwrap as aesGcmKw } from './aesgcmkw.js';\nimport { assertCryptoKey } from './is_key_like.js';\nexport default async (alg, key, encryptedKey, joseHeader, options) => {\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!isObject(joseHeader.epk))\n                throw new JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            assertCryptoKey(key);\n            if (!ecdhes.allowed(key))\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await importJWK(joseHeader.epk, alg);\n            assertCryptoKey(epk);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = b64u(joseHeader.apu);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = b64u(joseHeader.apv);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await ecdhes.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? cekLength(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aeskw.unwrap(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            assertCryptoKey(key);\n            return rsaes.decrypt(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = b64u(joseHeader.p2s);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return pbes2kw.unwrap(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aeskw.unwrap(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = b64u(joseHeader.iv);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = b64u(joseHeader.tag);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the tag');\n            }\n            return aesGcmKw(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n};\n", "import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n", "export default (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n", "import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'string';\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n", "import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n", "import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n", "import { decode as b64u } from '../../util/base64url.js';\nimport decrypt from '../../lib/decrypt.js';\nimport { JOSEAlgNotAllowed, JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport decryptKeyManagement from '../../lib/decrypt_key_management.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport generateCek from '../../lib/cek.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nexport async function flattenedDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !isObject(jwe.header)) {\n        throw new JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !isObject(jwe.unprotected)) {\n        throw new JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = b64u(jwe.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    validateCrit(JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && validateAlgorithms('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        validateAlgorithms('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = b64u(jwe.encrypted_key);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    checkKeyType(alg === 'dir' ? enc : alg, key, 'decrypt');\n    const k = await normalizeKey(key, alg);\n    let cek;\n    try {\n        cek = await decryptKeyManagement(alg, k, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof JWEInvalid || err instanceof JOSENotSupported) {\n            throw err;\n        }\n        cek = generateCek(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = b64u(jwe.iv);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = b64u(jwe.tag);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = b64u(jwe.ciphertext);\n    }\n    catch {\n        throw new JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await decrypt(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = b64u(jwe.aad);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n", "import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await flattenedDecrypt({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n", "import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEDecryptionFailed, JWEInvalid } from '../../util/errors.js';\nimport isObject from '../../lib/is_object.js';\nexport async function generalDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('General JWE must be an object');\n    }\n    if (!Array.isArray(jwe.recipients) || !jwe.recipients.every(isObject)) {\n        throw new JWEInvalid('JWE Recipients missing or incorrect type');\n    }\n    if (!jwe.recipients.length) {\n        throw new JWEInvalid('JWE Recipients has no members');\n    }\n    for (const recipient of jwe.recipients) {\n        try {\n            return await flattenedDecrypt({\n                aad: jwe.aad,\n                ciphertext: jwe.ciphertext,\n                encrypted_key: recipient.encrypted_key,\n                header: recipient.header,\n                iv: jwe.iv,\n                protected: jwe.protected,\n                tag: jwe.tag,\n                unprotected: jwe.unprotected,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new JWEDecryptionFailed();\n}\n", "export const unprotected = Symbol();\n", "import invalidKeyInput from './invalid_key_input.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nexport default async function keyToJWK(key) {\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            key = key.export();\n        }\n        else {\n            return key.export({ format: 'jwk' });\n        }\n    }\n    if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: b64u(key),\n        };\n    }\n    if (!isCryptoKey(key)) {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'Uint8Array'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('non-extractable CryptoKey cannot be exported as a JWK');\n    }\n    const { ext, key_ops, alg, use, ...jwk } = await crypto.subtle.exportKey('jwk', key);\n    return jwk;\n}\n", "import { toSPKI as exportPublic, toPKCS8 as exportPrivate } from '../lib/asn1.js';\nimport keyToJWK from '../lib/key_to_jwk.js';\nexport async function exportSPKI(key) {\n    return exportPublic(key);\n}\nexport async function exportPKCS8(key) {\n    return exportPrivate(key);\n}\nexport async function exportJWK(key) {\n    return keyToJWK(key);\n}\n", "import * as aeskw from './aeskw.js';\nimport * as ecdhes from './ecdhes.js';\nimport * as pbes2kw from './pbes2kw.js';\nimport * as rsaes from './rsaes.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport normalizeKey from './normalize_key.js';\nimport generateCek, { bitLength as cekLength } from '../lib/cek.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { exportJWK } from '../key/export.js';\nimport { wrap as aesGcmKw } from './aesgcmkw.js';\nimport { assertCryptoKey } from './is_key_like.js';\nexport default async (alg, enc, key, providedCek, providedParameters = {}) => {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            assertCryptoKey(key);\n            if (!ecdhes.allowed(key)) {\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let ephemeralKey;\n            if (providedParameters.epk) {\n                ephemeralKey = (await normalizeKey(providedParameters.epk, alg));\n            }\n            else {\n                ephemeralKey = (await crypto.subtle.generateKey(key.algorithm, true, ['deriveBits'])).privateKey;\n            }\n            const { x, y, crv, kty } = await exportJWK(ephemeralKey);\n            const sharedSecret = await ecdhes.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? cekLength(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = b64u(apu);\n            if (apv)\n                parameters.apv = b64u(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || generateCek(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await aeskw.wrap(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || generateCek(enc);\n            assertCryptoKey(key);\n            encryptedKey = await rsaes.encrypt(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || generateCek(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await pbes2kw.wrap(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await aeskw.wrap(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || generateCek(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await aesGcmKw(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n};\n", "import { encode as b64u } from '../../util/base64url.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport encrypt from '../../lib/encrypt.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nexport class FlattenedEncrypt {\n    #plaintext;\n    #protectedHeader;\n    #sharedUnprotectedHeader;\n    #unprotectedHeader;\n    #aad;\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this.#plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this.#sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this.#sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this.#aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader && !this.#sharedUnprotectedHeader) {\n            throw new JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader, this.#sharedUnprotectedHeader)) {\n            throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n            ...this.#sharedUnprotectedHeader,\n        };\n        validateCrit(JWEInvalid, new Map(), options?.crit, this.#protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this.#cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        checkKeyType(alg === 'dir' ? enc : alg, key, 'encrypt');\n        let cek;\n        {\n            let parameters;\n            const k = await normalizeKey(key, alg);\n            ({ cek, encryptedKey, parameters } = await encryptKeyManagement(alg, enc, k, this.#cek, this.#keyManagementParameters));\n            if (parameters) {\n                if (options && unprotected in options) {\n                    if (!this.#unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this.#unprotectedHeader = { ...this.#unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this.#protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this.#protectedHeader = { ...this.#protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        if (this.#aad) {\n            aadMember = b64u(this.#aad);\n            additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await encrypt(enc, this.#plaintext, cek, this.#iv, additionalData);\n        const jwe = {\n            ciphertext: b64u(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = b64u(iv);\n        }\n        if (tag) {\n            jwe.tag = b64u(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = b64u(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this.#protectedHeader) {\n            jwe.protected = decoder.decode(protectedHeader);\n        }\n        if (this.#sharedUnprotectedHeader) {\n            jwe.unprotected = this.#sharedUnprotectedHeader;\n        }\n        if (this.#unprotectedHeader) {\n            jwe.header = this.#unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n", "import { FlattenedEncrypt } from '../flattened/encrypt.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport generateCek from '../../lib/cek.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { encode as b64u } from '../../util/base64url.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nclass IndividualRecipient {\n    #parent;\n    unprotectedHeader;\n    key;\n    options;\n    constructor(enc, key, options) {\n        this.#parent = enc;\n        this.key = key;\n        this.options = options;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addRecipient(...args) {\n        return this.#parent.addRecipient(...args);\n    }\n    encrypt(...args) {\n        return this.#parent.encrypt(...args);\n    }\n    done() {\n        return this.#parent;\n    }\n}\nexport class GeneralEncrypt {\n    #plaintext;\n    #recipients = [];\n    #protectedHeader;\n    #unprotectedHeader;\n    #aad;\n    constructor(plaintext) {\n        this.#plaintext = plaintext;\n    }\n    addRecipient(key, options) {\n        const recipient = new IndividualRecipient(this, key, { crit: options?.crit });\n        this.#recipients.push(recipient);\n        return recipient;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this.#aad = aad;\n        return this;\n    }\n    async encrypt() {\n        if (!this.#recipients.length) {\n            throw new JWEInvalid('at least one recipient must be added');\n        }\n        if (this.#recipients.length === 1) {\n            const [recipient] = this.#recipients;\n            const flattened = await new FlattenedEncrypt(this.#plaintext)\n                .setAdditionalAuthenticatedData(this.#aad)\n                .setProtectedHeader(this.#protectedHeader)\n                .setSharedUnprotectedHeader(this.#unprotectedHeader)\n                .setUnprotectedHeader(recipient.unprotectedHeader)\n                .encrypt(recipient.key, { ...recipient.options });\n            const jwe = {\n                ciphertext: flattened.ciphertext,\n                iv: flattened.iv,\n                recipients: [{}],\n                tag: flattened.tag,\n            };\n            if (flattened.aad)\n                jwe.aad = flattened.aad;\n            if (flattened.protected)\n                jwe.protected = flattened.protected;\n            if (flattened.unprotected)\n                jwe.unprotected = flattened.unprotected;\n            if (flattened.encrypted_key)\n                jwe.recipients[0].encrypted_key = flattened.encrypted_key;\n            if (flattened.header)\n                jwe.recipients[0].header = flattened.header;\n            return jwe;\n        }\n        let enc;\n        for (let i = 0; i < this.#recipients.length; i++) {\n            const recipient = this.#recipients[i];\n            if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader, recipient.unprotectedHeader)) {\n                throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n            }\n            const joseHeader = {\n                ...this.#protectedHeader,\n                ...this.#unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const { alg } = joseHeader;\n            if (typeof alg !== 'string' || !alg) {\n                throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n            }\n            if (alg === 'dir' || alg === 'ECDH-ES') {\n                throw new JWEInvalid('\"dir\" and \"ECDH-ES\" alg may only be used with a single recipient');\n            }\n            if (typeof joseHeader.enc !== 'string' || !joseHeader.enc) {\n                throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n            }\n            if (!enc) {\n                enc = joseHeader.enc;\n            }\n            else if (enc !== joseHeader.enc) {\n                throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter must be the same for all recipients');\n            }\n            validateCrit(JWEInvalid, new Map(), recipient.options.crit, this.#protectedHeader, joseHeader);\n            if (joseHeader.zip !== undefined) {\n                throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n            }\n        }\n        const cek = generateCek(enc);\n        const jwe = {\n            ciphertext: '',\n            iv: '',\n            recipients: [],\n            tag: '',\n        };\n        for (let i = 0; i < this.#recipients.length; i++) {\n            const recipient = this.#recipients[i];\n            const target = {};\n            jwe.recipients.push(target);\n            const joseHeader = {\n                ...this.#protectedHeader,\n                ...this.#unprotectedHeader,\n                ...recipient.unprotectedHeader,\n            };\n            const p2c = joseHeader.alg.startsWith('PBES2') ? 2048 + i : undefined;\n            if (i === 0) {\n                const flattened = await new FlattenedEncrypt(this.#plaintext)\n                    .setAdditionalAuthenticatedData(this.#aad)\n                    .setContentEncryptionKey(cek)\n                    .setProtectedHeader(this.#protectedHeader)\n                    .setSharedUnprotectedHeader(this.#unprotectedHeader)\n                    .setUnprotectedHeader(recipient.unprotectedHeader)\n                    .setKeyManagementParameters({ p2c })\n                    .encrypt(recipient.key, {\n                    ...recipient.options,\n                    [unprotected]: true,\n                });\n                jwe.ciphertext = flattened.ciphertext;\n                jwe.iv = flattened.iv;\n                jwe.tag = flattened.tag;\n                if (flattened.aad)\n                    jwe.aad = flattened.aad;\n                if (flattened.protected)\n                    jwe.protected = flattened.protected;\n                if (flattened.unprotected)\n                    jwe.unprotected = flattened.unprotected;\n                target.encrypted_key = flattened.encrypted_key;\n                if (flattened.header)\n                    target.header = flattened.header;\n                continue;\n            }\n            const alg = recipient.unprotectedHeader?.alg ||\n                this.#protectedHeader?.alg ||\n                this.#unprotectedHeader?.alg;\n            checkKeyType(alg === 'dir' ? enc : alg, recipient.key, 'encrypt');\n            const k = await normalizeKey(recipient.key, alg);\n            const { encryptedKey, parameters } = await encryptKeyManagement(alg, enc, k, cek, { p2c });\n            target.encrypted_key = b64u(encryptedKey);\n            if (recipient.unprotectedHeader || parameters)\n                target.header = { ...recipient.unprotectedHeader, ...parameters };\n        }\n        return jwe;\n    }\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport default (alg, algorithm) => {\n    const hash = `SHA-${alg.slice(-3)}`;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            return { hash, name: '<PERSON><PERSON>' };\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            return { hash, name: 'RSA-PSS', saltLength: parseInt(alg.slice(-3), 10) >> 3 };\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            return { hash, name: 'RSASSA-PKCS1-v1_5' };\n        case 'ES256':\n        case 'ES384':\n        case 'ES512':\n            return { hash, name: 'ECDS<PERSON>', namedCurve: algorithm.namedCurve };\n        case 'Ed25519':\n        case 'EdDSA':\n            return { name: 'Ed25519' };\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\n", "import { checkSig<PERSON>rypt<PERSON><PERSON><PERSON> } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nexport default async (alg, key, usage) => {\n    if (key instanceof Uint8Array) {\n        if (!alg.startsWith('HS')) {\n            throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n        }\n        return crypto.subtle.importKey('raw', key, { hash: `SHA-${alg.slice(-3)}`, name: 'HM<PERSON>' }, false, [usage]);\n    }\n    checkSigCryptoKey(key, alg, usage);\n    return key;\n};\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport check<PERSON><PERSON><PERSON>ength from './check_key_length.js';\nimport getVerifyKey from './get_sign_verify_key.js';\nexport default async (alg, key, signature, data) => {\n    const cryptoKey = await getVerifyKey(alg, key, 'verify');\n    checkKeyLength(alg, cryptoKey);\n    const algorithm = subtleAlgorithm(alg, cryptoKey.algorithm);\n    try {\n        return await crypto.subtle.verify(algorithm, cryptoKey, signature, data);\n    }\n    catch {\n        return false;\n    }\n};\n", "import { decode as b64u } from '../../util/base64url.js';\nimport verify from '../../lib/verify.js';\nimport { JOSEAlgNotAllowed, JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport { concat, encoder, decoder } from '../../lib/buffer_utils.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport async function flattenedVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('Flattened JWS must be an object');\n    }\n    if (jws.protected === undefined && jws.header === undefined) {\n        throw new JWSInvalid('Flattened JWS must have either of the \"protected\" or \"header\" members');\n    }\n    if (jws.protected !== undefined && typeof jws.protected !== 'string') {\n        throw new JWSInvalid('JWS Protected Header incorrect type');\n    }\n    if (jws.payload === undefined) {\n        throw new JWSInvalid('JWS Payload missing');\n    }\n    if (typeof jws.signature !== 'string') {\n        throw new JWSInvalid('JWS Signature missing or incorrect type');\n    }\n    if (jws.header !== undefined && !isObject(jws.header)) {\n        throw new JWSInvalid('JWS Unprotected Header incorrect type');\n    }\n    let parsedProt = {};\n    if (jws.protected) {\n        try {\n            const protectedHeader = b64u(jws.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWSInvalid('JWS Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jws.header)) {\n        throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jws.header,\n    };\n    const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, parsedProt, joseHeader);\n    let b64 = true;\n    if (extensions.has('b64')) {\n        b64 = parsedProt.b64;\n        if (typeof b64 !== 'boolean') {\n            throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n        }\n    }\n    const { alg } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n    }\n    const algorithms = options && validateAlgorithms('algorithms', options.algorithms);\n    if (algorithms && !algorithms.has(alg)) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (b64) {\n        if (typeof jws.payload !== 'string') {\n            throw new JWSInvalid('JWS Payload must be a string');\n        }\n    }\n    else if (typeof jws.payload !== 'string' && !(jws.payload instanceof Uint8Array)) {\n        throw new JWSInvalid('JWS Payload must be a string or an Uint8Array instance');\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jws);\n        resolvedKey = true;\n    }\n    checkKeyType(alg, key, 'verify');\n    const data = concat(encoder.encode(jws.protected ?? ''), encoder.encode('.'), typeof jws.payload === 'string' ? encoder.encode(jws.payload) : jws.payload);\n    let signature;\n    try {\n        signature = b64u(jws.signature);\n    }\n    catch {\n        throw new JWSInvalid('Failed to base64url decode the signature');\n    }\n    const k = await normalizeKey(key, alg);\n    const verified = await verify(alg, k, signature, data);\n    if (!verified) {\n        throw new JWSSignatureVerificationFailed();\n    }\n    let payload;\n    if (b64) {\n        try {\n            payload = b64u(jws.payload);\n        }\n        catch {\n            throw new JWSInvalid('Failed to base64url decode the payload');\n        }\n    }\n    else if (typeof jws.payload === 'string') {\n        payload = encoder.encode(jws.payload);\n    }\n    else {\n        payload = jws.payload;\n    }\n    const result = { payload };\n    if (jws.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jws.header !== undefined) {\n        result.unprotectedHeader = jws.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactVerify(jws, key, options) {\n    if (jws instanceof Uint8Array) {\n        jws = decoder.decode(jws);\n    }\n    if (typeof jws !== 'string') {\n        throw new JWSInvalid('Compact JWS must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: payload, 2: signature, length } = jws.split('.');\n    if (length !== 3) {\n        throw new JWSInvalid('Invalid Compact JWS');\n    }\n    const verified = await flattenedVerify({ payload, protected: protectedHeader, signature }, key, options);\n    const result = { payload: verified.payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { flattenedVerify } from '../flattened/verify.js';\nimport { JWSInvalid, JWSSignatureVerificationFailed } from '../../util/errors.js';\nimport isObject from '../../lib/is_object.js';\nexport async function generalVerify(jws, key, options) {\n    if (!isObject(jws)) {\n        throw new JWSInvalid('General JWS must be an object');\n    }\n    if (!Array.isArray(jws.signatures) || !jws.signatures.every(isObject)) {\n        throw new JWSInvalid('JWS Signatures missing or incorrect type');\n    }\n    for (const signature of jws.signatures) {\n        try {\n            return await flattenedVerify({\n                header: signature.header,\n                payload: jws.payload,\n                protected: signature.protected,\n                signature: signature.signature,\n            }, key, options);\n        }\n        catch {\n        }\n    }\n    throw new JWSSignatureVerificationFailed();\n}\n", "export default (date) => Math.floor(date.getTime() / 1000);\n", "const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n", "import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n", "import { compactVerify } from '../jws/compact/verify.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTInvalid } from '../util/errors.js';\nexport async function jwtVerify(jwt, key, options) {\n    const verified = await compactVerify(jwt, key, options);\n    if (verified.protectedHeader.crit?.includes('b64') && verified.protectedHeader.b64 === false) {\n        throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n    }\n    const payload = validateClaimsSet(verified.protectedHeader, verified.payload, options);\n    const result = { payload, protectedHeader: verified.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: verified.key };\n    }\n    return result;\n}\n", "import { compactDecrypt } from '../jwe/compact/decrypt.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTClaimValidationFailed } from '../util/errors.js';\nexport async function jwtDecrypt(jwt, key, options) {\n    const decrypted = await compactDecrypt(jwt, key, options);\n    const payload = validateClaimsSet(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n", "import { FlattenedEncrypt } from '../flattened/encrypt.js';\nexport class CompactEncrypt {\n    #flattened;\n    constructor(plaintext) {\n        this.#flattened = new FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this.#flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this.#flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this.#flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this.#flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n", "import subtleAlgorithm from './subtle_dsa.js';\nimport checkKey<PERSON>ength from './check_key_length.js';\nimport getSignKey from './get_sign_verify_key.js';\nexport default async (alg, key, data) => {\n    const cryptoKey = await getSignKey(alg, key, 'sign');\n    checkKeyLength(alg, cryptoKey);\n    const signature = await crypto.subtle.sign(subtleAlgorithm(alg, cryptoKey.algorithm), cryptoKey, data);\n    return new Uint8Array(signature);\n};\n", "import { encode as b64u } from '../../util/base64url.js';\nimport sign from '../../lib/sign.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { JWSInvalid } from '../../util/errors.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nexport class FlattenedSign {\n    #payload;\n    #protectedHeader;\n    #unprotectedHeader;\n    constructor(payload) {\n        if (!(payload instanceof Uint8Array)) {\n            throw new TypeError('payload must be an instance of Uint8Array');\n        }\n        this.#payload = payload;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader) {\n            throw new JWSInvalid('either setProtectedHeader or setUnprotectedHeader must be called before #sign()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader)) {\n            throw new JWSInvalid('JWS Protected and JWS Unprotected Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n        };\n        const extensions = validateCrit(JWSInvalid, new Map([['b64', true]]), options?.crit, this.#protectedHeader, joseHeader);\n        let b64 = true;\n        if (extensions.has('b64')) {\n            b64 = this.#protectedHeader.b64;\n            if (typeof b64 !== 'boolean') {\n                throw new JWSInvalid('The \"b64\" (base64url-encode payload) Header Parameter must be a boolean');\n            }\n        }\n        const { alg } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWSInvalid('JWS \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        checkKeyType(alg, key, 'sign');\n        let payload = this.#payload;\n        if (b64) {\n            payload = encoder.encode(b64u(payload));\n        }\n        let protectedHeader;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        const data = concat(protectedHeader, encoder.encode('.'), payload);\n        const k = await normalizeKey(key, alg);\n        const signature = await sign(alg, k, data);\n        const jws = {\n            signature: b64u(signature),\n            payload: '',\n        };\n        if (b64) {\n            jws.payload = decoder.decode(payload);\n        }\n        if (this.#unprotectedHeader) {\n            jws.header = this.#unprotectedHeader;\n        }\n        if (this.#protectedHeader) {\n            jws.protected = decoder.decode(protectedHeader);\n        }\n        return jws;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nexport class CompactSign {\n    #flattened;\n    constructor(payload) {\n        this.#flattened = new FlattenedSign(payload);\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    async sign(key, options) {\n        const jws = await this.#flattened.sign(key, options);\n        if (jws.payload === undefined) {\n            throw new TypeError('use the flattened module for creating JWS with b64: false');\n        }\n        return `${jws.protected}.${jws.payload}.${jws.signature}`;\n    }\n}\n", "import { FlattenedSign } from '../flattened/sign.js';\nimport { JWSInvalid } from '../../util/errors.js';\nclass IndividualSignature {\n    #parent;\n    protectedHeader;\n    unprotectedHeader;\n    options;\n    key;\n    constructor(sig, key, options) {\n        this.#parent = sig;\n        this.key = key;\n        this.options = options;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.protectedHeader = protectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    addSignature(...args) {\n        return this.#parent.addSignature(...args);\n    }\n    sign(...args) {\n        return this.#parent.sign(...args);\n    }\n    done() {\n        return this.#parent;\n    }\n}\nexport class GeneralSign {\n    #payload;\n    #signatures = [];\n    constructor(payload) {\n        this.#payload = payload;\n    }\n    addSignature(key, options) {\n        const signature = new IndividualSignature(this, key, options);\n        this.#signatures.push(signature);\n        return signature;\n    }\n    async sign() {\n        if (!this.#signatures.length) {\n            throw new JWSInvalid('at least one signature must be added');\n        }\n        const jws = {\n            signatures: [],\n            payload: '',\n        };\n        for (let i = 0; i < this.#signatures.length; i++) {\n            const signature = this.#signatures[i];\n            const flattened = new FlattenedSign(this.#payload);\n            flattened.setProtectedHeader(signature.protectedHeader);\n            flattened.setUnprotectedHeader(signature.unprotectedHeader);\n            const { payload, ...rest } = await flattened.sign(signature.key, signature.options);\n            if (i === 0) {\n                jws.payload = payload;\n            }\n            else if (jws.payload !== payload) {\n                throw new JWSInvalid('inconsistent use of JWS Unencoded Payload (RFC7797)');\n            }\n            jws.signatures.push(rest);\n        }\n        return jws;\n    }\n}\n", "import { CompactSign } from '../jws/compact/sign.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class SignJWT {\n    #protectedHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    async sign(key, options) {\n        const sig = new CompactSign(this.#jwt.data());\n        sig.setProtectedHeader(this.#protectedHeader);\n        if (Array.isArray(this.#protectedHeader?.crit) &&\n            this.#protectedHeader.crit.includes('b64') &&\n            this.#protectedHeader.b64 === false) {\n            throw new JWTInvalid('JWTs MUST NOT use unencoded payload');\n        }\n        return sig.sign(key, options);\n    }\n}\n", "import { CompactEncrypt } from '../jwe/compact/encrypt.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class EncryptJWT {\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    #protectedHeader;\n    #replicateIssuerAsHeader;\n    #replicateSubjectAsHeader;\n    #replicateAudienceAsHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this.#replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this.#replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this.#replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new CompactEncrypt(this.#jwt.data());\n        if (this.#protectedHeader &&\n            (this.#replicateIssuerAsHeader ||\n                this.#replicateSubjectAsHeader ||\n                this.#replicateAudienceAsHeader)) {\n            this.#protectedHeader = {\n                ...this.#protectedHeader,\n                iss: this.#replicateIssuerAsHeader ? this.#jwt.iss : undefined,\n                sub: this.#replicateSubjectAsHeader ? this.#jwt.sub : undefined,\n                aud: this.#replicateAudienceAsHeader ? this.#jwt.aud : undefined,\n            };\n        }\n        enc.setProtectedHeader(this.#protectedHeader);\n        if (this.#iv) {\n            enc.setInitializationVector(this.#iv);\n        }\n        if (this.#cek) {\n            enc.setContentEncryptionKey(this.#cek);\n        }\n        if (this.#keyManagementParameters) {\n            enc.setKeyManagementParameters(this.#keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n", "import digest from '../lib/digest.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport { JOSENotSupported, JWKInvalid } from '../util/errors.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport isKey<PERSON>ike from '../lib/is_key_like.js';\nimport { isJWK } from '../lib/is_jwk.js';\nimport { exportJWK } from '../key/export.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new JWKInvalid(`${description} missing or invalid`);\n    }\n};\nexport async function calculateJwkThumbprint(key, digestAlgorithm) {\n    let jwk;\n    if (isJWK(key)) {\n        jwk = key;\n    }\n    else if (isKey<PERSON>ike(key)) {\n        jwk = await exportJWK(key);\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    digestAlgorithm ??= 'sha256';\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = encoder.encode(JSON.stringify(components));\n    return b64u(await digest(digestAlgorithm, data));\n}\nexport async function calculateJwkThumbprintUri(key, digestAlgorithm) {\n    digestAlgorithm ??= 'sha256';\n    const thumbprint = await calculateJwkThumbprint(key, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\n", "import { importJWK } from '../key/import.js';\nimport isObject from '../lib/is_object.js';\nimport { JWSInvalid } from '../util/errors.js';\nexport async function EmbeddedJWK(protectedHeader, token) {\n    const joseHeader = {\n        ...protectedHeader,\n        ...token?.header,\n    };\n    if (!isObject(joseHeader.jwk)) {\n        throw new JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a JSON object');\n    }\n    const key = await importJWK({ ...joseHeader.jwk, ext: true }, joseHeader.alg);\n    if (key instanceof Uint8Array || key.type !== 'public') {\n        throw new JWSInvalid('\"jwk\" (JSON Web Key) Header Parameter must be a public key');\n    }\n    return key;\n}\n", "import { importJWK } from '../key/import.js';\nimport { J<PERSON><PERSON>Invalid, JOSENotSupported, JWKSNoMatchingKey, JWKSMultipleMatchingKeys, } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nfunction getKtyFromAlg(alg) {\n    switch (typeof alg === 'string' && alg.slice(0, 2)) {\n        case 'RS':\n        case 'PS':\n            return 'RSA';\n        case 'ES':\n            return 'EC';\n        case 'Ed':\n            return 'OKP';\n        default:\n            throw new JOSENotSupported('Unsupported \"alg\" value for a JSON Web Key Set');\n    }\n}\nfunction isJWKSLike(jwks) {\n    return (jwks &&\n        typeof jwks === 'object' &&\n        Array.isArray(jwks.keys) &&\n        jwks.keys.every(isJWKLike));\n}\nfunction isJWKLike(key) {\n    return isObject(key);\n}\nclass LocalJWKSet {\n    #jwks;\n    #cached = new WeakMap();\n    constructor(jwks) {\n        if (!isJWKSLike(jwks)) {\n            throw new JWKSInvalid('JSON Web Key Set malformed');\n        }\n        this.#jwks = structuredClone(jwks);\n    }\n    jwks() {\n        return this.#jwks;\n    }\n    async getKey(protectedHeader, token) {\n        const { alg, kid } = { ...protectedHeader, ...token?.header };\n        const kty = getKtyFromAlg(alg);\n        const candidates = this.#jwks.keys.filter((jwk) => {\n            let candidate = kty === jwk.kty;\n            if (candidate && typeof kid === 'string') {\n                candidate = kid === jwk.kid;\n            }\n            if (candidate && typeof jwk.alg === 'string') {\n                candidate = alg === jwk.alg;\n            }\n            if (candidate && typeof jwk.use === 'string') {\n                candidate = jwk.use === 'sig';\n            }\n            if (candidate && Array.isArray(jwk.key_ops)) {\n                candidate = jwk.key_ops.includes('verify');\n            }\n            if (candidate) {\n                switch (alg) {\n                    case 'ES256':\n                        candidate = jwk.crv === 'P-256';\n                        break;\n                    case 'ES384':\n                        candidate = jwk.crv === 'P-384';\n                        break;\n                    case 'ES512':\n                        candidate = jwk.crv === 'P-521';\n                        break;\n                    case 'Ed25519':\n                    case 'EdDSA':\n                        candidate = jwk.crv === 'Ed25519';\n                        break;\n                }\n            }\n            return candidate;\n        });\n        const { 0: jwk, length } = candidates;\n        if (length === 0) {\n            throw new JWKSNoMatchingKey();\n        }\n        if (length !== 1) {\n            const error = new JWKSMultipleMatchingKeys();\n            const _cached = this.#cached;\n            error[Symbol.asyncIterator] = async function* () {\n                for (const jwk of candidates) {\n                    try {\n                        yield await importWithAlgCache(_cached, jwk, alg);\n                    }\n                    catch { }\n                }\n            };\n            throw error;\n        }\n        return importWithAlgCache(this.#cached, jwk, alg);\n    }\n}\nasync function importWithAlgCache(cache, jwk, alg) {\n    const cached = cache.get(jwk) || cache.set(jwk, {}).get(jwk);\n    if (cached[alg] === undefined) {\n        const key = await importJWK({ ...jwk, ext: true }, alg);\n        if (key instanceof Uint8Array || key.type !== 'public') {\n            throw new JWKSInvalid('JSON Web Key Set members must be public keys');\n        }\n        cached[alg] = key;\n    }\n    return cached[alg];\n}\nexport function createLocalJWKSet(jwks) {\n    const set = new LocalJWKSet(jwks);\n    const localJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(localJWKSet, {\n        jwks: {\n            value: () => structuredClone(set.jwks()),\n            enumerable: false,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return localJWKSet;\n}\n", "import { J<PERSON><PERSON><PERSON>r, JWKSNoMatching<PERSON>ey, JWKSTimeout } from '../util/errors.js';\nimport { createLocalJWKSet } from './local.js';\nimport isObject from '../lib/is_object.js';\nfunction isCloudflareWorkers() {\n    return (typeof WebSocketPair !== 'undefined' ||\n        (typeof navigator !== 'undefined' && navigator.userAgent === 'Cloudflare-Workers') ||\n        (typeof EdgeRuntime !== 'undefined' && EdgeRuntime === 'vercel'));\n}\nlet USER_AGENT;\nif (typeof navigator === 'undefined' || !navigator.userAgent?.startsWith?.('Mozilla/5.0 ')) {\n    const NAME = 'jose';\n    const VERSION = 'v6.0.11';\n    USER_AGENT = `${NAME}/${VERSION}`;\n}\nexport const customFetch = Symbol();\nasync function fetchJwks(url, headers, signal, fetchImpl = fetch) {\n    const response = await fetchImpl(url, {\n        method: 'GET',\n        signal,\n        redirect: 'manual',\n        headers,\n    }).catch((err) => {\n        if (err.name === 'TimeoutError') {\n            throw new JWKSTimeout();\n        }\n        throw err;\n    });\n    if (response.status !== 200) {\n        throw new JOSEError('Expected 200 OK from the JSON Web Key Set HTTP response');\n    }\n    try {\n        return await response.json();\n    }\n    catch {\n        throw new JOSEError('Failed to parse the JSON Web Key Set HTTP response as JSON');\n    }\n}\nexport const jwksCache = Symbol();\nfunction isFreshJwksCache(input, cacheMaxAge) {\n    if (typeof input !== 'object' || input === null) {\n        return false;\n    }\n    if (!('uat' in input) || typeof input.uat !== 'number' || Date.now() - input.uat >= cacheMaxAge) {\n        return false;\n    }\n    if (!('jwks' in input) ||\n        !isObject(input.jwks) ||\n        !Array.isArray(input.jwks.keys) ||\n        !Array.prototype.every.call(input.jwks.keys, isObject)) {\n        return false;\n    }\n    return true;\n}\nclass RemoteJWKSet {\n    #url;\n    #timeoutDuration;\n    #cooldownDuration;\n    #cacheMaxAge;\n    #jwksTimestamp;\n    #pendingFetch;\n    #headers;\n    #customFetch;\n    #local;\n    #cache;\n    constructor(url, options) {\n        if (!(url instanceof URL)) {\n            throw new TypeError('url must be an instance of URL');\n        }\n        this.#url = new URL(url.href);\n        this.#timeoutDuration =\n            typeof options?.timeoutDuration === 'number' ? options?.timeoutDuration : 5000;\n        this.#cooldownDuration =\n            typeof options?.cooldownDuration === 'number' ? options?.cooldownDuration : 30000;\n        this.#cacheMaxAge = typeof options?.cacheMaxAge === 'number' ? options?.cacheMaxAge : 600000;\n        this.#headers = new Headers(options?.headers);\n        if (USER_AGENT && !this.#headers.has('User-Agent')) {\n            this.#headers.set('User-Agent', USER_AGENT);\n        }\n        if (!this.#headers.has('accept')) {\n            this.#headers.set('accept', 'application/json');\n            this.#headers.append('accept', 'application/jwk-set+json');\n        }\n        this.#customFetch = options?.[customFetch];\n        if (options?.[jwksCache] !== undefined) {\n            this.#cache = options?.[jwksCache];\n            if (isFreshJwksCache(options?.[jwksCache], this.#cacheMaxAge)) {\n                this.#jwksTimestamp = this.#cache.uat;\n                this.#local = createLocalJWKSet(this.#cache.jwks);\n            }\n        }\n    }\n    pendingFetch() {\n        return !!this.#pendingFetch;\n    }\n    coolingDown() {\n        return typeof this.#jwksTimestamp === 'number'\n            ? Date.now() < this.#jwksTimestamp + this.#cooldownDuration\n            : false;\n    }\n    fresh() {\n        return typeof this.#jwksTimestamp === 'number'\n            ? Date.now() < this.#jwksTimestamp + this.#cacheMaxAge\n            : false;\n    }\n    jwks() {\n        return this.#local?.jwks();\n    }\n    async getKey(protectedHeader, token) {\n        if (!this.#local || !this.fresh()) {\n            await this.reload();\n        }\n        try {\n            return await this.#local(protectedHeader, token);\n        }\n        catch (err) {\n            if (err instanceof JWKSNoMatchingKey) {\n                if (this.coolingDown() === false) {\n                    await this.reload();\n                    return this.#local(protectedHeader, token);\n                }\n            }\n            throw err;\n        }\n    }\n    async reload() {\n        if (this.#pendingFetch && isCloudflareWorkers()) {\n            this.#pendingFetch = undefined;\n        }\n        this.#pendingFetch ||= fetchJwks(this.#url.href, this.#headers, AbortSignal.timeout(this.#timeoutDuration), this.#customFetch)\n            .then((json) => {\n            this.#local = createLocalJWKSet(json);\n            if (this.#cache) {\n                this.#cache.uat = Date.now();\n                this.#cache.jwks = json;\n            }\n            this.#jwksTimestamp = Date.now();\n            this.#pendingFetch = undefined;\n        })\n            .catch((err) => {\n            this.#pendingFetch = undefined;\n            throw err;\n        });\n        await this.#pendingFetch;\n    }\n}\nexport function createRemoteJWKSet(url, options) {\n    const set = new RemoteJWKSet(url, options);\n    const remoteJWKSet = async (protectedHeader, token) => set.getKey(protectedHeader, token);\n    Object.defineProperties(remoteJWKSet, {\n        coolingDown: {\n            get: () => set.coolingDown(),\n            enumerable: true,\n            configurable: false,\n        },\n        fresh: {\n            get: () => set.fresh(),\n            enumerable: true,\n            configurable: false,\n        },\n        reload: {\n            value: () => set.reload(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n        reloading: {\n            get: () => set.pendingFetch(),\n            enumerable: true,\n            configurable: false,\n        },\n        jwks: {\n            value: () => set.jwks(),\n            enumerable: true,\n            configurable: false,\n            writable: false,\n        },\n    });\n    return remoteJWKSet;\n}\n", "import * as b64u from '../util/base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport { JWTInvalid } from '../util/errors.js';\nimport { validateClaimsSet, JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class UnsecuredJWT {\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    encode() {\n        const header = b64u.encode(JSON.stringify({ alg: 'none' }));\n        const payload = b64u.encode(this.#jwt.data());\n        return `${header}.${payload}.`;\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    static decode(jwt, options) {\n        if (typeof jwt !== 'string') {\n            throw new JWTInvalid('Unsecured JWT must be a string');\n        }\n        const { 0: encodedHeader, 1: encodedPayload, 2: signature, length } = jwt.split('.');\n        if (length !== 3 || signature !== '') {\n            throw new JWTInvalid('Invalid Unsecured JWT');\n        }\n        let header;\n        try {\n            header = JSON.parse(decoder.decode(b64u.decode(encodedHeader)));\n            if (header.alg !== 'none')\n                throw new Error();\n        }\n        catch {\n            throw new JWTInvalid('Invalid Unsecured JWT');\n        }\n        const payload = validateClaimsSet(header, b64u.decode(encodedPayload), options);\n        return { payload, header };\n    }\n}\n", "import { decode as b64u } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nexport function decodeProtectedHeader(token) {\n    let protectedB64u;\n    if (typeof token === 'string') {\n        const parts = token.split('.');\n        if (parts.length === 3 || parts.length === 5) {\n            ;\n            [protectedB64u] = parts;\n        }\n    }\n    else if (typeof token === 'object' && token) {\n        if ('protected' in token) {\n            protectedB64u = token.protected;\n        }\n        else {\n            throw new TypeError('Token does not contain a Protected Header');\n        }\n    }\n    try {\n        if (typeof protectedB64u !== 'string' || !protectedB64u) {\n            throw new Error();\n        }\n        const result = JSON.parse(decoder.decode(b64u(protectedB64u)));\n        if (!isObject(result)) {\n            throw new Error();\n        }\n        return result;\n    }\n    catch {\n        throw new TypeError('Invalid Token or Protected Header formatting');\n    }\n}\n", "import { decode as b64u } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nimport { JWTInvalid } from './errors.js';\nexport function decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = b64u(payload);\n    }\n    catch {\n        throw new JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(decoder.decode(decoded));\n    }\n    catch {\n        throw new JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!isObject(result))\n        throw new JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nfunction getModulusLengthOption(options) {\n    const modulusLength = options?.modulusLength ?? 2048;\n    if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n        throw new JOSENotSupported('Invalid or unsupported modulusLength option provided, 2048 bits or larger keys must be used');\n    }\n    return modulusLength;\n}\nexport async function generateKeyPair(alg, options) {\n    let algorithm;\n    let keyUsages;\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            algorithm = {\n                name: 'RSA-PSS',\n                hash: `SHA-${alg.slice(-3)}`,\n                publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n                modulusLength: getModulusLengthOption(options),\n            };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            algorithm = {\n                name: 'RSASSA-PKCS1-v1_5',\n                hash: `SHA-${alg.slice(-3)}`,\n                publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n                modulusLength: getModulusLengthOption(options),\n            };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            algorithm = {\n                name: 'RSA-OAEP',\n                hash: `SHA-${parseInt(alg.slice(-3), 10) || 1}`,\n                publicExponent: new Uint8Array([0x01, 0x00, 0x01]),\n                modulusLength: getModulusLengthOption(options),\n            };\n            keyUsages = ['decrypt', 'unwrapKey', 'encrypt', 'wrapKey'];\n            break;\n        case 'ES256':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'ES384':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'ES512':\n            algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'Ed25519':\n        case 'EdDSA': {\n            keyUsages = ['sign', 'verify'];\n            algorithm = { name: 'Ed25519' };\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            keyUsages = ['deriveBits'];\n            const crv = options?.crv ?? 'P-256';\n            switch (crv) {\n                case 'P-256':\n                case 'P-384':\n                case 'P-521': {\n                    algorithm = { name: 'ECDH', namedCurve: crv };\n                    break;\n                }\n                case 'X25519':\n                    algorithm = { name: 'X25519' };\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported crv option provided, supported values are P-256, P-384, P-521, and X25519');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, keyUsages);\n}\n", "import { JOSENotSupported } from '../util/errors.js';\nexport async function generateSecret(alg, options) {\n    let length;\n    let algorithm;\n    let keyUsages;\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512':\n            length = parseInt(alg.slice(-3), 10);\n            algorithm = { name: 'HMAC', hash: `SHA-${length}`, length };\n            keyUsages = ['sign', 'verify'];\n            break;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            length = parseInt(alg.slice(-3), 10);\n            return crypto.getRandomValues(new Uint8Array(length >> 3));\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW':\n            length = parseInt(alg.slice(1, 4), 10);\n            algorithm = { name: 'AES-KW', length };\n            keyUsages = ['wrapKey', 'unwrapKey'];\n            break;\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW':\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            length = parseInt(alg.slice(1, 4), 10);\n            algorithm = { name: 'AES-GCM', length };\n            keyUsages = ['encrypt', 'decrypt'];\n            break;\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n    }\n    return crypto.subtle.generateKey(algorithm, options?.extractable ?? false, keyUsages);\n}\n", "export { compactDecrypt } from './jwe/compact/decrypt.js';\nexport { flattenedDecrypt } from './jwe/flattened/decrypt.js';\nexport { generalDecrypt } from './jwe/general/decrypt.js';\nexport { GeneralEncrypt } from './jwe/general/encrypt.js';\nexport { compactVerify } from './jws/compact/verify.js';\nexport { flattenedVerify } from './jws/flattened/verify.js';\nexport { generalVerify } from './jws/general/verify.js';\nexport { jwtVerify } from './jwt/verify.js';\nexport { jwtDecrypt } from './jwt/decrypt.js';\nexport { CompactEncrypt } from './jwe/compact/encrypt.js';\nexport { FlattenedEncrypt } from './jwe/flattened/encrypt.js';\nexport { CompactSign } from './jws/compact/sign.js';\nexport { FlattenedSign } from './jws/flattened/sign.js';\nexport { GeneralSign } from './jws/general/sign.js';\nexport { SignJWT } from './jwt/sign.js';\nexport { EncryptJWT } from './jwt/encrypt.js';\nexport { calculateJwkThumbprint, calculateJwkThumbprintUri } from './jwk/thumbprint.js';\nexport { EmbeddedJWK } from './jwk/embedded.js';\nexport { createLocalJWKSet } from './jwks/local.js';\nexport { createRemoteJWKSet, jwksCache, customFetch } from './jwks/remote.js';\nexport { UnsecuredJWT } from './jwt/unsecured.js';\nexport { exportPKCS8, exportSPKI, exportJWK } from './key/export.js';\nexport { importSPKI, importPKCS8, importX509, importJWK } from './key/import.js';\nexport { decodeProtectedHeader } from './util/decode_protected_header.js';\nexport { decodeJwt } from './util/decode_jwt.js';\nexport * as errors from './util/errors.js';\nexport { generateKeyPair } from './key/generate_key_pair.js';\nexport { generateSecret } from './key/generate_secret.js';\nexport * as base64url from './util/base64url.js';\nexport const cryptoRuntime = 'WebCryptoAPI';\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAO,IAAM,UAAU,IAAI,YAAY;AAChC,IAAM,UAAU,IAAI,YAAY;AACvC,IAAM,YAAY,KAAK;AAChB,SAAS,UAAU,SAAS;AAC/B,QAAM,OAAO,QAAQ,OAAO,CAAC,KAAK,EAAE,OAAO,MAAM,MAAM,QAAQ,CAAC;AAChE,QAAM,MAAM,IAAI,WAAW,IAAI;AAC/B,MAAI,IAAI;AACR,aAAW,UAAU,SAAS;AAC1B,QAAI,IAAI,QAAQ,CAAC;AACjB,SAAK,OAAO;AAAA,EAChB;AACA,SAAO;AACX;AACA,SAAS,cAAc,KAAK,OAAO,QAAQ;AACvC,MAAI,QAAQ,KAAK,SAAS,WAAW;AACjC,UAAM,IAAI,WAAW,6BAA6B,YAAY,CAAC,cAAc,KAAK,EAAE;AAAA,EACxF;AACA,MAAI,IAAI,CAAC,UAAU,IAAI,UAAU,IAAI,UAAU,GAAG,QAAQ,GAAI,GAAG,MAAM;AAC3E;AACO,SAAS,SAAS,OAAO;AAC5B,QAAM,OAAO,KAAK,MAAM,QAAQ,SAAS;AACzC,QAAM,MAAM,QAAQ;AACpB,QAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,gBAAc,KAAK,MAAM,CAAC;AAC1B,gBAAc,KAAK,KAAK,CAAC;AACzB,SAAO;AACX;AACO,SAAS,SAAS,OAAO;AAC5B,QAAM,MAAM,IAAI,WAAW,CAAC;AAC5B,gBAAc,KAAK,KAAK;AACxB,SAAO;AACX;;;AC/BO,SAAS,aAAa,OAAO;AAChC,MAAI,WAAW,UAAU,UAAU;AAC/B,WAAO,MAAM,SAAS;AAAA,EAC1B;AACA,QAAM,aAAa;AACnB,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,YAAY;AAC/C,QAAI,KAAK,OAAO,aAAa,MAAM,MAAM,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC;AAAA,EAC/E;AACA,SAAO,KAAK,IAAI,KAAK,EAAE,CAAC;AAC5B;AACO,SAAS,aAAa,SAAS;AAClC,MAAI,WAAW,YAAY;AACvB,WAAO,WAAW,WAAW,OAAO;AAAA,EACxC;AACA,QAAM,SAAS,KAAK,OAAO;AAC3B,QAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;AAC1C,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAM,CAAC,IAAI,OAAO,WAAW,CAAC;AAAA,EAClC;AACA,SAAO;AACX;;;AFnBO,SAAS,OAAO,OAAO;AAC1B,MAAI,WAAW,YAAY;AACvB,WAAO,WAAW,WAAW,OAAO,UAAU,WAAW,QAAQ,QAAQ,OAAO,KAAK,GAAG;AAAA,MACpF,UAAU;AAAA,IACd,CAAC;AAAA,EACL;AACA,MAAI,UAAU;AACd,MAAI,mBAAmB,YAAY;AAC/B,cAAU,QAAQ,OAAO,OAAO;AAAA,EACpC;AACA,YAAU,QAAQ,QAAQ,MAAM,GAAG,EAAE,QAAQ,MAAM,GAAG,EAAE,QAAQ,OAAO,EAAE;AACzE,MAAI;AACA,WAAO,aAAa,OAAO;AAAA,EAC/B,QACM;AACF,UAAM,IAAI,UAAU,mDAAmD;AAAA,EAC3E;AACJ;AACO,SAAS,OAAO,OAAO;AAC1B,MAAI,YAAY;AAChB,MAAI,OAAO,cAAc,UAAU;AAC/B,gBAAY,QAAQ,OAAO,SAAS;AAAA,EACxC;AACA,MAAI,WAAW,UAAU,UAAU;AAC/B,WAAO,UAAU,SAAS,EAAE,UAAU,aAAa,aAAa,KAAK,CAAC;AAAA,EAC1E;AACA,SAAO,aAAa,SAAS,EAAE,QAAQ,MAAM,EAAE,EAAE,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAC3F;;;AG7BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAO,IAAM,YAAN,cAAwB,MAAM;AAAA,EAGjC,YAAYA,UAAS,SAAS;AAHlC,QAAAC;AAIQ,UAAMD,UAAS,OAAO;AAF1B,gCAAO;AAGH,SAAK,OAAO,KAAK,YAAY;AAC7B,KAAAC,MAAA,MAAM,sBAAN,gBAAAA,IAAA,YAA0B,MAAM,KAAK;AAAA,EACzC;AACJ;AAPI,cADS,WACF,QAAO;AAQX,IAAM,2BAAN,cAAuC,UAAU;AAAA,EAMpD,YAAYD,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AALxD,gCAAO;AACP;AACA;AACA;AAGI,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AAXI,cADS,0BACF,QAAO;AAYX,IAAM,aAAN,cAAyB,UAAU;AAAA,EAMtC,YAAYA,UAAS,SAAS,QAAQ,eAAe,SAAS,eAAe;AACzE,UAAMA,UAAS,EAAE,OAAO,EAAE,OAAO,QAAQ,QAAQ,EAAE,CAAC;AALxD,gCAAO;AACP;AACA;AACA;AAGI,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACnB;AACJ;AAXI,cADS,YACF,QAAO;AAYX,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAA1C;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,mBACF,QAAO;AAGX,IAAM,mBAAN,cAA+B,UAAU;AAAA,EAAzC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,kBACF,QAAO;AAGX,IAAM,sBAAN,cAAkC,UAAU;AAAA,EAG/C,YAAYA,WAAU,+BAA+B,SAAS;AAC1D,UAAMA,UAAS,OAAO;AAF1B,gCAAO;AAAA,EAGP;AACJ;AALI,cADS,qBACF,QAAO;AAMX,IAAM,aAAN,cAAyB,UAAU;AAAA,EAAnC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,YACF,QAAO;AAGX,IAAM,aAAN,cAAyB,UAAU;AAAA,EAAnC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,YACF,QAAO;AAGX,IAAM,aAAN,cAAyB,UAAU;AAAA,EAAnC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,YACF,QAAO;AAGX,IAAM,aAAN,cAAyB,UAAU;AAAA,EAAnC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,YACF,QAAO;AAGX,IAAM,cAAN,cAA0B,UAAU;AAAA,EAApC;AAAA;AAEH,gCAAO;AAAA;AACX;AAFI,cADS,aACF,QAAO;AAGX,IAAM,oBAAN,cAAgC,UAAU;AAAA,EAG7C,YAAYA,WAAU,mDAAmD,SAAS;AAC9E,UAAMA,UAAS,OAAO;AAF1B,gCAAO;AAAA,EAGP;AACJ;AALI,cADS,mBACF,QAAO;AAvElB;AA6EO,IAAM,2BAAN,cAAuC,UAAU;AAAA,EAIpD,YAAYA,WAAU,wDAAwD,SAAS;AACnF,UAAMA,UAAS,OAAO;AAJ1B,wBAAC;AAED,gCAAO;AAAA,EAGP;AACJ;AANK,YAAO;AACR,cAFS,0BAEF,QAAO;AAMX,IAAM,cAAN,cAA0B,UAAU;AAAA,EAGvC,YAAYA,WAAU,qBAAqB,SAAS;AAChD,UAAMA,UAAS,OAAO;AAF1B,gCAAO;AAAA,EAGP;AACJ;AALI,cADS,aACF,QAAO;AAMX,IAAM,iCAAN,cAA6C,UAAU;AAAA,EAG1D,YAAYA,WAAU,iCAAiC,SAAS;AAC5D,UAAMA,UAAS,OAAO;AAF1B,gCAAO;AAAA,EAGP;AACJ;AALI,cADS,gCACF,QAAO;;;AC5FX,SAAS,UAAU,KAAK;AAC3B,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,8BAA8B,GAAG,EAAE;AAAA,EACtE;AACJ;AACA,IAAO,aAAQ,CAAC,QAAQ,OAAO,gBAAgB,IAAI,WAAW,UAAU,GAAG,KAAK,CAAC,CAAC;;;AChBlF,IAAO,0BAAQ,CAAC,KAAK,OAAO;AACxB,MAAI,GAAG,UAAU,MAAM,UAAU,GAAG,GAAG;AACnC,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC/D;AACJ;;;ACLA,IAAO,2BAAQ,CAAC,KAAK,aAAa;AAC9B,QAAM,SAAS,IAAI,cAAc;AACjC,MAAI,WAAW,UAAU;AACrB,UAAM,IAAI,WAAW,mDAAmD,QAAQ,cAAc,MAAM,OAAO;AAAA,EAC/G;AACJ;;;ACNA,SAAS,SAAS,MAAM,OAAO,kBAAkB;AAC7C,SAAO,IAAI,UAAU,kDAAkD,IAAI,YAAY,IAAI,EAAE;AACjG;AACA,SAAS,YAAY,WAAW,MAAM;AAClC,SAAO,UAAU,SAAS;AAC9B;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,SAAS,KAAK,KAAK,MAAM,CAAC,GAAG,EAAE;AAC1C;AACA,SAAS,cAAc,KAAK;AACxB,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,MAAM,aAAa;AAAA,EACrC;AACJ;AACA,SAAS,WAAW,KAAK,OAAO;AAC5B,MAAI,SAAS,CAAC,IAAI,OAAO,SAAS,KAAK,GAAG;AACtC,UAAM,IAAI,UAAU,sEAAsE,KAAK,GAAG;AAAA,EACtG;AACJ;AACO,SAAS,kBAAkB,KAAK,KAAK,OAAO;AAC/C,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,MAAM;AAClC,cAAM,SAAS,MAAM;AACzB,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,mBAAmB;AAC/C,cAAM,SAAS,mBAAmB;AACtC,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE;AAC1C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACV,UAAI,CAAC,YAAY,IAAI,WAAW,OAAO;AACnC,cAAM,SAAS,OAAO;AAC1B,YAAM,WAAW,cAAc,GAAG;AAClC,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,sBAAsB;AACnD;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,UAAU,2CAA2C;AAAA,EACvE;AACA,aAAW,KAAK,KAAK;AACzB;AACO,SAAS,kBAAkB,KAAK,KAAK,OAAO;AAC/C,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,WAAW;AACZ,UAAI,CAAC,YAAY,IAAI,WAAW,SAAS;AACrC,cAAM,SAAS,SAAS;AAC5B,YAAM,WAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC7C,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,kBAAkB;AAC/C;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,UAAI,CAAC,YAAY,IAAI,WAAW,QAAQ;AACpC,cAAM,SAAS,QAAQ;AAC3B,YAAM,WAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC7C,YAAM,SAAS,IAAI,UAAU;AAC7B,UAAI,WAAW;AACX,cAAM,SAAS,UAAU,kBAAkB;AAC/C;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,cAAQ,IAAI,UAAU,MAAM;AAAA,QACxB,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ;AACI,gBAAM,SAAS,gBAAgB;AAAA,MACvC;AACA;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,CAAC,YAAY,IAAI,WAAW,QAAQ;AACpC,cAAM,SAAS,QAAQ;AAC3B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,UAAI,CAAC,YAAY,IAAI,WAAW,UAAU;AACtC,cAAM,SAAS,UAAU;AAC7B,YAAM,WAAW,SAAS,IAAI,MAAM,CAAC,GAAG,EAAE,KAAK;AAC/C,YAAM,SAAS,cAAc,IAAI,UAAU,IAAI;AAC/C,UAAI,WAAW;AACX,cAAM,SAAS,OAAO,QAAQ,IAAI,gBAAgB;AACtD;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,UAAU,2CAA2C;AAAA,EACvE;AACA,aAAW,KAAK,KAAK;AACzB;;;AC3IA,SAAS,QAAQ,KAAK,WAAW,OAAO;AAAxC,MAAAE;AACI,UAAQ,MAAM,OAAO,OAAO;AAC5B,MAAI,MAAM,SAAS,GAAG;AAClB,UAAM,OAAO,MAAM,IAAI;AACvB,WAAO,eAAe,MAAM,KAAK,IAAI,CAAC,QAAQ,IAAI;AAAA,EACtD,WACS,MAAM,WAAW,GAAG;AACzB,WAAO,eAAe,MAAM,CAAC,CAAC,OAAO,MAAM,CAAC,CAAC;AAAA,EACjD,OACK;AACD,WAAO,WAAW,MAAM,CAAC,CAAC;AAAA,EAC9B;AACA,MAAI,UAAU,MAAM;AAChB,WAAO,aAAa,MAAM;AAAA,EAC9B,WACS,OAAO,WAAW,cAAc,OAAO,MAAM;AAClD,WAAO,sBAAsB,OAAO,IAAI;AAAA,EAC5C,WACS,OAAO,WAAW,YAAY,UAAU,MAAM;AACnD,SAAIA,MAAA,OAAO,gBAAP,gBAAAA,IAAoB,MAAM;AAC1B,aAAO,4BAA4B,OAAO,YAAY,IAAI;AAAA,IAC9D;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAO,4BAAQ,CAAC,WAAW,UAAU;AACjC,SAAO,QAAQ,gBAAgB,QAAQ,GAAG,KAAK;AACnD;AACO,SAAS,QAAQ,KAAK,WAAW,OAAO;AAC3C,SAAO,QAAQ,eAAe,GAAG,uBAAuB,QAAQ,GAAG,KAAK;AAC5E;;;AC9BO,SAAS,gBAAgB,KAAK;AACjC,MAAI,CAAC,YAAY,GAAG,GAAG;AACnB,UAAM,IAAI,MAAM,6BAA6B;AAAA,EACjD;AACJ;AACO,SAAS,YAAY,KAAK;AAC7B,UAAO,2BAAM,OAAO,kBAAiB;AACzC;AACO,SAAS,YAAY,KAAK;AAC7B,UAAO,2BAAM,OAAO,kBAAiB;AACzC;AACA,IAAO,sBAAQ,CAAC,QAAQ;AACpB,SAAO,YAAY,GAAG,KAAK,YAAY,GAAG;AAC9C;;;ACNA,eAAe,gBAAgB,GAAG,GAAG;AACjC,MAAI,EAAE,aAAa,aAAa;AAC5B,UAAM,IAAI,UAAU,iCAAiC;AAAA,EACzD;AACA,MAAI,EAAE,aAAa,aAAa;AAC5B,UAAM,IAAI,UAAU,kCAAkC;AAAA,EAC1D;AACA,QAAM,YAAY,EAAE,MAAM,QAAQ,MAAM,UAAU;AAClD,QAAM,MAAO,MAAM,OAAO,OAAO,YAAY,WAAW,OAAO,CAAC,MAAM,CAAC;AACvE,QAAM,QAAQ,IAAI,WAAW,MAAM,OAAO,OAAO,KAAK,WAAW,KAAK,CAAC,CAAC;AACxE,QAAM,QAAQ,IAAI,WAAW,MAAM,OAAO,OAAO,KAAK,WAAW,KAAK,CAAC,CAAC;AACxE,MAAI,MAAM;AACV,MAAI,IAAI;AACR,SAAO,EAAE,IAAI,IAAI;AACb,WAAO,MAAM,CAAC,IAAI,MAAM,CAAC;AAAA,EAC7B;AACA,SAAO,QAAQ;AACnB;AACA,eAAe,WAAW,KAAK,KAAK,YAAY,IAAIC,MAAK,KAAK;AAC1D,MAAI,EAAE,eAAe,aAAa;AAC9B,UAAM,IAAI,UAAU,0BAAgB,KAAK,YAAY,CAAC;AAAA,EAC1D;AACA,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,QAAM,SAAS,MAAM,OAAO,OAAO,UAAU,OAAO,IAAI,SAAS,WAAW,CAAC,GAAG,WAAW,OAAO,CAAC,SAAS,CAAC;AAC7G,QAAM,SAAS,MAAM,OAAO,OAAO,UAAU,OAAO,IAAI,SAAS,GAAG,WAAW,CAAC,GAAG;AAAA,IAC/E,MAAM,OAAO,WAAW,CAAC;AAAA,IACzB,MAAM;AAAA,EACV,GAAG,OAAO,CAAC,MAAM,CAAC;AAClB,QAAM,UAAU,OAAO,KAAK,IAAI,YAAY,SAAS,IAAI,UAAU,CAAC,CAAC;AACrE,QAAM,cAAc,IAAI,YAAY,MAAM,OAAO,OAAO,KAAK,QAAQ,QAAQ,OAAO,GAAG,MAAM,GAAG,WAAW,CAAC,CAAC;AAC7G,MAAI;AACJ,MAAI;AACA,qBAAiB,MAAM,gBAAgBA,MAAK,WAAW;AAAA,EAC3D,QACM;AAAA,EACN;AACA,MAAI,CAAC,gBAAgB;AACjB,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACA,MAAI;AACJ,MAAI;AACA,gBAAY,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ,EAAE,IAAI,MAAM,UAAU,GAAG,QAAQ,UAAU,CAAC;AAAA,EACvG,QACM;AAAA,EACN;AACA,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACA,SAAO;AACX;AACA,eAAe,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,KAAK;AAC1D,MAAI;AACJ,MAAI,eAAe,YAAY;AAC3B,aAAS,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,WAAW,OAAO,CAAC,SAAS,CAAC;AAAA,EACpF,OACK;AACD,sBAAkB,KAAK,KAAK,SAAS;AACrC,aAAS;AAAA,EACb;AACA,MAAI;AACA,WAAO,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ;AAAA,MAC9C,gBAAgB;AAAA,MAChB;AAAA,MACA,MAAM;AAAA,MACN,WAAW;AAAA,IACf,GAAG,QAAQ,OAAO,YAAYA,IAAG,CAAC,CAAC;AAAA,EACvC,QACM;AACF,UAAM,IAAI,oBAAoB;AAAA,EAClC;AACJ;AACA,IAAO,kBAAQ,OAAO,KAAK,KAAK,YAAY,IAAIA,MAAK,QAAQ;AACzD,MAAI,CAAC,YAAY,GAAG,KAAK,EAAE,eAAe,aAAa;AACnD,UAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,aAAa,cAAc,cAAc,CAAC;AAAA,EACpG;AACA,MAAI,CAAC,IAAI;AACL,UAAM,IAAI,WAAW,mCAAmC;AAAA,EAC5D;AACA,MAAI,CAACA,MAAK;AACN,UAAM,IAAI,WAAW,gCAAgC;AAAA,EACzD;AACA,0BAAc,KAAK,EAAE;AACrB,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,eAAe;AACf,iCAAe,KAAK,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC;AACnD,aAAO,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,GAAG;AAAA,IACxD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,eAAe;AACf,iCAAe,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;AACrD,aAAO,WAAW,KAAK,KAAK,YAAY,IAAIA,MAAK,GAAG;AAAA,IACxD;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;;;ACzGA,IAAO,sBAAQ,IAAI,YAAY;AAC3B,QAAM,UAAU,QAAQ,OAAO,OAAO;AACtC,MAAI,QAAQ,WAAW,KAAK,QAAQ,WAAW,GAAG;AAC9C,WAAO;AAAA,EACX;AACA,MAAI;AACJ,aAAW,UAAU,SAAS;AAC1B,UAAM,aAAa,OAAO,KAAK,MAAM;AACrC,QAAI,CAAC,OAAO,IAAI,SAAS,GAAG;AACxB,YAAM,IAAI,IAAI,UAAU;AACxB;AAAA,IACJ;AACA,eAAW,aAAa,YAAY;AAChC,UAAI,IAAI,IAAI,SAAS,GAAG;AACpB,eAAO;AAAA,MACX;AACA,UAAI,IAAI,SAAS;AAAA,IACrB;AAAA,EACJ;AACA,SAAO;AACX;;;ACpBA,SAAS,aAAa,OAAO;AACzB,SAAO,OAAO,UAAU,YAAY,UAAU;AAClD;AACA,IAAO,oBAAQ,CAAC,UAAU;AACtB,MAAI,CAAC,aAAa,KAAK,KAAK,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM,mBAAmB;AACrF,WAAO;AAAA,EACX;AACA,MAAI,OAAO,eAAe,KAAK,MAAM,MAAM;AACvC,WAAO;AAAA,EACX;AACA,MAAI,QAAQ;AACZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC1C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACvC;AACA,SAAO,OAAO,eAAe,KAAK,MAAM;AAC5C;;;ACdA,SAAS,aAAa,KAAK,KAAK;AAC5B,MAAI,IAAI,UAAU,WAAW,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,GAAG;AACxD,UAAM,IAAI,UAAU,6BAA6B,GAAG,EAAE;AAAA,EAC1D;AACJ;AACA,SAAS,aAAa,KAAK,KAAK,OAAO;AACnC,MAAI,eAAe,YAAY;AAC3B,WAAO,OAAO,OAAO,UAAU,OAAO,KAAK,UAAU,MAAM,CAAC,KAAK,CAAC;AAAA,EACtE;AACA,oBAAkB,KAAK,KAAK,KAAK;AACjC,SAAO;AACX;AACA,eAAsB,KAAK,KAAK,KAAK,KAAK;AACtC,QAAM,YAAY,MAAM,aAAa,KAAK,KAAK,SAAS;AACxD,eAAa,WAAW,GAAG;AAC3B,QAAM,eAAe,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,EAAE,MAAM,WAAW,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAChH,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ,OAAO,cAAc,WAAW,QAAQ,CAAC;AAC/F;AACA,eAAsB,OAAO,KAAK,KAAK,cAAc;AACjD,QAAM,YAAY,MAAM,aAAa,KAAK,KAAK,WAAW;AAC1D,eAAa,WAAW,GAAG;AAC3B,QAAM,eAAe,MAAM,OAAO,OAAO,UAAU,OAAO,cAAc,WAAW,UAAU,EAAE,MAAM,WAAW,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAC9I,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,UAAU,OAAO,YAAY,CAAC;AAC5E;;;ACxBA,IAAO,iBAAQ,OAAO,WAAW,SAAS;AACtC,QAAM,eAAe,OAAO,UAAU,MAAM,EAAE,CAAC;AAC/C,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,OAAO,cAAc,IAAI,CAAC;AACxE;;;ACAA,SAAS,eAAe,OAAO;AAC3B,SAAO,OAAO,SAAS,MAAM,MAAM,GAAG,KAAK;AAC/C;AACA,eAAe,UAAU,QAAQ,MAAM,OAAO;AAC1C,QAAM,aAAa,KAAK,MAAM,QAAQ,KAAK,EAAE;AAC7C,QAAM,MAAM,IAAI,WAAW,aAAa,EAAE;AAC1C,WAAS,OAAO,GAAG,OAAO,YAAY,QAAQ;AAC1C,UAAM,MAAM,IAAI,WAAW,IAAI,OAAO,SAAS,MAAM,MAAM;AAC3D,QAAI,IAAI,SAAS,OAAO,CAAC,CAAC;AAC1B,QAAI,IAAI,QAAQ,CAAC;AACjB,QAAI,IAAI,OAAO,IAAI,OAAO,MAAM;AAChC,QAAI,IAAI,MAAM,eAAO,UAAU,GAAG,GAAG,OAAO,EAAE;AAAA,EAClD;AACA,SAAO,IAAI,MAAM,GAAG,QAAQ,CAAC;AACjC;AACA,eAAsB,UAAU,WAAW,YAAY,WAAW,WAAW,MAAM,IAAI,WAAW,CAAC,GAAG,MAAM,IAAI,WAAW,CAAC,GAAG;AAC3H,oBAAkB,WAAW,MAAM;AACnC,oBAAkB,YAAY,QAAQ,YAAY;AAClD,QAAM,QAAQ,OAAO,eAAe,QAAQ,OAAO,SAAS,CAAC,GAAG,eAAe,GAAG,GAAG,eAAe,GAAG,GAAG,SAAS,SAAS,CAAC;AAC7H,MAAI;AACJ,MAAI,UAAU,UAAU,SAAS,UAAU;AACvC,aAAS;AAAA,EACb,OACK;AACD,aACI,KAAK,KAAK,SAAS,UAAU,UAAU,WAAW,MAAM,EAAE,GAAG,EAAE,IAAI,CAAC,KAAK;AAAA,EACjF;AACA,QAAM,eAAe,IAAI,WAAW,MAAM,OAAO,OAAO,WAAW;AAAA,IAC/D,MAAM,UAAU,UAAU;AAAA,IAC1B,QAAQ;AAAA,EACZ,GAAG,YAAY,MAAM,CAAC;AACtB,SAAO,UAAU,cAAc,WAAW,KAAK;AACnD;AACO,SAAS,QAAQ,KAAK;AACzB,UAAQ,IAAI,UAAU,YAAY;AAAA,IAC9B,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO,IAAI,UAAU,SAAS;AAAA,EACtC;AACJ;;;ACxCA,SAASC,cAAa,KAAK,KAAK;AAC5B,MAAI,eAAe,YAAY;AAC3B,WAAO,OAAO,OAAO,UAAU,OAAO,KAAK,UAAU,OAAO,CAAC,YAAY,CAAC;AAAA,EAC9E;AACA,oBAAkB,KAAK,KAAK,YAAY;AACxC,SAAO;AACX;AACA,IAAM,aAAa,CAAC,KAAK,aAAa,OAAO,QAAQ,OAAO,GAAG,GAAG,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,QAAQ;AAC/F,eAAeC,WAAU,KAAK,KAAK,KAAK,KAAK;AACzC,MAAI,EAAE,eAAe,eAAe,IAAI,SAAS,GAAG;AAChD,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACA,QAAM,OAAO,WAAW,KAAK,GAAG;AAChC,QAAM,SAAS,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE;AAC7C,QAAM,YAAY;AAAA,IACd,MAAM,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAAA,IAC7B,YAAY;AAAA,IACZ,MAAM;AAAA,IACN;AAAA,EACJ;AACA,QAAM,YAAY,MAAMD,cAAa,KAAK,GAAG;AAC7C,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,WAAW,WAAW,WAAW,MAAM,CAAC;AACtF;AACA,eAAsBE,MAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,OAAO,gBAAgB,IAAI,WAAW,EAAE,CAAC,GAAG;AACpG,QAAM,UAAU,MAAMD,WAAU,KAAK,KAAK,KAAK,GAAG;AAClD,QAAM,eAAe,MAAY,KAAK,IAAI,MAAM,EAAE,GAAG,SAAS,GAAG;AACjE,SAAO,EAAE,cAAc,KAAK,KAAK,OAAK,GAAG,EAAE;AAC/C;AACA,eAAsBE,QAAO,KAAK,KAAK,cAAc,KAAK,KAAK;AAC3D,QAAM,UAAU,MAAMF,WAAU,KAAK,KAAK,KAAK,GAAG;AAClD,SAAa,OAAO,IAAI,MAAM,EAAE,GAAG,SAAS,YAAY;AAC5D;;;ACpCA,IAAO,2BAAQ,CAAC,KAAK,QAAQ;AACzB,MAAI,IAAI,WAAW,IAAI,KAAK,IAAI,WAAW,IAAI,GAAG;AAC9C,UAAM,EAAE,cAAc,IAAI,IAAI;AAC9B,QAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;AAC3D,YAAM,IAAI,UAAU,GAAG,GAAG,uDAAuD;AAAA,IACrF;AAAA,EACJ;AACJ;;;ACJA,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EAC1G;AACJ;AACA,eAAsB,QAAQ,KAAK,KAAK,KAAK;AACzC,oBAAkB,KAAK,KAAK,SAAS;AACrC,2BAAe,KAAK,GAAG;AACvB,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ,gBAAgB,GAAG,GAAG,KAAK,GAAG,CAAC;AACrF;AACA,eAAsB,QAAQ,KAAK,KAAK,cAAc;AAClD,oBAAkB,KAAK,KAAK,SAAS;AACrC,2BAAe,KAAK,GAAG;AACvB,SAAO,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ,gBAAgB,GAAG,GAAG,KAAK,YAAY,CAAC;AAC9F;;;ACtBO,SAASG,WAAU,KAAK;AAC3B,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,8BAA8B,GAAG,EAAE;AAAA,EACtE;AACJ;AACA,IAAO,cAAQ,CAAC,QAAQ,OAAO,gBAAgB,IAAI,WAAWA,WAAU,GAAG,KAAK,CAAC,CAAC;;;ACdlF,IAAM,YAAY,CAAC,KAAK,eAAe;AACnC,QAAM,YAAY,IAAI,MAAM,UAAU,KAAK,CAAC,GAAG,KAAK,IAAI;AACxD,SAAO,cAAc,UAAU;AAAA,EAAU,QAAQ;AAAA,WAAc,UAAU;AAC7E;AACA,IAAM,gBAAgB,OAAO,SAAS,WAAW,QAAQ;AACrD,MAAI,YAAY,GAAG,GAAG;AAClB,QAAI,IAAI,SAAS,SAAS;AACtB,YAAM,IAAI,UAAU,gBAAgB,OAAO,MAAM;AAAA,IACrD;AACA,WAAO,IAAI,OAAO,EAAE,QAAQ,OAAO,MAAM,UAAU,CAAC;AAAA,EACxD;AACA,MAAI,CAAC,YAAY,GAAG,GAAG;AACnB,UAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,WAAW,CAAC;AAAA,EACtE;AACA,MAAI,CAAC,IAAI,aAAa;AAClB,UAAM,IAAI,UAAU,8BAA8B;AAAA,EACtD;AACA,MAAI,IAAI,SAAS,SAAS;AACtB,UAAM,IAAI,UAAU,gBAAgB,OAAO,MAAM;AAAA,EACrD;AACA,SAAO,UAAU,aAAa,IAAI,WAAW,MAAM,OAAO,OAAO,UAAU,WAAW,GAAG,CAAC,CAAC,GAAG,GAAG,QAAQ,YAAY,CAAC,MAAM;AAChI;AACO,IAAM,SAAS,CAAC,QAAQ;AAC3B,SAAO,cAAc,UAAU,QAAQ,GAAG;AAC9C;AACO,IAAM,UAAU,CAAC,QAAQ;AAC5B,SAAO,cAAc,WAAW,SAAS,GAAG;AAChD;AACA,IAAM,UAAU,CAAC,SAAS,KAAK,OAAO,MAAM;AACxC,MAAI,SAAS,GAAG;AACZ,QAAI,QAAQ,IAAI,MAAM;AACtB,QAAI,QAAQ,CAAI;AAAA,EACpB;AACA,QAAM,IAAI,QAAQ,QAAQ,IAAI,CAAC,GAAG,IAAI;AACtC,MAAI,MAAM;AACN,WAAO;AACX,QAAM,MAAM,QAAQ,SAAS,GAAG,IAAI,IAAI,MAAM;AAC9C,MAAI,IAAI,WAAW,IAAI;AACnB,WAAO;AACX,SAAO,IAAI,MAAM,CAAC,OAAO,UAAU,UAAU,IAAI,KAAK,CAAC,KAAK,QAAQ,SAAS,KAAK,IAAI,CAAC;AAC3F;AACA,IAAMC,iBAAgB,CAAC,YAAY;AAC/B,UAAQ,MAAM;AAAA,IACV,KAAK,QAAQ,SAAS,CAAC,IAAM,KAAM,IAAM,KAAM,IAAM,GAAM,GAAM,CAAI,CAAC;AAClE,aAAO;AAAA,IACX,KAAK,QAAQ,SAAS,CAAC,IAAM,KAAM,GAAM,GAAM,EAAI,CAAC;AAChD,aAAO;AAAA,IACX,KAAK,QAAQ,SAAS,CAAC,IAAM,KAAM,GAAM,GAAM,EAAI,CAAC;AAChD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,IAAM,gBAAgB,OAAO,SAAS,WAAW,KAAK,KAAK,YAAY;AACnE,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,WAAW,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC,EACvD,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;AAChC,QAAM,WAAW,cAAc;AAC/B,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC,GAAG;AAC5D,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY,EAAE,MAAM,qBAAqB,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC,GAAG;AACtE,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY;AAAA,QACR,MAAM;AAAA,QACN,MAAM,OAAO,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC;AAAA,MACjD;AACA,kBAAY,WAAW,CAAC,WAAW,SAAS,IAAI,CAAC,WAAW,WAAW;AACvE;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,YAAM,aAAaA,eAAc,OAAO;AACxC,mBAAY,yCAAY,WAAW,SAAQ,EAAE,MAAM,QAAQ,WAAW,IAAI,EAAE,MAAM,SAAS;AAC3F,kBAAY,WAAW,CAAC,IAAI,CAAC,YAAY;AACzC;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AACD,kBAAY,EAAE,MAAM,UAAU;AAC9B,kBAAY,WAAW,CAAC,QAAQ,IAAI,CAAC,MAAM;AAC3C;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,gDAAgD;AAAA,EACnF;AACA,SAAO,OAAO,OAAO,UAAU,WAAW,SAAS,YAAW,mCAAS,iBAAgB,WAAW,OAAO,QAAQ,SAAS;AAC9H;AACO,IAAM,YAAY,CAAC,KAAK,KAAK,YAAY;AAC5C,SAAO,cAAc,+CAA+C,SAAS,KAAK,KAAK,OAAO;AAClG;AACO,IAAM,WAAW,CAAC,KAAK,KAAK,YAAY;AAC3C,SAAO,cAAc,8CAA8C,QAAQ,KAAK,KAAK,OAAO;AAChG;AACA,SAAS,WAAW,KAAK;AACrB,QAAM,SAAS,CAAC;AAChB,MAAI,OAAO;AACX,SAAO,OAAO,IAAI,QAAQ;AACtB,UAAM,WAAW,aAAa,IAAI,SAAS,IAAI,CAAC;AAChD,WAAO,KAAK,QAAQ;AACpB,YAAQ,SAAS;AAAA,EACrB;AACA,SAAO;AACX;AACA,SAAS,aAAa,OAAO;AACzB,MAAI,WAAW;AACf,MAAIC,OAAM,MAAM,CAAC,IAAI;AACrB;AACA,MAAIA,SAAQ,IAAM;AACd,IAAAA,OAAM;AACN,WAAO,MAAM,QAAQ,KAAK,KAAM;AAC5B,MAAAA,OAAMA,OAAM,MAAM,MAAM,QAAQ,IAAI;AACpC;AAAA,IACJ;AACA,IAAAA,OAAMA,OAAM,MAAM,MAAM,QAAQ,IAAI;AACpC;AAAA,EACJ;AACA,MAAI,SAAS;AACb,MAAI,MAAM,QAAQ,IAAI,KAAM;AACxB,aAAS,MAAM,QAAQ;AACvB;AAAA,EACJ,WACS,WAAW,KAAM;AACtB,aAAS;AACT,WAAO,MAAM,WAAW,MAAM,MAAM,KAAK,MAAM,WAAW,SAAS,CAAC,MAAM,GAAG;AACzE,UAAI,SAAS,MAAM,YAAY;AAC3B,cAAM,IAAI,UAAU,gCAAgC;AAAA,MACxD;AACA;AAAA,IACJ;AACA,UAAMC,cAAa,WAAW,SAAS;AACvC,WAAO;AAAA,MACH,YAAAA;AAAA,MACA,UAAU,MAAM,SAAS,UAAU,WAAW,MAAM;AAAA,MACpD,KAAK,MAAM,SAAS,GAAGA,WAAU;AAAA,IACrC;AAAA,EACJ,OACK;AACD,UAAM,iBAAiB,MAAM,QAAQ,IAAI;AACzC;AACA,aAAS;AACT,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK;AACrC,eAAS,SAAS,MAAM,MAAM,QAAQ;AACtC;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,aAAa,WAAW;AAC9B,SAAO;AAAA,IACH;AAAA,IACA,UAAU,MAAM,SAAS,UAAU,UAAU;AAAA,IAC7C,KAAK,MAAM,SAAS,GAAG,UAAU;AAAA,EACrC;AACJ;AACA,SAAS,aAAa,KAAK;AACvB,QAAM,iBAAiB,WAAW,WAAW,aAAa,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ;AACpF,SAAO,aAAa,eAAe,eAAe,CAAC,EAAE,IAAI,CAAC,MAAM,MAAO,IAAI,CAAC,EAAE,GAAG;AACrF;AACA,IAAI;AACJ,SAAS,QAAQ,MAAM;AA5LvB,MAAAC,KAAAC,KAAA;AA6LI,MAAI;AACA,2CAAoB,MAAAA,OAAAD,MAAA,WAAW,YAAX,gBAAAA,IAAoB,qBAApB,gBAAAC,IAAA,KAAAD,KAAuC,mBAAvC,mBAAuD;AAAA,EAC/E,QACM;AACF,sBAAkB;AAAA,EACtB;AACA,MAAI,iBAAiB;AACjB,QAAI;AACA,aAAO,IAAI,gBAAgB,IAAI,EAAE,OAAO,EAAE,QAAQ,OAAO,MAAM,OAAO,CAAC;AAAA,IAC3E,QACM;AAAA,IAAE;AAAA,EACZ;AACA,QAAM,MAAM,KAAK,QAAQ,+CAA+C,EAAE;AAC1E,QAAM,MAAM,aAAa,GAAG;AAC5B,SAAO,UAAU,aAAa,GAAG,GAAG,YAAY;AACpD;AACO,IAAM,WAAW,CAAC,KAAK,KAAK,YAAY;AAC3C,MAAI;AACJ,MAAI;AACA,WAAO,QAAQ,GAAG;AAAA,EACtB,SACO,OAAO;AACV,UAAM,IAAI,UAAU,yCAAyC,EAAE,MAAM,CAAC;AAAA,EAC1E;AACA,SAAO,SAAS,MAAM,KAAK,OAAO;AACtC;;;ACrNA,SAAS,cAAc,KAAK;AACxB,MAAI;AACJ,MAAI;AACJ,UAAQ,IAAI,KAAK;AAAA,IACb,KAAK,OAAO;AACR,cAAQ,IAAI,KAAK;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,sBAAY,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI,IAAI,MAAM,EAAE,CAAC,GAAG;AAChE,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,sBAAY,EAAE,MAAM,qBAAqB,MAAM,OAAO,IAAI,IAAI,MAAM,EAAE,CAAC,GAAG;AAC1E,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,sBAAY;AAAA,YACR,MAAM;AAAA,YACN,MAAM,OAAO,SAAS,IAAI,IAAI,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC;AAAA,UACrD;AACA,sBAAY,IAAI,IAAI,CAAC,WAAW,WAAW,IAAI,CAAC,WAAW,SAAS;AACpE;AAAA,QACJ;AACI,gBAAM,IAAI,iBAAiB,8DAA8D;AAAA,MACjG;AACA;AAAA,IACJ;AAAA,IACA,KAAK,MAAM;AACP,cAAQ,IAAI,KAAK;AAAA,QACb,KAAK;AACD,sBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AACD,sBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AACD,sBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,sBAAY,EAAE,MAAM,QAAQ,YAAY,IAAI,IAAI;AAChD,sBAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC;AACtC;AAAA,QACJ;AACI,gBAAM,IAAI,iBAAiB,8DAA8D;AAAA,MACjG;AACA;AAAA,IACJ;AAAA,IACA,KAAK,OAAO;AACR,cAAQ,IAAI,KAAK;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACD,sBAAY,EAAE,MAAM,UAAU;AAC9B,sBAAY,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ;AACxC;AAAA,QACJ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD,sBAAY,EAAE,MAAM,IAAI,IAAI;AAC5B,sBAAY,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC;AACtC;AAAA,QACJ;AACI,gBAAM,IAAI,iBAAiB,8DAA8D;AAAA,MACjG;AACA;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,iBAAiB,6DAA6D;AAAA,EAChG;AACA,SAAO,EAAE,WAAW,UAAU;AAClC;AACA,IAAO,qBAAQ,OAAO,QAAQ;AAC1B,MAAI,CAAC,IAAI,KAAK;AACV,UAAM,IAAI,UAAU,0DAA0D;AAAA,EAClF;AACA,QAAM,EAAE,WAAW,UAAU,IAAI,cAAc,GAAG;AAClD,QAAM,UAAU,EAAE,GAAG,IAAI;AACzB,SAAO,QAAQ;AACf,SAAO,QAAQ;AACf,SAAO,OAAO,OAAO,UAAU,OAAO,SAAS,WAAW,IAAI,QAAQ,IAAI,IAAI,QAAQ,OAAO,IAAI,WAAW,SAAS;AACzH;;;ACxFA,eAAsB,WAAW,MAAM,KAAK,SAAS;AACjD,MAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,4BAA4B,MAAM,GAAG;AAC9E,UAAM,IAAI,UAAU,sCAAsC;AAAA,EAC9D;AACA,SAAO,SAAS,MAAM,KAAK,OAAO;AACtC;AACA,eAAsB,WAAW,MAAM,KAAK,SAAS;AACjD,MAAI,OAAO,SAAS,YAAY,KAAK,QAAQ,6BAA6B,MAAM,GAAG;AAC/E,UAAM,IAAI,UAAU,uCAAuC;AAAA,EAC/D;AACA,SAAO,SAAS,MAAM,KAAK,OAAO;AACtC;AACA,eAAsB,YAAY,OAAO,KAAK,SAAS;AACnD,MAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,6BAA6B,MAAM,GAAG;AACjF,UAAM,IAAI,UAAU,yCAAyC;AAAA,EACjE;AACA,SAAO,UAAU,OAAO,KAAK,OAAO;AACxC;AACA,eAAsB,UAAU,KAAK,KAAK,SAAS;AAC/C,MAAI,CAAC,kBAAS,GAAG,GAAG;AAChB,UAAM,IAAI,UAAU,uBAAuB;AAAA,EAC/C;AACA,MAAI;AACJ,gBAAQ,IAAI;AACZ,iBAAQ,mCAAS,gBAAe,IAAI;AACpC,UAAQ,IAAI,KAAK;AAAA,IACb,KAAK;AACD,UAAI,OAAO,IAAI,MAAM,YAAY,CAAC,IAAI,GAAG;AACrC,cAAM,IAAI,UAAU,yCAAyC;AAAA,MACjE;AACA,aAAO,OAAgB,IAAI,CAAC;AAAA,IAChC,KAAK;AACD,UAAI,SAAS,OAAO,IAAI,QAAQ,QAAW;AACvC,cAAM,IAAI,iBAAiB,oEAAoE;AAAA,MACnG;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO,mBAAY,EAAE,GAAG,KAAK,KAAK,IAAI,CAAC;AAAA,IAC3C;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;;;ACtCA,eAAe,WAAW,KAAK,WAAW,KAAK,IAAI,KAAK;AACpD,MAAI,EAAE,eAAe,aAAa;AAC9B,UAAM,IAAI,UAAU,0BAAgB,KAAK,YAAY,CAAC;AAAA,EAC1D;AACA,QAAM,UAAU,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AAC5C,QAAM,SAAS,MAAM,OAAO,OAAO,UAAU,OAAO,IAAI,SAAS,WAAW,CAAC,GAAG,WAAW,OAAO,CAAC,SAAS,CAAC;AAC7G,QAAM,SAAS,MAAM,OAAO,OAAO,UAAU,OAAO,IAAI,SAAS,GAAG,WAAW,CAAC,GAAG;AAAA,IAC/E,MAAM,OAAO,WAAW,CAAC;AAAA,IACzB,MAAM;AAAA,EACV,GAAG,OAAO,CAAC,MAAM,CAAC;AAClB,QAAM,aAAa,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ;AAAA,IAC1D;AAAA,IACA,MAAM;AAAA,EACV,GAAG,QAAQ,SAAS,CAAC;AACrB,QAAM,UAAU,OAAO,KAAK,IAAI,YAAY,SAAS,IAAI,UAAU,CAAC,CAAC;AACrE,QAAME,OAAM,IAAI,YAAY,MAAM,OAAO,OAAO,KAAK,QAAQ,QAAQ,OAAO,GAAG,MAAM,GAAG,WAAW,CAAC,CAAC;AACrG,SAAO,EAAE,YAAY,KAAAA,MAAK,GAAG;AACjC;AACA,eAAe,WAAW,KAAK,WAAW,KAAK,IAAI,KAAK;AACpD,MAAI;AACJ,MAAI,eAAe,YAAY;AAC3B,aAAS,MAAM,OAAO,OAAO,UAAU,OAAO,KAAK,WAAW,OAAO,CAAC,SAAS,CAAC;AAAA,EACpF,OACK;AACD,sBAAkB,KAAK,KAAK,SAAS;AACrC,aAAS;AAAA,EACb;AACA,QAAM,YAAY,IAAI,WAAW,MAAM,OAAO,OAAO,QAAQ;AAAA,IACzD,gBAAgB;AAAA,IAChB;AAAA,IACA,MAAM;AAAA,IACN,WAAW;AAAA,EACf,GAAG,QAAQ,SAAS,CAAC;AACrB,QAAMA,OAAM,UAAU,MAAM,GAAG;AAC/B,QAAM,aAAa,UAAU,MAAM,GAAG,GAAG;AACzC,SAAO,EAAE,YAAY,KAAAA,MAAK,GAAG;AACjC;AACA,IAAO,kBAAQ,OAAO,KAAK,WAAW,KAAK,IAAI,QAAQ;AACnD,MAAI,CAAC,YAAY,GAAG,KAAK,EAAE,eAAe,aAAa;AACnD,UAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,aAAa,cAAc,cAAc,CAAC;AAAA,EACpG;AACA,MAAI,IAAI;AACJ,4BAAc,KAAK,EAAE;AAAA,EACzB,OACK;AACD,SAAK,WAAW,GAAG;AAAA,EACvB;AACA,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,eAAe,YAAY;AAC3B,iCAAe,KAAK,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC;AAAA,MACnD;AACA,aAAO,WAAW,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,IAClD,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,UAAI,eAAe,YAAY;AAC3B,iCAAe,KAAK,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,CAAC;AAAA,MACrD;AACA,aAAO,WAAW,KAAK,WAAW,KAAK,IAAI,GAAG;AAAA,IAClD;AACI,YAAM,IAAI,iBAAiB,8CAA8C;AAAA,EACjF;AACJ;;;ACtEA,eAAsBC,MAAK,KAAK,KAAK,KAAK,IAAI;AAC1C,QAAM,eAAe,IAAI,MAAM,GAAG,CAAC;AACnC,QAAM,UAAU,MAAM,gBAAQ,cAAc,KAAK,KAAK,IAAI,IAAI,WAAW,CAAC,CAAC;AAC3E,SAAO;AAAA,IACH,cAAc,QAAQ;AAAA,IACtB,IAAI,OAAK,QAAQ,EAAE;AAAA,IACnB,KAAK,OAAK,QAAQ,GAAG;AAAA,EACzB;AACJ;AACA,eAAsBC,QAAO,KAAK,KAAK,cAAc,IAAIC,MAAK;AAC1D,QAAM,eAAe,IAAI,MAAM,GAAG,CAAC;AACnC,SAAO,gBAAQ,cAAc,KAAK,cAAc,IAAIA,MAAK,IAAI,WAAW,CAAC,CAAC;AAC9E;;;ACJA,IAAO,iCAAQ,OAAO,KAAK,KAAK,cAAc,YAAY,YAAY;AAClE,UAAQ,KAAK;AAAA,IACT,KAAK,OAAO;AACR,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,0CAA0C;AACnE,aAAO;AAAA,IACX;AAAA,IACA,KAAK;AACD,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,0CAA0C;AAAA,IACvE,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,UAAI,CAAC,kBAAS,WAAW,GAAG;AACxB,cAAM,IAAI,WAAW,6DAA6D;AACtF,sBAAgB,GAAG;AACnB,UAAI,CAAQ,QAAQ,GAAG;AACnB,cAAM,IAAI,iBAAiB,uFAAuF;AACtH,YAAM,MAAM,MAAM,UAAU,WAAW,KAAK,GAAG;AAC/C,sBAAgB,GAAG;AACnB,UAAI;AACJ,UAAI;AACJ,UAAI,WAAW,QAAQ,QAAW;AAC9B,YAAI,OAAO,WAAW,QAAQ;AAC1B,gBAAM,IAAI,WAAW,kDAAkD;AAC3E,YAAI;AACA,uBAAa,OAAK,WAAW,GAAG;AAAA,QACpC,QACM;AACF,gBAAM,IAAI,WAAW,oCAAoC;AAAA,QAC7D;AAAA,MACJ;AACA,UAAI,WAAW,QAAQ,QAAW;AAC9B,YAAI,OAAO,WAAW,QAAQ;AAC1B,gBAAM,IAAI,WAAW,kDAAkD;AAC3E,YAAI;AACA,uBAAa,OAAK,WAAW,GAAG;AAAA,QACpC,QACM;AACF,gBAAM,IAAI,WAAW,oCAAoC;AAAA,QAC7D;AAAA,MACJ;AACA,YAAM,eAAe,MAAa,UAAU,KAAK,KAAK,QAAQ,YAAY,WAAW,MAAM,KAAK,QAAQ,YAAYC,WAAU,WAAW,GAAG,IAAI,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,YAAY,UAAU;AACvM,UAAI,QAAQ;AACR,eAAO;AACX,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAa,OAAO,IAAI,MAAM,EAAE,GAAG,cAAc,YAAY;AAAA,IACjE;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,sBAAgB,GAAG;AACnB,aAAa,QAAQ,KAAK,KAAK,YAAY;AAAA,IAC/C;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,sBAAsB;AACvB,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,oDAAoD;AAC7E,YAAM,YAAW,mCAAS,kBAAiB;AAC3C,UAAI,WAAW,MAAM;AACjB,cAAM,IAAI,WAAW,6DAA6D;AACtF,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,mDAAmD;AAC5E,UAAI;AACJ,UAAI;AACA,cAAM,OAAK,WAAW,GAAG;AAAA,MAC7B,QACM;AACF,cAAM,IAAI,WAAW,oCAAoC;AAAA,MAC7D;AACA,aAAeC,QAAO,KAAK,KAAK,cAAc,WAAW,KAAK,GAAG;AAAA,IACrE;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAa,OAAO,KAAK,KAAK,YAAY;AAAA,IAC9C;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,aAAa;AACd,UAAI,iBAAiB;AACjB,cAAM,IAAI,WAAW,2BAA2B;AACpD,UAAI,OAAO,WAAW,OAAO;AACzB,cAAM,IAAI,WAAW,6DAA6D;AACtF,UAAI,OAAO,WAAW,QAAQ;AAC1B,cAAM,IAAI,WAAW,2DAA2D;AACpF,UAAI;AACJ,UAAI;AACA,aAAK,OAAK,WAAW,EAAE;AAAA,MAC3B,QACM;AACF,cAAM,IAAI,WAAW,mCAAmC;AAAA,MAC5D;AACA,UAAIC;AACJ,UAAI;AACA,QAAAA,OAAM,OAAK,WAAW,GAAG;AAAA,MAC7B,QACM;AACF,cAAM,IAAI,WAAW,oCAAoC;AAAA,MAC7D;AACA,aAAOD,QAAS,KAAK,KAAK,cAAc,IAAIC,IAAG;AAAA,IACnD;AAAA,IACA,SAAS;AACL,YAAM,IAAI,iBAAiB,2DAA2D;AAAA,IAC1F;AAAA,EACJ;AACJ;;;AC7HA,IAAO,wBAAQ,CAAC,KAAK,mBAAmB,kBAAkB,iBAAiB,eAAe;AACtF,MAAI,WAAW,SAAS,WAAa,mDAAiB,UAAS,QAAW;AACtE,UAAM,IAAI,IAAI,gEAAgE;AAAA,EAClF;AACA,MAAI,CAAC,mBAAmB,gBAAgB,SAAS,QAAW;AACxD,WAAO,oBAAI,IAAI;AAAA,EACnB;AACA,MAAI,CAAC,MAAM,QAAQ,gBAAgB,IAAI,KACnC,gBAAgB,KAAK,WAAW,KAChC,gBAAgB,KAAK,KAAK,CAAC,UAAU,OAAO,UAAU,YAAY,MAAM,WAAW,CAAC,GAAG;AACvF,UAAM,IAAI,IAAI,uFAAuF;AAAA,EACzG;AACA,MAAI;AACJ,MAAI,qBAAqB,QAAW;AAChC,iBAAa,IAAI,IAAI,CAAC,GAAG,OAAO,QAAQ,gBAAgB,GAAG,GAAG,kBAAkB,QAAQ,CAAC,CAAC;AAAA,EAC9F,OACK;AACD,iBAAa;AAAA,EACjB;AACA,aAAW,aAAa,gBAAgB,MAAM;AAC1C,QAAI,CAAC,WAAW,IAAI,SAAS,GAAG;AAC5B,YAAM,IAAI,iBAAiB,+BAA+B,SAAS,qBAAqB;AAAA,IAC5F;AACA,QAAI,WAAW,SAAS,MAAM,QAAW;AACrC,YAAM,IAAI,IAAI,+BAA+B,SAAS,cAAc;AAAA,IACxE;AACA,QAAI,WAAW,IAAI,SAAS,KAAK,gBAAgB,SAAS,MAAM,QAAW;AACvE,YAAM,IAAI,IAAI,+BAA+B,SAAS,+BAA+B;AAAA,IACzF;AAAA,EACJ;AACA,SAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC;;;AChCA,IAAO,8BAAQ,CAAC,QAAQ,eAAe;AACnC,MAAI,eAAe,WACd,CAAC,MAAM,QAAQ,UAAU,KAAK,WAAW,KAAK,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI;AAC/E,UAAM,IAAI,UAAU,IAAI,MAAM,sCAAsC;AAAA,EACxE;AACA,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,SAAO,IAAI,IAAI,UAAU;AAC7B;;;ACRO,SAAS,MAAM,KAAK;AACvB,SAAO,kBAAS,GAAG,KAAK,OAAO,IAAI,QAAQ;AAC/C;AACO,SAAS,aAAa,KAAK;AAC9B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;AACO,SAAS,YAAY,KAAK;AAC7B,SAAO,IAAI,QAAQ,SAAS,OAAO,IAAI,MAAM;AACjD;;;ACRA,IAAI;AACJ,IAAM,YAAY,OAAO,KAAK,KAAK,KAAK,SAAS,UAAU;AACvD,oBAAU,oBAAI,QAAQ;AACtB,MAAI,SAAS,MAAM,IAAI,GAAG;AAC1B,MAAI,iCAAS,MAAM;AACf,WAAO,OAAO,GAAG;AAAA,EACrB;AACA,QAAM,YAAY,MAAM,mBAAU,EAAE,GAAG,KAAK,IAAI,CAAC;AACjD,MAAI;AACA,WAAO,OAAO,GAAG;AACrB,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,KAAK,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC;AAAA,EACvC,OACK;AACD,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAM,kBAAkB,CAAC,WAAW,QAAQ;AAtB5C,MAAAC;AAuBI,oBAAU,oBAAI,QAAQ;AACtB,MAAI,SAAS,MAAM,IAAI,SAAS;AAChC,MAAI,iCAAS,MAAM;AACf,WAAO,OAAO,GAAG;AAAA,EACrB;AACA,QAAM,WAAW,UAAU,SAAS;AACpC,QAAM,cAAc,WAAW,OAAO;AACtC,MAAI;AACJ,MAAI,UAAU,sBAAsB,UAAU;AAC1C,YAAQ,KAAK;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD;AAAA,MACJ;AACI,cAAM,IAAI,UAAU,4DAA4D;AAAA,IACxF;AACA,gBAAY,UAAU,YAAY,UAAU,mBAAmB,aAAa,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AAAA,EAC9G;AACA,MAAI,UAAU,sBAAsB,WAAW;AAC3C,QAAI,QAAQ,WAAW,QAAQ,WAAW;AACtC,YAAM,IAAI,UAAU,4DAA4D;AAAA,IACpF;AACA,gBAAY,UAAU,YAAY,UAAU,mBAAmB,aAAa;AAAA,MACxE,WAAW,WAAW;AAAA,IAC1B,CAAC;AAAA,EACL;AACA,MAAI,UAAU,sBAAsB,OAAO;AACvC,QAAI;AACJ,YAAQ,KAAK;AAAA,MACT,KAAK;AACD,eAAO;AACP;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AACP;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AACP;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,eAAO;AACP;AAAA,MACJ;AACI,cAAM,IAAI,UAAU,4DAA4D;AAAA,IACxF;AACA,QAAI,IAAI,WAAW,UAAU,GAAG;AAC5B,aAAO,UAAU,YAAY;AAAA,QACzB,MAAM;AAAA,QACN;AAAA,MACJ,GAAG,aAAa,WAAW,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC;AAAA,IACxD;AACA,gBAAY,UAAU,YAAY;AAAA,MAC9B,MAAM,IAAI,WAAW,IAAI,IAAI,YAAY;AAAA,MACzC;AAAA,IACJ,GAAG,aAAa,CAAC,WAAW,WAAW,MAAM,CAAC;AAAA,EAClD;AACA,MAAI,UAAU,sBAAsB,MAAM;AACtC,UAAM,OAAO,oBAAI,IAAI;AAAA,MACjB,CAAC,cAAc,OAAO;AAAA,MACtB,CAAC,aAAa,OAAO;AAAA,MACrB,CAAC,aAAa,OAAO;AAAA,IACzB,CAAC;AACD,UAAM,aAAa,KAAK,KAAIA,MAAA,UAAU,yBAAV,gBAAAA,IAAgC,UAAU;AACtE,QAAI,CAAC,YAAY;AACb,YAAM,IAAI,UAAU,4DAA4D;AAAA,IACpF;AACA,QAAI,QAAQ,WAAW,eAAe,SAAS;AAC3C,kBAAY,UAAU,YAAY;AAAA,QAC9B,MAAM;AAAA,QACN;AAAA,MACJ,GAAG,aAAa,CAAC,WAAW,WAAW,MAAM,CAAC;AAAA,IAClD;AACA,QAAI,QAAQ,WAAW,eAAe,SAAS;AAC3C,kBAAY,UAAU,YAAY;AAAA,QAC9B,MAAM;AAAA,QACN;AAAA,MACJ,GAAG,aAAa,CAAC,WAAW,WAAW,MAAM,CAAC;AAAA,IAClD;AACA,QAAI,QAAQ,WAAW,eAAe,SAAS;AAC3C,kBAAY,UAAU,YAAY;AAAA,QAC9B,MAAM;AAAA,QACN;AAAA,MACJ,GAAG,aAAa,CAAC,WAAW,WAAW,MAAM,CAAC;AAAA,IAClD;AACA,QAAI,IAAI,WAAW,SAAS,GAAG;AAC3B,kBAAY,UAAU,YAAY;AAAA,QAC9B,MAAM;AAAA,QACN;AAAA,MACJ,GAAG,aAAa,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC;AAAA,IAClD;AAAA,EACJ;AACA,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,UAAU,4DAA4D;AAAA,EACpF;AACA,MAAI,CAAC,QAAQ;AACT,UAAM,IAAI,WAAW,EAAE,CAAC,GAAG,GAAG,UAAU,CAAC;AAAA,EAC7C,OACK;AACD,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,IAAO,wBAAQ,OAAO,KAAK,QAAQ;AAC/B,MAAI,eAAe,YAAY;AAC3B,WAAO;AAAA,EACX;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,YAAY,GAAG,GAAG;AAClB,QAAI,IAAI,SAAS,UAAU;AACvB,aAAO,IAAI,OAAO;AAAA,IACtB;AACA,QAAI,iBAAiB,OAAO,OAAO,IAAI,gBAAgB,YAAY;AAC/D,UAAI;AACA,eAAO,gBAAgB,KAAK,GAAG;AAAA,MACnC,SACO,KAAK;AACR,YAAI,eAAe,WAAW;AAC1B,gBAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM,IAAI,OAAO,EAAE,QAAQ,MAAM,CAAC;AACtC,WAAO,UAAU,KAAK,KAAK,GAAG;AAAA,EAClC;AACA,MAAI,MAAM,GAAG,GAAG;AACZ,QAAI,IAAI,GAAG;AACP,aAAO,OAAO,IAAI,CAAC;AAAA,IACvB;AACA,WAAO,UAAU,KAAK,KAAK,KAAK,IAAI;AAAA,EACxC;AACA,QAAM,IAAI,MAAM,aAAa;AACjC;;;AChKA,IAAM,MAAM,CAAC,QAAQ,2BAAM,OAAO;AAClC,IAAM,eAAe,CAAC,KAAK,KAAK,UAAU;AAJ1C,MAAAC,KAAAC;AAKI,MAAI,IAAI,QAAQ,QAAW;AACvB,QAAI;AACJ,YAAQ,OAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,mBAAW;AACX;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,mBAAW;AACX;AAAA,IACR;AACA,QAAI,IAAI,QAAQ,UAAU;AACtB,YAAM,IAAI,UAAU,sDAAsD,QAAQ,gBAAgB;AAAA,IACtG;AAAA,EACJ;AACA,MAAI,IAAI,QAAQ,UAAa,IAAI,QAAQ,KAAK;AAC1C,UAAM,IAAI,UAAU,sDAAsD,GAAG,gBAAgB;AAAA,EACjG;AACA,MAAI,MAAM,QAAQ,IAAI,OAAO,GAAG;AAC5B,QAAI;AACJ,YAAQ,MAAM;AAAA,MACV,MAAK,UAAU,UAAU,UAAU;AAAA,MACnC,KAAK,QAAQ;AAAA,MACb,KAAK,IAAI,SAAS,QAAQ;AACtB,wBAAgB;AAChB;AAAA,MACJ,KAAK,IAAI,WAAW,OAAO;AACvB,wBAAgB;AAChB;AAAA,MACJ,KAAK,0BAA0B,KAAK,GAAG;AACnC,YAAI,CAAC,IAAI,SAAS,KAAK,KAAK,IAAI,SAAS,IAAI,GAAG;AAC5C,0BAAgB,UAAU,YAAY,YAAY;AAAA,QACtD,OACK;AACD,0BAAgB;AAAA,QACpB;AACA;AAAA,MACJ,MAAK,UAAU,aAAa,IAAI,WAAW,KAAK;AAC5C,wBAAgB;AAChB;AAAA,MACJ,KAAK,UAAU;AACX,wBAAgB,IAAI,WAAW,KAAK,IAAI,cAAc;AACtD;AAAA,IACR;AACA,QAAI,mBAAiBA,OAAAD,MAAA,IAAI,YAAJ,gBAAAA,IAAa,aAAb,gBAAAC,IAAA,KAAAD,KAAwB,oBAAmB,OAAO;AACnE,YAAM,IAAI,UAAU,+DAA+D,aAAa,gBAAgB;AAAA,IACpH;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,qBAAqB,CAAC,KAAK,KAAK,UAAU;AAC5C,MAAI,eAAe;AACf;AACJ,MAAQ,MAAM,GAAG,GAAG;AAChB,QAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,UAAM,IAAI,UAAU,yHAAyH;AAAA,EACjJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,aAAa,aAAa,gBAAgB,YAAY,CAAC;AAAA,EACzG;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,8DAA8D;AAAA,EACjG;AACJ;AACA,IAAM,sBAAsB,CAAC,KAAK,KAAK,UAAU;AAC7C,MAAQ,MAAM,GAAG,GAAG;AAChB,YAAQ,OAAO;AAAA,MACX,KAAK;AAAA,MACL,KAAK;AACD,YAAQ,aAAa,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACrD;AACJ,cAAM,IAAI,UAAU,kDAAkD;AAAA,MAC1E,KAAK;AAAA,MACL,KAAK;AACD,YAAQ,YAAY,GAAG,KAAK,aAAa,KAAK,KAAK,KAAK;AACpD;AACJ,cAAM,IAAI,UAAU,iDAAiD;AAAA,IAC7E;AAAA,EACJ;AACA,MAAI,CAAC,oBAAU,GAAG,GAAG;AACjB,UAAM,IAAI,UAAU,QAAgB,KAAK,KAAK,aAAa,aAAa,cAAc,CAAC;AAAA,EAC3F;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,UAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,mEAAmE;AAAA,EACtG;AACA,MAAI,IAAI,SAAS,UAAU;AACvB,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,cAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,uEAAuE;AAAA,MAC1G,KAAK;AACD,cAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,0EAA0E;AAAA,MAC7G;AACI;AAAA,IACR;AAAA,EACJ;AACA,MAAI,IAAI,SAAS,WAAW;AACxB,YAAQ,OAAO;AAAA,MACX,KAAK;AACD,cAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,wEAAwE;AAAA,MAC3G,KAAK;AACD,cAAM,IAAI,UAAU,GAAG,IAAI,GAAG,CAAC,yEAAyE;AAAA,MAC5G;AACI;AAAA,IACR;AAAA,EACJ;AACJ;AACA,IAAO,yBAAQ,CAAC,KAAK,KAAK,UAAU;AAChC,QAAM,YAAY,IAAI,WAAW,IAAI,KACjC,QAAQ,SACR,IAAI,WAAW,OAAO,KACtB,oCAAoC,KAAK,GAAG,KAC5C,0CAA0C,KAAK,GAAG;AACtD,MAAI,WAAW;AACX,uBAAmB,KAAK,KAAK,KAAK;AAAA,EACtC,OACK;AACD,wBAAoB,KAAK,KAAK,KAAK;AAAA,EACvC;AACJ;;;ACjHA,eAAsB,iBAAiB,KAAK,KAAK,SAAS;AACtD,MAAI,CAAC,kBAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,iCAAiC;AAAA,EAC1D;AACA,MAAI,IAAI,cAAc,UAAa,IAAI,WAAW,UAAa,IAAI,gBAAgB,QAAW;AAC1F,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,MAAI,IAAI,OAAO,UAAa,OAAO,IAAI,OAAO,UAAU;AACpD,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,OAAO,IAAI,eAAe,UAAU;AACpC,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,IAAI,QAAQ,UAAa,OAAO,IAAI,QAAQ,UAAU;AACtD,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAChE;AACA,MAAI,IAAI,cAAc,UAAa,OAAO,IAAI,cAAc,UAAU;AAClE,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,MAAI,IAAI,kBAAkB,UAAa,OAAO,IAAI,kBAAkB,UAAU;AAC1E,UAAM,IAAI,WAAW,kCAAkC;AAAA,EAC3D;AACA,MAAI,IAAI,QAAQ,UAAa,OAAO,IAAI,QAAQ,UAAU;AACtD,UAAM,IAAI,WAAW,wBAAwB;AAAA,EACjD;AACA,MAAI,IAAI,WAAW,UAAa,CAAC,kBAAS,IAAI,MAAM,GAAG;AACnD,UAAM,IAAI,WAAW,8CAA8C;AAAA,EACvE;AACA,MAAI,IAAI,gBAAgB,UAAa,CAAC,kBAAS,IAAI,WAAW,GAAG;AAC7D,UAAM,IAAI,WAAW,qDAAqD;AAAA,EAC9E;AACA,MAAI;AACJ,MAAI,IAAI,WAAW;AACf,QAAI;AACA,YAAME,mBAAkB,OAAK,IAAI,SAAS;AAC1C,mBAAa,KAAK,MAAM,QAAQ,OAAOA,gBAAe,CAAC;AAAA,IAC3D,QACM;AACF,YAAM,IAAI,WAAW,iCAAiC;AAAA,IAC1D;AAAA,EACJ;AACA,MAAI,CAAC,oBAAW,YAAY,IAAI,QAAQ,IAAI,WAAW,GAAG;AACtD,UAAM,IAAI,WAAW,kHAAkH;AAAA,EAC3I;AACA,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACX;AACA,wBAAa,YAAY,oBAAI,IAAI,GAAG,mCAAS,MAAM,YAAY,UAAU;AACzE,MAAI,WAAW,QAAQ,QAAW;AAC9B,UAAM,IAAI,iBAAiB,sEAAsE;AAAA,EACrG;AACA,QAAM,EAAE,KAAK,IAAI,IAAI;AACrB,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACA,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,sDAAsD;AAAA,EAC/E;AACA,QAAM,0BAA0B,WAAW,4BAAmB,2BAA2B,QAAQ,uBAAuB;AACxH,QAAM,8BAA8B,WAChC,4BAAmB,+BAA+B,QAAQ,2BAA2B;AACzF,MAAK,2BAA2B,CAAC,wBAAwB,IAAI,GAAG,KAC3D,CAAC,2BAA2B,IAAI,WAAW,OAAO,GAAI;AACvD,UAAM,IAAI,kBAAkB,sDAAsD;AAAA,EACtF;AACA,MAAI,+BAA+B,CAAC,4BAA4B,IAAI,GAAG,GAAG;AACtE,UAAM,IAAI,kBAAkB,iEAAiE;AAAA,EACjG;AACA,MAAI;AACJ,MAAI,IAAI,kBAAkB,QAAW;AACjC,QAAI;AACA,qBAAe,OAAK,IAAI,aAAa;AAAA,IACzC,QACM;AACF,YAAM,IAAI,WAAW,8CAA8C;AAAA,IACvE;AAAA,EACJ;AACA,MAAI,cAAc;AAClB,MAAI,OAAO,QAAQ,YAAY;AAC3B,UAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,kBAAc;AAAA,EAClB;AACA,yBAAa,QAAQ,QAAQ,MAAM,KAAK,KAAK,SAAS;AACtD,QAAM,IAAI,MAAM,sBAAa,KAAK,GAAG;AACrC,MAAI;AACJ,MAAI;AACA,UAAM,MAAM,+BAAqB,KAAK,GAAG,cAAc,YAAY,OAAO;AAAA,EAC9E,SACO,KAAK;AACR,QAAI,eAAe,aAAa,eAAe,cAAc,eAAe,kBAAkB;AAC1F,YAAM;AAAA,IACV;AACA,UAAM,YAAY,GAAG;AAAA,EACzB;AACA,MAAI;AACJ,MAAIC;AACJ,MAAI,IAAI,OAAO,QAAW;AACtB,QAAI;AACA,WAAK,OAAK,IAAI,EAAE;AAAA,IACpB,QACM;AACF,YAAM,IAAI,WAAW,mCAAmC;AAAA,IAC5D;AAAA,EACJ;AACA,MAAI,IAAI,QAAQ,QAAW;AACvB,QAAI;AACA,MAAAA,OAAM,OAAK,IAAI,GAAG;AAAA,IACtB,QACM;AACF,YAAM,IAAI,WAAW,oCAAoC;AAAA,IAC7D;AAAA,EACJ;AACA,QAAM,kBAAkB,QAAQ,OAAO,IAAI,aAAa,EAAE;AAC1D,MAAI;AACJ,MAAI,IAAI,QAAQ,QAAW;AACvB,qBAAiB,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,QAAQ,OAAO,IAAI,GAAG,CAAC;AAAA,EACzF,OACK;AACD,qBAAiB;AAAA,EACrB;AACA,MAAI;AACJ,MAAI;AACA,iBAAa,OAAK,IAAI,UAAU;AAAA,EACpC,QACM;AACF,UAAM,IAAI,WAAW,2CAA2C;AAAA,EACpE;AACA,QAAM,YAAY,MAAM,gBAAQ,KAAK,KAAK,YAAY,IAAIA,MAAK,cAAc;AAC7E,QAAM,SAAS,EAAE,UAAU;AAC3B,MAAI,IAAI,cAAc,QAAW;AAC7B,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,IAAI,QAAQ,QAAW;AACvB,QAAI;AACA,aAAO,8BAA8B,OAAK,IAAI,GAAG;AAAA,IACrD,QACM;AACF,YAAM,IAAI,WAAW,oCAAoC;AAAA,IAC7D;AAAA,EACJ;AACA,MAAI,IAAI,gBAAgB,QAAW;AAC/B,WAAO,0BAA0B,IAAI;AAAA,EACzC;AACA,MAAI,IAAI,WAAW,QAAW;AAC1B,WAAO,oBAAoB,IAAI;AAAA,EACnC;AACA,MAAI,aAAa;AACb,WAAO,EAAE,GAAG,QAAQ,KAAK,EAAE;AAAA,EAC/B;AACA,SAAO;AACX;;;ACjKA,eAAsB,eAAe,KAAK,KAAK,SAAS;AACpD,MAAI,eAAe,YAAY;AAC3B,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,OAAO,QAAQ,UAAU;AACzB,UAAM,IAAI,WAAW,4CAA4C;AAAA,EACrE;AACA,QAAM,EAAE,GAAG,iBAAiB,GAAG,cAAc,GAAG,IAAI,GAAG,YAAY,GAAGC,MAAK,OAAQ,IAAI,IAAI,MAAM,GAAG;AACpG,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,QAAM,YAAY,MAAM,iBAAiB;AAAA,IACrC;AAAA,IACA,IAAI,MAAM;AAAA,IACV,WAAW;AAAA,IACX,KAAKA,QAAO;AAAA,IACZ,eAAe,gBAAgB;AAAA,EACnC,GAAG,KAAK,OAAO;AACf,QAAM,SAAS,EAAE,WAAW,UAAU,WAAW,iBAAiB,UAAU,gBAAgB;AAC5F,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,UAAU,IAAI;AAAA,EAC3C;AACA,SAAO;AACX;;;ACvBA,eAAsB,eAAe,KAAK,KAAK,SAAS;AACpD,MAAI,CAAC,kBAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,CAAC,IAAI,WAAW,MAAM,iBAAQ,GAAG;AACnE,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,MAAI,CAAC,IAAI,WAAW,QAAQ;AACxB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,aAAW,aAAa,IAAI,YAAY;AACpC,QAAI;AACA,aAAO,MAAM,iBAAiB;AAAA,QAC1B,KAAK,IAAI;AAAA,QACT,YAAY,IAAI;AAAA,QAChB,eAAe,UAAU;AAAA,QACzB,QAAQ,UAAU;AAAA,QAClB,IAAI,IAAI;AAAA,QACR,WAAW,IAAI;AAAA,QACf,KAAK,IAAI;AAAA,QACT,aAAa,IAAI;AAAA,MACrB,GAAG,KAAK,OAAO;AAAA,IACnB,QACM;AAAA,IACN;AAAA,EACJ;AACA,QAAM,IAAI,oBAAoB;AAClC;;;AC9BO,IAAM,cAAc,OAAO;;;ACGlC,eAAO,SAAgC,KAAK;AACxC,MAAI,YAAY,GAAG,GAAG;AAClB,QAAI,IAAI,SAAS,UAAU;AACvB,YAAM,IAAI,OAAO;AAAA,IACrB,OACK;AACD,aAAO,IAAI,OAAO,EAAE,QAAQ,MAAM,CAAC;AAAA,IACvC;AAAA,EACJ;AACA,MAAI,eAAe,YAAY;AAC3B,WAAO;AAAA,MACH,KAAK;AAAA,MACL,GAAG,OAAK,GAAG;AAAA,IACf;AAAA,EACJ;AACA,MAAI,CAAC,YAAY,GAAG,GAAG;AACnB,UAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,aAAa,YAAY,CAAC;AAAA,EACpF;AACA,MAAI,CAAC,IAAI,aAAa;AAClB,UAAM,IAAI,UAAU,uDAAuD;AAAA,EAC/E;AACA,QAAM,EAAE,KAAK,SAAS,KAAK,KAAK,GAAG,IAAI,IAAI,MAAM,OAAO,OAAO,UAAU,OAAO,GAAG;AACnF,SAAO;AACX;;;ACxBA,eAAsB,WAAW,KAAK;AAClC,SAAO,OAAa,GAAG;AAC3B;AACA,eAAsB,YAAY,KAAK;AACnC,SAAO,QAAc,GAAG;AAC5B;AACA,eAAsB,UAAU,KAAK;AACjC,SAAO,SAAS,GAAG;AACvB;;;ACCA,IAAO,iCAAQ,OAAO,KAAK,KAAK,KAAK,aAAa,qBAAqB,CAAC,MAAM;AAC1E,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK,OAAO;AACR,YAAM;AACN;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,sBAAgB,GAAG;AACnB,UAAI,CAAQ,QAAQ,GAAG,GAAG;AACtB,cAAM,IAAI,iBAAiB,uFAAuF;AAAA,MACtH;AACA,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,UAAI;AACJ,UAAI,mBAAmB,KAAK;AACxB,uBAAgB,MAAM,sBAAa,mBAAmB,KAAK,GAAG;AAAA,MAClE,OACK;AACD,wBAAgB,MAAM,OAAO,OAAO,YAAY,IAAI,WAAW,MAAM,CAAC,YAAY,CAAC,GAAG;AAAA,MAC1F;AACA,YAAM,EAAE,GAAG,GAAG,KAAK,IAAI,IAAI,MAAM,UAAU,YAAY;AACvD,YAAM,eAAe,MAAa,UAAU,KAAK,cAAc,QAAQ,YAAY,MAAM,KAAK,QAAQ,YAAYC,WAAU,GAAG,IAAI,SAAS,IAAI,MAAM,IAAI,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG;AAC5K,mBAAa,EAAE,KAAK,EAAE,GAAG,KAAK,IAAI,EAAE;AACpC,UAAI,QAAQ;AACR,mBAAW,IAAI,IAAI;AACvB,UAAI;AACA,mBAAW,MAAM,OAAK,GAAG;AAC7B,UAAI;AACA,mBAAW,MAAM,OAAK,GAAG;AAC7B,UAAI,QAAQ,WAAW;AACnB,cAAM;AACN;AAAA,MACJ;AACA,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,QAAQ,IAAI,MAAM,EAAE;AAC1B,qBAAe,MAAY,KAAK,OAAO,cAAc,GAAG;AACxD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,gBAAgB;AACjB,YAAM,eAAe,YAAY,GAAG;AACpC,sBAAgB,GAAG;AACnB,qBAAe,MAAY,QAAQ,KAAK,KAAK,GAAG;AAChD;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,sBAAsB;AACvB,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,EAAE,KAAK,IAAI,IAAI;AACrB,OAAC,EAAE,cAAc,GAAG,WAAW,IAAI,MAAcC,MAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AAC7E;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,UAAU;AACX,YAAM,eAAe,YAAY,GAAG;AACpC,qBAAe,MAAY,KAAK,KAAK,KAAK,GAAG;AAC7C;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,aAAa;AACd,YAAM,eAAe,YAAY,GAAG;AACpC,YAAM,EAAE,GAAG,IAAI;AACf,OAAC,EAAE,cAAc,GAAG,WAAW,IAAI,MAAMA,MAAS,KAAK,KAAK,KAAK,EAAE;AACnE;AAAA,IACJ;AAAA,IACA,SAAS;AACL,YAAM,IAAI,iBAAiB,2DAA2D;AAAA,IAC1F;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,cAAc,WAAW;AAC3C;;;AC3FA;AAUO,IAAM,mBAAN,MAAuB;AAAA,EAS1B,YAAY,WAAW;AARvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEI,QAAI,EAAE,qBAAqB,aAAa;AACpC,YAAM,IAAI,UAAU,6CAA6C;AAAA,IACrE;AACA,uBAAK,YAAa;AAAA,EACtB;AAAA,EACA,2BAA2B,YAAY;AACnC,QAAI,mBAAK,2BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,uBAAK,0BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,mBAAK,mBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,uBAAK,kBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,yBAAyB;AAChD,QAAI,mBAAK,2BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,uBAAK,0BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,mBAAK,qBAAoB;AACzB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,uBAAK,oBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,+BAA+B,KAAK;AAChC,uBAAK,MAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,KAAK;AACzB,QAAI,mBAAK,OAAM;AACX,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,uBAAK,MAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,QAAI,mBAAK,MAAK;AACV,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,uBAAK,KAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,QAAI,CAAC,mBAAK,qBAAoB,CAAC,mBAAK,uBAAsB,CAAC,mBAAK,2BAA0B;AACtF,YAAM,IAAI,WAAW,8GAA8G;AAAA,IACvI;AACA,QAAI,CAAC,oBAAW,mBAAK,mBAAkB,mBAAK,qBAAoB,mBAAK,yBAAwB,GAAG;AAC5F,YAAM,IAAI,WAAW,qGAAqG;AAAA,IAC9H;AACA,UAAM,aAAa;AAAA,MACf,GAAG,mBAAK;AAAA,MACR,GAAG,mBAAK;AAAA,MACR,GAAG,mBAAK;AAAA,IACZ;AACA,0BAAa,YAAY,oBAAI,IAAI,GAAG,mCAAS,MAAM,mBAAK,mBAAkB,UAAU;AACpF,QAAI,WAAW,QAAQ,QAAW;AAC9B,YAAM,IAAI,iBAAiB,sEAAsE;AAAA,IACrG;AACA,UAAM,EAAE,KAAK,IAAI,IAAI;AACrB,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,2DAA2D;AAAA,IACpF;AACA,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,sEAAsE;AAAA,IAC/F;AACA,QAAI;AACJ,QAAI,mBAAK,UAAS,QAAQ,SAAS,QAAQ,YAAY;AACnD,YAAM,IAAI,UAAU,8EAA8E,GAAG,EAAE;AAAA,IAC3G;AACA,2BAAa,QAAQ,QAAQ,MAAM,KAAK,KAAK,SAAS;AACtD,QAAI;AACJ;AACI,UAAI;AACJ,YAAM,IAAI,MAAM,sBAAa,KAAK,GAAG;AACrC,OAAC,EAAE,KAAK,cAAc,WAAW,IAAI,MAAM,+BAAqB,KAAK,KAAK,GAAG,mBAAK,OAAM,mBAAK,yBAAwB;AACrH,UAAI,YAAY;AACZ,YAAI,WAAW,eAAe,SAAS;AACnC,cAAI,CAAC,mBAAK,qBAAoB;AAC1B,iBAAK,qBAAqB,UAAU;AAAA,UACxC,OACK;AACD,+BAAK,oBAAqB,EAAE,GAAG,mBAAK,qBAAoB,GAAG,WAAW;AAAA,UAC1E;AAAA,QACJ,WACS,CAAC,mBAAK,mBAAkB;AAC7B,eAAK,mBAAmB,UAAU;AAAA,QACtC,OACK;AACD,6BAAK,kBAAmB,EAAE,GAAG,mBAAK,mBAAkB,GAAG,WAAW;AAAA,QACtE;AAAA,MACJ;AAAA,IACJ;AACA,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,mBAAK,mBAAkB;AACvB,wBAAkB,QAAQ,OAAO,OAAK,KAAK,UAAU,mBAAK,iBAAgB,CAAC,CAAC;AAAA,IAChF,OACK;AACD,wBAAkB,QAAQ,OAAO,EAAE;AAAA,IACvC;AACA,QAAI,mBAAK,OAAM;AACX,kBAAY,OAAK,mBAAK,KAAI;AAC1B,uBAAiB,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,QAAQ,OAAO,SAAS,CAAC;AAAA,IAC3F,OACK;AACD,uBAAiB;AAAA,IACrB;AACA,UAAM,EAAE,YAAY,KAAAC,MAAK,GAAG,IAAI,MAAM,gBAAQ,KAAK,mBAAK,aAAY,KAAK,mBAAK,MAAK,cAAc;AACjG,UAAM,MAAM;AAAA,MACR,YAAY,OAAK,UAAU;AAAA,IAC/B;AACA,QAAI,IAAI;AACJ,UAAI,KAAK,OAAK,EAAE;AAAA,IACpB;AACA,QAAIA,MAAK;AACL,UAAI,MAAM,OAAKA,IAAG;AAAA,IACtB;AACA,QAAI,cAAc;AACd,UAAI,gBAAgB,OAAK,YAAY;AAAA,IACzC;AACA,QAAI,WAAW;AACX,UAAI,MAAM;AAAA,IACd;AACA,QAAI,mBAAK,mBAAkB;AACvB,UAAI,YAAY,QAAQ,OAAO,eAAe;AAAA,IAClD;AACA,QAAI,mBAAK,2BAA0B;AAC/B,UAAI,cAAc,mBAAK;AAAA,IAC3B;AACA,QAAI,mBAAK,qBAAoB;AACzB,UAAI,SAAS,mBAAK;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AACJ;AAzJI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AClBJ;AAUA,IAAM,sBAAN,MAA0B;AAAA,EAKtB,YAAY,KAAK,KAAK,SAAS;AAJ/B;AACA;AACA;AACA;AAEI,uBAAK,SAAU;AACf,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,mBAAmB;AACxB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,WAAO,mBAAK,SAAQ,aAAa,GAAG,IAAI;AAAA,EAC5C;AAAA,EACA,WAAW,MAAM;AACb,WAAO,mBAAK,SAAQ,QAAQ,GAAG,IAAI;AAAA,EACvC;AAAA,EACA,OAAO;AACH,WAAO,mBAAK;AAAA,EAChB;AACJ;AAzBI;AAXJ,IAAAC,aAAA,aAAAC,mBAAAC,qBAAAC;AAqCO,IAAM,iBAAN,MAAqB;AAAA,EAMxB,YAAY,WAAW;AALvB,uBAAAH,aAAA;AACA,oCAAc,CAAC;AACf,uBAAAC,mBAAA;AACA,uBAAAC,qBAAA;AACA,uBAAAC,OAAA;AAEI,uBAAKH,aAAa;AAAA,EACtB;AAAA,EACA,aAAa,KAAK,SAAS;AACvB,UAAM,YAAY,IAAI,oBAAoB,MAAM,KAAK,EAAE,MAAM,mCAAS,KAAK,CAAC;AAC5E,uBAAK,aAAY,KAAK,SAAS;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,mBAAKC,oBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,uBAAKA,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,yBAAyB;AAChD,QAAI,mBAAKC,sBAAoB;AACzB,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,uBAAKA,qBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,+BAA+B,KAAK;AAChC,uBAAKC,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,MAAM,UAAU;AArEpB,QAAAC,KAAAC,KAAA;AAsEQ,QAAI,CAAC,mBAAK,aAAY,QAAQ;AAC1B,YAAM,IAAI,WAAW,sCAAsC;AAAA,IAC/D;AACA,QAAI,mBAAK,aAAY,WAAW,GAAG;AAC/B,YAAM,CAAC,SAAS,IAAI,mBAAK;AACzB,YAAM,YAAY,MAAM,IAAI,iBAAiB,mBAAKL,YAAU,EACvD,+BAA+B,mBAAKG,MAAI,EACxC,mBAAmB,mBAAKF,kBAAgB,EACxC,2BAA2B,mBAAKC,oBAAkB,EAClD,qBAAqB,UAAU,iBAAiB,EAChD,QAAQ,UAAU,KAAK,EAAE,GAAG,UAAU,QAAQ,CAAC;AACpD,YAAMI,OAAM;AAAA,QACR,YAAY,UAAU;AAAA,QACtB,IAAI,UAAU;AAAA,QACd,YAAY,CAAC,CAAC,CAAC;AAAA,QACf,KAAK,UAAU;AAAA,MACnB;AACA,UAAI,UAAU;AACV,QAAAA,KAAI,MAAM,UAAU;AACxB,UAAI,UAAU;AACV,QAAAA,KAAI,YAAY,UAAU;AAC9B,UAAI,UAAU;AACV,QAAAA,KAAI,cAAc,UAAU;AAChC,UAAI,UAAU;AACV,QAAAA,KAAI,WAAW,CAAC,EAAE,gBAAgB,UAAU;AAChD,UAAI,UAAU;AACV,QAAAA,KAAI,WAAW,CAAC,EAAE,SAAS,UAAU;AACzC,aAAOA;AAAA,IACX;AACA,QAAI;AACJ,aAAS,IAAI,GAAG,IAAI,mBAAK,aAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,mBAAK,aAAY,CAAC;AACpC,UAAI,CAAC,oBAAW,mBAAKL,oBAAkB,mBAAKC,sBAAoB,UAAU,iBAAiB,GAAG;AAC1F,cAAM,IAAI,WAAW,qGAAqG;AAAA,MAC9H;AACA,YAAM,aAAa;AAAA,QACf,GAAG,mBAAKD;AAAA,QACR,GAAG,mBAAKC;AAAA,QACR,GAAG,UAAU;AAAA,MACjB;AACA,YAAM,EAAE,IAAI,IAAI;AAChB,UAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,cAAM,IAAI,WAAW,2DAA2D;AAAA,MACpF;AACA,UAAI,QAAQ,SAAS,QAAQ,WAAW;AACpC,cAAM,IAAI,WAAW,kEAAkE;AAAA,MAC3F;AACA,UAAI,OAAO,WAAW,QAAQ,YAAY,CAAC,WAAW,KAAK;AACvD,cAAM,IAAI,WAAW,sEAAsE;AAAA,MAC/F;AACA,UAAI,CAAC,KAAK;AACN,cAAM,WAAW;AAAA,MACrB,WACS,QAAQ,WAAW,KAAK;AAC7B,cAAM,IAAI,WAAW,uFAAuF;AAAA,MAChH;AACA,4BAAa,YAAY,oBAAI,IAAI,GAAG,UAAU,QAAQ,MAAM,mBAAKD,oBAAkB,UAAU;AAC7F,UAAI,WAAW,QAAQ,QAAW;AAC9B,cAAM,IAAI,iBAAiB,sEAAsE;AAAA,MACrG;AAAA,IACJ;AACA,UAAM,MAAM,YAAY,GAAG;AAC3B,UAAM,MAAM;AAAA,MACR,YAAY;AAAA,MACZ,IAAI;AAAA,MACJ,YAAY,CAAC;AAAA,MACb,KAAK;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,mBAAK,aAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,mBAAK,aAAY,CAAC;AACpC,YAAM,SAAS,CAAC;AAChB,UAAI,WAAW,KAAK,MAAM;AAC1B,YAAM,aAAa;AAAA,QACf,GAAG,mBAAKA;AAAA,QACR,GAAG,mBAAKC;AAAA,QACR,GAAG,UAAU;AAAA,MACjB;AACA,YAAM,MAAM,WAAW,IAAI,WAAW,OAAO,IAAI,OAAO,IAAI;AAC5D,UAAI,MAAM,GAAG;AACT,cAAM,YAAY,MAAM,IAAI,iBAAiB,mBAAKF,YAAU,EACvD,+BAA+B,mBAAKG,MAAI,EACxC,wBAAwB,GAAG,EAC3B,mBAAmB,mBAAKF,kBAAgB,EACxC,2BAA2B,mBAAKC,oBAAkB,EAClD,qBAAqB,UAAU,iBAAiB,EAChD,2BAA2B,EAAE,IAAI,CAAC,EAClC,QAAQ,UAAU,KAAK;AAAA,UACxB,GAAG,UAAU;AAAA,UACb,CAAC,WAAW,GAAG;AAAA,QACnB,CAAC;AACD,YAAI,aAAa,UAAU;AAC3B,YAAI,KAAK,UAAU;AACnB,YAAI,MAAM,UAAU;AACpB,YAAI,UAAU;AACV,cAAI,MAAM,UAAU;AACxB,YAAI,UAAU;AACV,cAAI,YAAY,UAAU;AAC9B,YAAI,UAAU;AACV,cAAI,cAAc,UAAU;AAChC,eAAO,gBAAgB,UAAU;AACjC,YAAI,UAAU;AACV,iBAAO,SAAS,UAAU;AAC9B;AAAA,MACJ;AACA,YAAM,QAAME,MAAA,UAAU,sBAAV,gBAAAA,IAA6B,UACrCC,MAAA,mBAAKJ,uBAAL,gBAAAI,IAAuB,UACvB,wBAAKH,yBAAL,mBAAyB;AAC7B,6BAAa,QAAQ,QAAQ,MAAM,KAAK,UAAU,KAAK,SAAS;AAChE,YAAM,IAAI,MAAM,sBAAa,UAAU,KAAK,GAAG;AAC/C,YAAM,EAAE,cAAc,WAAW,IAAI,MAAM,+BAAqB,KAAK,KAAK,GAAG,KAAK,EAAE,IAAI,CAAC;AACzF,aAAO,gBAAgB,OAAK,YAAY;AACxC,UAAI,UAAU,qBAAqB;AAC/B,eAAO,SAAS,EAAE,GAAG,UAAU,mBAAmB,GAAG,WAAW;AAAA,IACxE;AACA,WAAO;AAAA,EACX;AACJ;AApJIF,cAAA;AACA;AACAC,oBAAA;AACAC,sBAAA;AACAC,QAAA;;;ACzCJ,IAAO,qBAAQ,CAAC,KAAK,cAAc;AAC/B,QAAM,OAAO,OAAO,IAAI,MAAM,EAAE,CAAC;AACjC,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,EAAE,MAAM,MAAM,OAAO;AAAA,IAChC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,EAAE,MAAM,MAAM,WAAW,YAAY,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;AAAA,IACjF,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,EAAE,MAAM,MAAM,oBAAoB;AAAA,IAC7C,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,aAAO,EAAE,MAAM,MAAM,SAAS,YAAY,UAAU,WAAW;AAAA,IACnE,KAAK;AAAA,IACL,KAAK;AACD,aAAO,EAAE,MAAM,UAAU;AAAA,IAC7B;AACI,YAAM,IAAI,iBAAiB,OAAO,GAAG,6DAA6D;AAAA,EAC1G;AACJ;;;ACxBA,IAAO,8BAAQ,OAAO,KAAK,KAAK,UAAU;AACtC,MAAI,eAAe,YAAY;AAC3B,QAAI,CAAC,IAAI,WAAW,IAAI,GAAG;AACvB,YAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,aAAa,cAAc,CAAC;AAAA,IACtF;AACA,WAAO,OAAO,OAAO,UAAU,OAAO,KAAK,EAAE,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAAA,EAC7G;AACA,oBAAkB,KAAK,KAAK,KAAK;AACjC,SAAO;AACX;;;ACRA,IAAO,iBAAQ,OAAO,KAAK,KAAK,WAAW,SAAS;AAChD,QAAM,YAAY,MAAM,4BAAa,KAAK,KAAK,QAAQ;AACvD,2BAAe,KAAK,SAAS;AAC7B,QAAM,YAAY,mBAAgB,KAAK,UAAU,SAAS;AAC1D,MAAI;AACA,WAAO,MAAM,OAAO,OAAO,OAAO,WAAW,WAAW,WAAW,IAAI;AAAA,EAC3E,QACM;AACF,WAAO;AAAA,EACX;AACJ;;;ACHA,eAAsB,gBAAgB,KAAK,KAAK,SAAS;AACrD,MAAI,CAAC,kBAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,iCAAiC;AAAA,EAC1D;AACA,MAAI,IAAI,cAAc,UAAa,IAAI,WAAW,QAAW;AACzD,UAAM,IAAI,WAAW,uEAAuE;AAAA,EAChG;AACA,MAAI,IAAI,cAAc,UAAa,OAAO,IAAI,cAAc,UAAU;AAClE,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,MAAI,IAAI,YAAY,QAAW;AAC3B,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,MAAI,OAAO,IAAI,cAAc,UAAU;AACnC,UAAM,IAAI,WAAW,yCAAyC;AAAA,EAClE;AACA,MAAI,IAAI,WAAW,UAAa,CAAC,kBAAS,IAAI,MAAM,GAAG;AACnD,UAAM,IAAI,WAAW,uCAAuC;AAAA,EAChE;AACA,MAAI,aAAa,CAAC;AAClB,MAAI,IAAI,WAAW;AACf,QAAI;AACA,YAAM,kBAAkB,OAAK,IAAI,SAAS;AAC1C,mBAAa,KAAK,MAAM,QAAQ,OAAO,eAAe,CAAC;AAAA,IAC3D,QACM;AACF,YAAM,IAAI,WAAW,iCAAiC;AAAA,IAC1D;AAAA,EACJ;AACA,MAAI,CAAC,oBAAW,YAAY,IAAI,MAAM,GAAG;AACrC,UAAM,IAAI,WAAW,2EAA2E;AAAA,EACpG;AACA,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,IAAI;AAAA,EACX;AACA,QAAM,aAAa,sBAAa,YAAY,oBAAI,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,mCAAS,MAAM,YAAY,UAAU;AAC3G,MAAI,MAAM;AACV,MAAI,WAAW,IAAI,KAAK,GAAG;AACvB,UAAM,WAAW;AACjB,QAAI,OAAO,QAAQ,WAAW;AAC1B,YAAM,IAAI,WAAW,yEAAyE;AAAA,IAClG;AAAA,EACJ;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,UAAM,IAAI,WAAW,2DAA2D;AAAA,EACpF;AACA,QAAM,aAAa,WAAW,4BAAmB,cAAc,QAAQ,UAAU;AACjF,MAAI,cAAc,CAAC,WAAW,IAAI,GAAG,GAAG;AACpC,UAAM,IAAI,kBAAkB,sDAAsD;AAAA,EACtF;AACA,MAAI,KAAK;AACL,QAAI,OAAO,IAAI,YAAY,UAAU;AACjC,YAAM,IAAI,WAAW,8BAA8B;AAAA,IACvD;AAAA,EACJ,WACS,OAAO,IAAI,YAAY,YAAY,EAAE,IAAI,mBAAmB,aAAa;AAC9E,UAAM,IAAI,WAAW,wDAAwD;AAAA,EACjF;AACA,MAAI,cAAc;AAClB,MAAI,OAAO,QAAQ,YAAY;AAC3B,UAAM,MAAM,IAAI,YAAY,GAAG;AAC/B,kBAAc;AAAA,EAClB;AACA,yBAAa,KAAK,KAAK,QAAQ;AAC/B,QAAM,OAAO,OAAO,QAAQ,OAAO,IAAI,aAAa,EAAE,GAAG,QAAQ,OAAO,GAAG,GAAG,OAAO,IAAI,YAAY,WAAW,QAAQ,OAAO,IAAI,OAAO,IAAI,IAAI,OAAO;AACzJ,MAAI;AACJ,MAAI;AACA,gBAAY,OAAK,IAAI,SAAS;AAAA,EAClC,QACM;AACF,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,QAAM,IAAI,MAAM,sBAAa,KAAK,GAAG;AACrC,QAAM,WAAW,MAAM,eAAO,KAAK,GAAG,WAAW,IAAI;AACrD,MAAI,CAAC,UAAU;AACX,UAAM,IAAI,+BAA+B;AAAA,EAC7C;AACA,MAAI;AACJ,MAAI,KAAK;AACL,QAAI;AACA,gBAAU,OAAK,IAAI,OAAO;AAAA,IAC9B,QACM;AACF,YAAM,IAAI,WAAW,wCAAwC;AAAA,IACjE;AAAA,EACJ,WACS,OAAO,IAAI,YAAY,UAAU;AACtC,cAAU,QAAQ,OAAO,IAAI,OAAO;AAAA,EACxC,OACK;AACD,cAAU,IAAI;AAAA,EAClB;AACA,QAAM,SAAS,EAAE,QAAQ;AACzB,MAAI,IAAI,cAAc,QAAW;AAC7B,WAAO,kBAAkB;AAAA,EAC7B;AACA,MAAI,IAAI,WAAW,QAAW;AAC1B,WAAO,oBAAoB,IAAI;AAAA,EACnC;AACA,MAAI,aAAa;AACb,WAAO,EAAE,GAAG,QAAQ,KAAK,EAAE;AAAA,EAC/B;AACA,SAAO;AACX;;;AChHA,eAAsB,cAAc,KAAK,KAAK,SAAS;AACnD,MAAI,eAAe,YAAY;AAC3B,UAAM,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,OAAO,QAAQ,UAAU;AACzB,UAAM,IAAI,WAAW,4CAA4C;AAAA,EACrE;AACA,QAAM,EAAE,GAAG,iBAAiB,GAAG,SAAS,GAAG,WAAW,OAAO,IAAI,IAAI,MAAM,GAAG;AAC9E,MAAI,WAAW,GAAG;AACd,UAAM,IAAI,WAAW,qBAAqB;AAAA,EAC9C;AACA,QAAM,WAAW,MAAM,gBAAgB,EAAE,SAAS,WAAW,iBAAiB,UAAU,GAAG,KAAK,OAAO;AACvG,QAAM,SAAS,EAAE,SAAS,SAAS,SAAS,iBAAiB,SAAS,gBAAgB;AACtF,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,SAAS,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;;;ACjBA,eAAsB,cAAc,KAAK,KAAK,SAAS;AACnD,MAAI,CAAC,kBAAS,GAAG,GAAG;AAChB,UAAM,IAAI,WAAW,+BAA+B;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,QAAQ,IAAI,UAAU,KAAK,CAAC,IAAI,WAAW,MAAM,iBAAQ,GAAG;AACnE,UAAM,IAAI,WAAW,0CAA0C;AAAA,EACnE;AACA,aAAW,aAAa,IAAI,YAAY;AACpC,QAAI;AACA,aAAO,MAAM,gBAAgB;AAAA,QACzB,QAAQ,UAAU;AAAA,QAClB,SAAS,IAAI;AAAA,QACb,WAAW,UAAU;AAAA,QACrB,WAAW,UAAU;AAAA,MACzB,GAAG,KAAK,OAAO;AAAA,IACnB,QACM;AAAA,IACN;AAAA,EACJ;AACA,QAAM,IAAI,+BAA+B;AAC7C;;;ACvBA,IAAO,gBAAQ,CAAC,SAAS,KAAK,MAAM,KAAK,QAAQ,IAAI,GAAI;;;ACAzD,IAAM,SAAS;AACf,IAAM,OAAO,SAAS;AACtB,IAAM,MAAM,OAAO;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,OAAO,MAAM;AACnB,IAAM,QAAQ;AACd,IAAO,eAAQ,CAAC,QAAQ;AACpB,QAAM,UAAU,MAAM,KAAK,GAAG;AAC9B,MAAI,CAAC,WAAY,QAAQ,CAAC,KAAK,QAAQ,CAAC,GAAI;AACxC,UAAM,IAAI,UAAU,4BAA4B;AAAA,EACpD;AACA,QAAM,QAAQ,WAAW,QAAQ,CAAC,CAAC;AACnC,QAAM,OAAO,QAAQ,CAAC,EAAE,YAAY;AACpC,MAAI;AACJ,UAAQ,MAAM;AAAA,IACV,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,KAAK;AAC9B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,MAAM;AACvC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,GAAG;AACpC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,IACJ;AACI,oBAAc,KAAK,MAAM,QAAQ,IAAI;AACrC;AAAA,EACR;AACA,MAAI,QAAQ,CAAC,MAAM,OAAO,QAAQ,CAAC,MAAM,OAAO;AAC5C,WAAO,CAAC;AAAA,EACZ;AACA,SAAO;AACX;;;AChDA,SAAS,cAAc,OAAO,OAAO;AACjC,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AACzB,UAAM,IAAI,UAAU,WAAW,KAAK,QAAQ;AAAA,EAChD;AACA,SAAO;AACX;AACA,IAAM,eAAe,CAAC,UAAU;AAC5B,MAAI,MAAM,SAAS,GAAG,GAAG;AACrB,WAAO,MAAM,YAAY;AAAA,EAC7B;AACA,SAAO,eAAe,MAAM,YAAY,CAAC;AAC7C;AACA,IAAM,wBAAwB,CAAC,YAAY,cAAc;AACrD,MAAI,OAAO,eAAe,UAAU;AAChC,WAAO,UAAU,SAAS,UAAU;AAAA,EACxC;AACA,MAAI,MAAM,QAAQ,UAAU,GAAG;AAC3B,WAAO,UAAU,KAAK,IAAI,UAAU,IAAI,KAAK,IAAI,IAAI,UAAU,CAAC,CAAC;AAAA,EACrE;AACA,SAAO;AACX;AACO,SAAS,kBAAkB,iBAAiB,gBAAgB,UAAU,CAAC,GAAG;AAC7E,MAAI;AACJ,MAAI;AACA,cAAU,KAAK,MAAM,QAAQ,OAAO,cAAc,CAAC;AAAA,EACvD,QACM;AAAA,EACN;AACA,MAAI,CAAC,kBAAS,OAAO,GAAG;AACpB,UAAM,IAAI,WAAW,gDAAgD;AAAA,EACzE;AACA,QAAM,EAAE,IAAI,IAAI;AAChB,MAAI,QACC,OAAO,gBAAgB,QAAQ,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,GAAG,IAAI;AAC9D,UAAM,IAAI,yBAAyB,qCAAqC,SAAS,OAAO,cAAc;AAAA,EAC1G;AACA,QAAM,EAAE,iBAAiB,CAAC,GAAG,QAAQ,SAAS,UAAU,YAAY,IAAI;AACxE,QAAM,gBAAgB,CAAC,GAAG,cAAc;AACxC,MAAI,gBAAgB;AAChB,kBAAc,KAAK,KAAK;AAC5B,MAAI,aAAa;AACb,kBAAc,KAAK,KAAK;AAC5B,MAAI,YAAY;AACZ,kBAAc,KAAK,KAAK;AAC5B,MAAI,WAAW;AACX,kBAAc,KAAK,KAAK;AAC5B,aAAW,SAAS,IAAI,IAAI,cAAc,QAAQ,CAAC,GAAG;AAClD,QAAI,EAAE,SAAS,UAAU;AACrB,YAAM,IAAI,yBAAyB,qBAAqB,KAAK,WAAW,SAAS,OAAO,SAAS;AAAA,IACrG;AAAA,EACJ;AACA,MAAI,UACA,EAAE,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,QAAQ,GAAG,GAAG;AACpE,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI,WAAW,QAAQ,QAAQ,SAAS;AACpC,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI,YACA,CAAC,sBAAsB,QAAQ,KAAK,OAAO,aAAa,WAAW,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC3F,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,cAAc;AAAA,EACrG;AACA,MAAI;AACJ,UAAQ,OAAO,QAAQ,gBAAgB;AAAA,IACnC,KAAK;AACD,kBAAY,aAAK,QAAQ,cAAc;AACvC;AAAA,IACJ,KAAK;AACD,kBAAY,QAAQ;AACpB;AAAA,IACJ,KAAK;AACD,kBAAY;AACZ;AAAA,IACJ;AACI,YAAM,IAAI,UAAU,oCAAoC;AAAA,EAChE;AACA,QAAM,EAAE,YAAY,IAAI;AACxB,QAAM,MAAM,cAAM,eAAe,oBAAI,KAAK,CAAC;AAC3C,OAAK,QAAQ,QAAQ,UAAa,gBAAgB,OAAO,QAAQ,QAAQ,UAAU;AAC/E,UAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,EAChG;AACA,MAAI,QAAQ,QAAQ,QAAW;AAC3B,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,YAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,IAChG;AACA,QAAI,QAAQ,MAAM,MAAM,WAAW;AAC/B,YAAM,IAAI,yBAAyB,sCAAsC,SAAS,OAAO,cAAc;AAAA,IAC3G;AAAA,EACJ;AACA,MAAI,QAAQ,QAAQ,QAAW;AAC3B,QAAI,OAAO,QAAQ,QAAQ,UAAU;AACjC,YAAM,IAAI,yBAAyB,gCAAgC,SAAS,OAAO,SAAS;AAAA,IAChG;AACA,QAAI,QAAQ,OAAO,MAAM,WAAW;AAChC,YAAM,IAAI,WAAW,sCAAsC,SAAS,OAAO,cAAc;AAAA,IAC7F;AAAA,EACJ;AACA,MAAI,aAAa;AACb,UAAM,MAAM,MAAM,QAAQ;AAC1B,UAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,aAAK,WAAW;AAC5E,QAAI,MAAM,YAAY,KAAK;AACvB,YAAM,IAAI,WAAW,4DAA4D,SAAS,OAAO,cAAc;AAAA,IACnH;AACA,QAAI,MAAM,IAAI,WAAW;AACrB,YAAM,IAAI,yBAAyB,iEAAiE,SAAS,OAAO,cAAc;AAAA,IACtI;AAAA,EACJ;AACA,SAAO;AACX;AAnHA;AAoHO,IAAM,mBAAN,MAAuB;AAAA,EAE1B,YAAY,SAAS;AADrB;AAEI,QAAI,CAAC,kBAAS,OAAO,GAAG;AACpB,YAAM,IAAI,UAAU,kCAAkC;AAAA,IAC1D;AACA,uBAAK,UAAW,gBAAgB,OAAO;AAAA,EAC3C;AAAA,EACA,OAAO;AACH,WAAO,QAAQ,OAAO,KAAK,UAAU,mBAAK,SAAQ,CAAC;AAAA,EACvD;AAAA,EACA,IAAI,MAAM;AACN,WAAO,mBAAK,UAAS;AAAA,EACzB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,uBAAK,UAAS,MAAM;AAAA,EACxB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,mBAAK,UAAS;AAAA,EACzB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,uBAAK,UAAS,MAAM;AAAA,EACxB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,mBAAK,UAAS;AAAA,EACzB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,uBAAK,UAAS,MAAM;AAAA,EACxB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,uBAAK,UAAS,MAAM;AAAA,EACxB;AAAA,EACA,IAAI,IAAI,OAAO;AACX,QAAI,OAAO,UAAU,UAAU;AAC3B,yBAAK,UAAS,MAAM,cAAc,gBAAgB,KAAK;AAAA,IAC3D,WACS,iBAAiB,MAAM;AAC5B,yBAAK,UAAS,MAAM,cAAc,gBAAgB,cAAM,KAAK,CAAC;AAAA,IAClE,OACK;AACD,yBAAK,UAAS,MAAM,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,IAAI,IAAI,OAAO;AACX,QAAI,OAAO,UAAU,UAAU;AAC3B,yBAAK,UAAS,MAAM,cAAc,qBAAqB,KAAK;AAAA,IAChE,WACS,iBAAiB,MAAM;AAC5B,yBAAK,UAAS,MAAM,cAAc,qBAAqB,cAAM,KAAK,CAAC;AAAA,IACvE,OACK;AACD,yBAAK,UAAS,MAAM,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK;AAAA,IACtD;AAAA,EACJ;AAAA,EACA,IAAI,IAAI,OAAO;AACX,QAAI,OAAO,UAAU,aAAa;AAC9B,yBAAK,UAAS,MAAM,cAAM,oBAAI,KAAK,CAAC;AAAA,IACxC,WACS,iBAAiB,MAAM;AAC5B,yBAAK,UAAS,MAAM,cAAc,eAAe,cAAM,KAAK,CAAC;AAAA,IACjE,WACS,OAAO,UAAU,UAAU;AAChC,yBAAK,UAAS,MAAM,cAAc,eAAe,cAAM,oBAAI,KAAK,CAAC,IAAI,aAAK,KAAK,CAAC;AAAA,IACpF,OACK;AACD,yBAAK,UAAS,MAAM,cAAc,eAAe,KAAK;AAAA,IAC1D;AAAA,EACJ;AACJ;AAnEI;;;AClHJ,eAAsB,UAAU,KAAK,KAAK,SAAS;AAHnD,MAAAI;AAII,QAAM,WAAW,MAAM,cAAc,KAAK,KAAK,OAAO;AACtD,QAAIA,MAAA,SAAS,gBAAgB,SAAzB,gBAAAA,IAA+B,SAAS,WAAU,SAAS,gBAAgB,QAAQ,OAAO;AAC1F,UAAM,IAAI,WAAW,qCAAqC;AAAA,EAC9D;AACA,QAAM,UAAU,kBAAkB,SAAS,iBAAiB,SAAS,SAAS,OAAO;AACrF,QAAM,SAAS,EAAE,SAAS,iBAAiB,SAAS,gBAAgB;AACpE,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,SAAS,IAAI;AAAA,EAC1C;AACA,SAAO;AACX;;;ACXA,eAAsB,WAAW,KAAK,KAAK,SAAS;AAChD,QAAM,YAAY,MAAM,eAAe,KAAK,KAAK,OAAO;AACxD,QAAM,UAAU,kBAAkB,UAAU,iBAAiB,UAAU,WAAW,OAAO;AACzF,QAAM,EAAE,gBAAgB,IAAI;AAC5B,MAAI,gBAAgB,QAAQ,UAAa,gBAAgB,QAAQ,QAAQ,KAAK;AAC1E,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,MAAI,gBAAgB,QAAQ,UAAa,gBAAgB,QAAQ,QAAQ,KAAK;AAC1E,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,MAAI,gBAAgB,QAAQ,UACxB,KAAK,UAAU,gBAAgB,GAAG,MAAM,KAAK,UAAU,QAAQ,GAAG,GAAG;AACrE,UAAM,IAAI,yBAAyB,oDAAoD,SAAS,OAAO,UAAU;AAAA,EACrH;AACA,QAAM,SAAS,EAAE,SAAS,gBAAgB;AAC1C,MAAI,OAAO,QAAQ,YAAY;AAC3B,WAAO,EAAE,GAAG,QAAQ,KAAK,UAAU,IAAI;AAAA,EAC3C;AACA,SAAO;AACX;;;ACtBA;AACO,IAAM,iBAAN,MAAqB;AAAA,EAExB,YAAY,WAAW;AADvB;AAEI,uBAAK,YAAa,IAAI,iBAAiB,SAAS;AAAA,EACpD;AAAA,EACA,wBAAwB,KAAK;AACzB,uBAAK,YAAW,wBAAwB,GAAG;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,uBAAK,YAAW,wBAAwB,EAAE;AAC1C,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,uBAAK,YAAW,mBAAmB,eAAe;AAClD,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,YAAY;AACnC,uBAAK,YAAW,2BAA2B,UAAU;AACrD,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,UAAM,MAAM,MAAM,mBAAK,YAAW,QAAQ,KAAK,OAAO;AACtD,WAAO,CAAC,IAAI,WAAW,IAAI,eAAe,IAAI,IAAI,IAAI,YAAY,IAAI,GAAG,EAAE,KAAK,GAAG;AAAA,EACvF;AACJ;AAxBI;;;ACCJ,IAAO,eAAQ,OAAO,KAAK,KAAK,SAAS;AACrC,QAAM,YAAY,MAAM,4BAAW,KAAK,KAAK,MAAM;AACnD,2BAAe,KAAK,SAAS;AAC7B,QAAM,YAAY,MAAM,OAAO,OAAO,KAAK,mBAAgB,KAAK,UAAU,SAAS,GAAG,WAAW,IAAI;AACrG,SAAO,IAAI,WAAW,SAAS;AACnC;;;ACRA,IAAAC,WAAAC,mBAAAC;AAQO,IAAM,gBAAN,MAAoB;AAAA,EAIvB,YAAY,SAAS;AAHrB,uBAAAF,WAAA;AACA,uBAAAC,mBAAA;AACA,uBAAAC,qBAAA;AAEI,QAAI,EAAE,mBAAmB,aAAa;AAClC,YAAM,IAAI,UAAU,2CAA2C;AAAA,IACnE;AACA,uBAAKF,WAAW;AAAA,EACpB;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,mBAAKC,oBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,uBAAKA,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,mBAAKC,sBAAoB;AACzB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,uBAAKA,qBAAqB;AAC1B,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AACrB,QAAI,CAAC,mBAAKD,sBAAoB,CAAC,mBAAKC,sBAAoB;AACpD,YAAM,IAAI,WAAW,iFAAiF;AAAA,IAC1G;AACA,QAAI,CAAC,oBAAW,mBAAKD,oBAAkB,mBAAKC,oBAAkB,GAAG;AAC7D,YAAM,IAAI,WAAW,2EAA2E;AAAA,IACpG;AACA,UAAM,aAAa;AAAA,MACf,GAAG,mBAAKD;AAAA,MACR,GAAG,mBAAKC;AAAA,IACZ;AACA,UAAM,aAAa,sBAAa,YAAY,oBAAI,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,mCAAS,MAAM,mBAAKD,oBAAkB,UAAU;AACtH,QAAI,MAAM;AACV,QAAI,WAAW,IAAI,KAAK,GAAG;AACvB,YAAM,mBAAKA,mBAAiB;AAC5B,UAAI,OAAO,QAAQ,WAAW;AAC1B,cAAM,IAAI,WAAW,yEAAyE;AAAA,MAClG;AAAA,IACJ;AACA,UAAM,EAAE,IAAI,IAAI;AAChB,QAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;AACjC,YAAM,IAAI,WAAW,2DAA2D;AAAA,IACpF;AACA,2BAAa,KAAK,KAAK,MAAM;AAC7B,QAAI,UAAU,mBAAKD;AACnB,QAAI,KAAK;AACL,gBAAU,QAAQ,OAAO,OAAK,OAAO,CAAC;AAAA,IAC1C;AACA,QAAI;AACJ,QAAI,mBAAKC,oBAAkB;AACvB,wBAAkB,QAAQ,OAAO,OAAK,KAAK,UAAU,mBAAKA,kBAAgB,CAAC,CAAC;AAAA,IAChF,OACK;AACD,wBAAkB,QAAQ,OAAO,EAAE;AAAA,IACvC;AACA,UAAM,OAAO,OAAO,iBAAiB,QAAQ,OAAO,GAAG,GAAG,OAAO;AACjE,UAAM,IAAI,MAAM,sBAAa,KAAK,GAAG;AACrC,UAAM,YAAY,MAAM,aAAK,KAAK,GAAG,IAAI;AACzC,UAAM,MAAM;AAAA,MACR,WAAW,OAAK,SAAS;AAAA,MACzB,SAAS;AAAA,IACb;AACA,QAAI,KAAK;AACL,UAAI,UAAU,QAAQ,OAAO,OAAO;AAAA,IACxC;AACA,QAAI,mBAAKC,sBAAoB;AACzB,UAAI,SAAS,mBAAKA;AAAA,IACtB;AACA,QAAI,mBAAKD,oBAAkB;AACvB,UAAI,YAAY,QAAQ,OAAO,eAAe;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AACJ;AA5EID,YAAA;AACAC,oBAAA;AACAC,sBAAA;;;ACXJ,IAAAC;AACO,IAAM,cAAN,MAAkB;AAAA,EAErB,YAAY,SAAS;AADrB,uBAAAA,aAAA;AAEI,uBAAKA,aAAa,IAAI,cAAc,OAAO;AAAA,EAC/C;AAAA,EACA,mBAAmB,iBAAiB;AAChC,uBAAKA,aAAW,mBAAmB,eAAe;AAClD,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AACrB,UAAM,MAAM,MAAM,mBAAKA,aAAW,KAAK,KAAK,OAAO;AACnD,QAAI,IAAI,YAAY,QAAW;AAC3B,YAAM,IAAI,UAAU,2DAA2D;AAAA,IACnF;AACA,WAAO,GAAG,IAAI,SAAS,IAAI,IAAI,OAAO,IAAI,IAAI,SAAS;AAAA,EAC3D;AACJ;AAfIA,cAAA;;;ACFJ,IAAAC;AAEA,IAAM,sBAAN,MAA0B;AAAA,EAMtB,YAAY,KAAK,KAAK,SAAS;AAL/B,uBAAAA,UAAA;AACA;AACA;AACA;AACA;AAEI,uBAAKA,UAAU;AACf,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,KAAK,iBAAiB;AACtB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,SAAK,kBAAkB;AACvB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,mBAAmB;AACpC,QAAI,KAAK,mBAAmB;AACxB,YAAM,IAAI,UAAU,8CAA8C;AAAA,IACtE;AACA,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACX;AAAA,EACA,gBAAgB,MAAM;AAClB,WAAO,mBAAKA,UAAQ,aAAa,GAAG,IAAI;AAAA,EAC5C;AAAA,EACA,QAAQ,MAAM;AACV,WAAO,mBAAKA,UAAQ,KAAK,GAAG,IAAI;AAAA,EACpC;AAAA,EACA,OAAO;AACH,WAAO,mBAAKA;AAAA,EAChB;AACJ;AAjCIA,WAAA;AAHJ,IAAAC,WAAA;AAqCO,IAAM,cAAN,MAAkB;AAAA,EAGrB,YAAY,SAAS;AAFrB,uBAAAA,WAAA;AACA,oCAAc,CAAC;AAEX,uBAAKA,WAAW;AAAA,EACpB;AAAA,EACA,aAAa,KAAK,SAAS;AACvB,UAAM,YAAY,IAAI,oBAAoB,MAAM,KAAK,OAAO;AAC5D,uBAAK,aAAY,KAAK,SAAS;AAC/B,WAAO;AAAA,EACX;AAAA,EACA,MAAM,OAAO;AACT,QAAI,CAAC,mBAAK,aAAY,QAAQ;AAC1B,YAAM,IAAI,WAAW,sCAAsC;AAAA,IAC/D;AACA,UAAM,MAAM;AAAA,MACR,YAAY,CAAC;AAAA,MACb,SAAS;AAAA,IACb;AACA,aAAS,IAAI,GAAG,IAAI,mBAAK,aAAY,QAAQ,KAAK;AAC9C,YAAM,YAAY,mBAAK,aAAY,CAAC;AACpC,YAAM,YAAY,IAAI,cAAc,mBAAKA,UAAQ;AACjD,gBAAU,mBAAmB,UAAU,eAAe;AACtD,gBAAU,qBAAqB,UAAU,iBAAiB;AAC1D,YAAM,EAAE,SAAS,GAAG,KAAK,IAAI,MAAM,UAAU,KAAK,UAAU,KAAK,UAAU,OAAO;AAClF,UAAI,MAAM,GAAG;AACT,YAAI,UAAU;AAAA,MAClB,WACS,IAAI,YAAY,SAAS;AAC9B,cAAM,IAAI,WAAW,qDAAqD;AAAA,MAC9E;AACA,UAAI,WAAW,KAAK,IAAI;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACJ;AAlCIA,YAAA;AACA;;;ACvCJ,IAAAC,mBAAA;AAGO,IAAM,UAAN,MAAc;AAAA,EAGjB,YAAY,UAAU,CAAC,GAAG;AAF1B,uBAAAA,mBAAA;AACA;AAEI,uBAAK,MAAO,IAAI,iBAAiB,OAAO;AAAA,EAC5C;AAAA,EACA,UAAU,QAAQ;AACd,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,WAAW,SAAS;AAChB,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU;AAClB,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO;AACrB,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,uBAAK,MAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,uBAAKA,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,MAAM,KAAK,KAAK,SAAS;AAzC7B,QAAAC;AA0CQ,UAAM,MAAM,IAAI,YAAY,mBAAK,MAAK,KAAK,CAAC;AAC5C,QAAI,mBAAmB,mBAAKD,kBAAgB;AAC5C,QAAI,MAAM,SAAQC,MAAA,mBAAKD,uBAAL,gBAAAC,IAAuB,IAAI,KACzC,mBAAKD,mBAAiB,KAAK,SAAS,KAAK,KACzC,mBAAKA,mBAAiB,QAAQ,OAAO;AACrC,YAAM,IAAI,WAAW,qCAAqC;AAAA,IAC9D;AACA,WAAO,IAAI,KAAK,KAAK,OAAO;AAAA,EAChC;AACJ;AA/CIA,oBAAA;AACA;;;ACLJ,IAAAE,OAAAC,MAAAC,2BAAAC,mBAAA,iFAAAC;AAEO,IAAM,aAAN,MAAiB;AAAA,EASpB,YAAY,UAAU,CAAC,GAAG;AAR1B,uBAAAJ,OAAA;AACA,uBAAAC,MAAA;AACA,uBAAAC,2BAAA;AACA,uBAAAC,mBAAA;AACA;AACA;AACA;AACA,uBAAAC,OAAA;AAEI,uBAAKA,OAAO,IAAI,iBAAiB,OAAO;AAAA,EAC5C;AAAA,EACA,UAAU,QAAQ;AACd,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,WAAW,SAAS;AAChB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU;AAClB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO;AACrB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,mBAAmB,iBAAiB;AAChC,QAAI,mBAAKD,oBAAkB;AACvB,YAAM,IAAI,UAAU,4CAA4C;AAAA,IACpE;AACA,uBAAKA,mBAAmB;AACxB,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B,YAAY;AACnC,QAAI,mBAAKD,4BAA0B;AAC/B,YAAM,IAAI,UAAU,oDAAoD;AAAA,IAC5E;AACA,uBAAKA,2BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,KAAK;AACzB,QAAI,mBAAKF,QAAM;AACX,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,uBAAKA,OAAO;AACZ,WAAO;AAAA,EACX;AAAA,EACA,wBAAwB,IAAI;AACxB,QAAI,mBAAKC,OAAK;AACV,YAAM,IAAI,UAAU,iDAAiD;AAAA,IACzE;AACA,uBAAKA,MAAM;AACX,WAAO;AAAA,EACX;AAAA,EACA,0BAA0B;AACtB,uBAAK,0BAA2B;AAChC,WAAO;AAAA,EACX;AAAA,EACA,2BAA2B;AACvB,uBAAK,2BAA4B;AACjC,WAAO;AAAA,EACX;AAAA,EACA,4BAA4B;AACxB,uBAAK,4BAA6B;AAClC,WAAO;AAAA,EACX;AAAA,EACA,MAAM,QAAQ,KAAK,SAAS;AACxB,UAAM,MAAM,IAAI,eAAe,mBAAKG,OAAK,KAAK,CAAC;AAC/C,QAAI,mBAAKD,uBACJ,mBAAK,6BACF,mBAAK,8BACL,mBAAK,8BAA6B;AACtC,yBAAKA,mBAAmB;AAAA,QACpB,GAAG,mBAAKA;AAAA,QACR,KAAK,mBAAK,4BAA2B,mBAAKC,OAAK,MAAM;AAAA,QACrD,KAAK,mBAAK,6BAA4B,mBAAKA,OAAK,MAAM;AAAA,QACtD,KAAK,mBAAK,8BAA6B,mBAAKA,OAAK,MAAM;AAAA,MAC3D;AAAA,IACJ;AACA,QAAI,mBAAmB,mBAAKD,kBAAgB;AAC5C,QAAI,mBAAKF,OAAK;AACV,UAAI,wBAAwB,mBAAKA,KAAG;AAAA,IACxC;AACA,QAAI,mBAAKD,QAAM;AACX,UAAI,wBAAwB,mBAAKA,MAAI;AAAA,IACzC;AACA,QAAI,mBAAKE,4BAA0B;AAC/B,UAAI,2BAA2B,mBAAKA,0BAAwB;AAAA,IAChE;AACA,WAAO,IAAI,QAAQ,KAAK,OAAO;AAAA,EACnC;AACJ;AAxGIF,QAAA;AACAC,OAAA;AACAC,4BAAA;AACAC,oBAAA;AACA;AACA;AACA;AACAC,QAAA;;;ACFJ,IAAM,QAAQ,CAAC,OAAO,gBAAgB;AAClC,MAAI,OAAO,UAAU,YAAY,CAAC,OAAO;AACrC,UAAM,IAAI,WAAW,GAAG,WAAW,qBAAqB;AAAA,EAC5D;AACJ;AACA,eAAsB,uBAAuB,KAAK,iBAAiB;AAC/D,MAAI;AACJ,MAAI,MAAM,GAAG,GAAG;AACZ,UAAM;AAAA,EACV,WACS,oBAAU,GAAG,GAAG;AACrB,UAAM,MAAM,UAAU,GAAG;AAAA,EAC7B,OACK;AACD,UAAM,IAAI,UAAU,0BAAgB,KAAK,aAAa,aAAa,cAAc,CAAC;AAAA,EACtF;AACA,wCAAoB;AACpB,MAAI,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,UAAU;AAC9B,UAAM,IAAI,UAAU,6DAA6D;AAAA,EACrF;AACA,MAAI;AACJ,UAAQ,IAAI,KAAK;AAAA,IACb,KAAK;AACD,YAAM,IAAI,KAAK,yBAAyB;AACxC,YAAM,IAAI,GAAG,8BAA8B;AAC3C,YAAM,IAAI,GAAG,8BAA8B;AAC3C,mBAAa,EAAE,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,GAAG,GAAG,IAAI,EAAE;AAC9D;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,KAAK,uCAAuC;AACtD,YAAM,IAAI,GAAG,4BAA4B;AACzC,mBAAa,EAAE,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;AACpD;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,GAAG,0BAA0B;AACvC,YAAM,IAAI,GAAG,yBAAyB;AACtC,mBAAa,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,EAAE;AAChD;AAAA,IACJ,KAAK;AACD,YAAM,IAAI,GAAG,2BAA2B;AACxC,mBAAa,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI,IAAI;AACtC;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,mDAAmD;AAAA,EACtF;AACA,QAAM,OAAO,QAAQ,OAAO,KAAK,UAAU,UAAU,CAAC;AACtD,SAAO,OAAK,MAAM,eAAO,iBAAiB,IAAI,CAAC;AACnD;AACA,eAAsB,0BAA0B,KAAK,iBAAiB;AAClE,wCAAoB;AACpB,QAAM,aAAa,MAAM,uBAAuB,KAAK,eAAe;AACpE,SAAO,4CAA4C,gBAAgB,MAAM,EAAE,CAAC,IAAI,UAAU;AAC9F;;;AC3DA,eAAsB,YAAY,iBAAiB,OAAO;AACtD,QAAM,aAAa;AAAA,IACf,GAAG;AAAA,IACH,GAAG,+BAAO;AAAA,EACd;AACA,MAAI,CAAC,kBAAS,WAAW,GAAG,GAAG;AAC3B,UAAM,IAAI,WAAW,6DAA6D;AAAA,EACtF;AACA,QAAM,MAAM,MAAM,UAAU,EAAE,GAAG,WAAW,KAAK,KAAK,KAAK,GAAG,WAAW,GAAG;AAC5E,MAAI,eAAe,cAAc,IAAI,SAAS,UAAU;AACpD,UAAM,IAAI,WAAW,4DAA4D;AAAA,EACrF;AACA,SAAO;AACX;;;ACbA,SAAS,cAAc,KAAK;AACxB,UAAQ,OAAO,QAAQ,YAAY,IAAI,MAAM,GAAG,CAAC,GAAG;AAAA,IAChD,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,YAAM,IAAI,iBAAiB,gDAAgD;AAAA,EACnF;AACJ;AACA,SAAS,WAAW,MAAM;AACtB,SAAQ,QACJ,OAAO,SAAS,YAChB,MAAM,QAAQ,KAAK,IAAI,KACvB,KAAK,KAAK,MAAM,SAAS;AACjC;AACA,SAAS,UAAU,KAAK;AACpB,SAAO,kBAAS,GAAG;AACvB;AAxBA;AAyBA,IAAM,cAAN,MAAkB;AAAA,EAGd,YAAY,MAAM;AAFlB;AACA,gCAAU,oBAAI,QAAQ;AAElB,QAAI,CAAC,WAAW,IAAI,GAAG;AACnB,YAAM,IAAI,YAAY,4BAA4B;AAAA,IACtD;AACA,uBAAK,OAAQ,gBAAgB,IAAI;AAAA,EACrC;AAAA,EACA,OAAO;AACH,WAAO,mBAAK;AAAA,EAChB;AAAA,EACA,MAAM,OAAO,iBAAiB,OAAO;AACjC,UAAM,EAAE,KAAK,IAAI,IAAI,EAAE,GAAG,iBAAiB,GAAG,+BAAO,OAAO;AAC5D,UAAM,MAAM,cAAc,GAAG;AAC7B,UAAM,aAAa,mBAAK,OAAM,KAAK,OAAO,CAACC,SAAQ;AAC/C,UAAI,YAAY,QAAQA,KAAI;AAC5B,UAAI,aAAa,OAAO,QAAQ,UAAU;AACtC,oBAAY,QAAQA,KAAI;AAAA,MAC5B;AACA,UAAI,aAAa,OAAOA,KAAI,QAAQ,UAAU;AAC1C,oBAAY,QAAQA,KAAI;AAAA,MAC5B;AACA,UAAI,aAAa,OAAOA,KAAI,QAAQ,UAAU;AAC1C,oBAAYA,KAAI,QAAQ;AAAA,MAC5B;AACA,UAAI,aAAa,MAAM,QAAQA,KAAI,OAAO,GAAG;AACzC,oBAAYA,KAAI,QAAQ,SAAS,QAAQ;AAAA,MAC7C;AACA,UAAI,WAAW;AACX,gBAAQ,KAAK;AAAA,UACT,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AACD,wBAAYA,KAAI,QAAQ;AACxB;AAAA,QACR;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,UAAM,EAAE,GAAG,KAAK,OAAO,IAAI;AAC3B,QAAI,WAAW,GAAG;AACd,YAAM,IAAI,kBAAkB;AAAA,IAChC;AACA,QAAI,WAAW,GAAG;AACd,YAAM,QAAQ,IAAI,yBAAyB;AAC3C,YAAMC,WAAU,mBAAK;AACrB,YAAM,OAAO,aAAa,IAAI,mBAAmB;AAC7C,mBAAWD,QAAO,YAAY;AAC1B,cAAI;AACA,kBAAM,MAAM,mBAAmBC,UAASD,MAAK,GAAG;AAAA,UACpD,QACM;AAAA,UAAE;AAAA,QACZ;AAAA,MACJ;AACA,YAAM;AAAA,IACV;AACA,WAAO,mBAAmB,mBAAK,UAAS,KAAK,GAAG;AAAA,EACpD;AACJ;AAlEI;AACA;AAkEJ,eAAe,mBAAmBE,QAAO,KAAK,KAAK;AAC/C,QAAM,SAASA,OAAM,IAAI,GAAG,KAAKA,OAAM,IAAI,KAAK,CAAC,CAAC,EAAE,IAAI,GAAG;AAC3D,MAAI,OAAO,GAAG,MAAM,QAAW;AAC3B,UAAM,MAAM,MAAM,UAAU,EAAE,GAAG,KAAK,KAAK,KAAK,GAAG,GAAG;AACtD,QAAI,eAAe,cAAc,IAAI,SAAS,UAAU;AACpD,YAAM,IAAI,YAAY,8CAA8C;AAAA,IACxE;AACA,WAAO,GAAG,IAAI;AAAA,EAClB;AACA,SAAO,OAAO,GAAG;AACrB;AACO,SAAS,kBAAkB,MAAM;AACpC,QAAM,MAAM,IAAI,YAAY,IAAI;AAChC,QAAM,cAAc,OAAO,iBAAiB,UAAU,IAAI,OAAO,iBAAiB,KAAK;AACvF,SAAO,iBAAiB,aAAa;AAAA,IACjC,MAAM;AAAA,MACF,OAAO,MAAM,gBAAgB,IAAI,KAAK,CAAC;AAAA,MACvC,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;ACjHA,SAAS,sBAAsB;AAC3B,SAAQ,OAAO,kBAAkB,eAC5B,OAAO,cAAc,eAAe,UAAU,cAAc,wBAC5D,OAAO,gBAAgB,eAAe,gBAAgB;AAC/D;AACA,IAAI;AARJ,IAAAC,KAAA;AASA,IAAI,OAAO,cAAc,eAAe,GAAC,MAAAA,MAAA,UAAU,cAAV,gBAAAA,IAAqB,eAArB,wBAAAA,KAAkC,kBAAiB;AACxF,QAAM,OAAO;AACb,QAAM,UAAU;AAChB,eAAa,GAAG,IAAI,IAAI,OAAO;AACnC;AACO,IAAM,cAAc,OAAO;AAClC,eAAe,UAAU,KAAK,SAAS,QAAQ,YAAY,OAAO;AAC9D,QAAM,WAAW,MAAM,UAAU,KAAK;AAAA,IAClC,QAAQ;AAAA,IACR;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACJ,CAAC,EAAE,MAAM,CAAC,QAAQ;AACd,QAAI,IAAI,SAAS,gBAAgB;AAC7B,YAAM,IAAI,YAAY;AAAA,IAC1B;AACA,UAAM;AAAA,EACV,CAAC;AACD,MAAI,SAAS,WAAW,KAAK;AACzB,UAAM,IAAI,UAAU,yDAAyD;AAAA,EACjF;AACA,MAAI;AACA,WAAO,MAAM,SAAS,KAAK;AAAA,EAC/B,QACM;AACF,UAAM,IAAI,UAAU,4DAA4D;AAAA,EACpF;AACJ;AACO,IAAM,YAAY,OAAO;AAChC,SAAS,iBAAiB,OAAO,aAAa;AAC1C,MAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAC7C,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,UAAU,OAAO,MAAM,QAAQ,YAAY,KAAK,IAAI,IAAI,MAAM,OAAO,aAAa;AAC7F,WAAO;AAAA,EACX;AACA,MAAI,EAAE,UAAU,UACZ,CAAC,kBAAS,MAAM,IAAI,KACpB,CAAC,MAAM,QAAQ,MAAM,KAAK,IAAI,KAC9B,CAAC,MAAM,UAAU,MAAM,KAAK,MAAM,KAAK,MAAM,iBAAQ,GAAG;AACxD,WAAO;AAAA,EACX;AACA,SAAO;AACX;AApDA;AAqDA,IAAM,eAAN,MAAmB;AAAA,EAWf,YAAY,KAAK,SAAS;AAV1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEI,QAAI,EAAE,eAAe,MAAM;AACvB,YAAM,IAAI,UAAU,gCAAgC;AAAA,IACxD;AACA,uBAAK,MAAO,IAAI,IAAI,IAAI,IAAI;AAC5B,uBAAK,kBACD,QAAO,mCAAS,qBAAoB,WAAW,mCAAS,kBAAkB;AAC9E,uBAAK,mBACD,QAAO,mCAAS,sBAAqB,WAAW,mCAAS,mBAAmB;AAChF,uBAAK,cAAe,QAAO,mCAAS,iBAAgB,WAAW,mCAAS,cAAc;AACtF,uBAAK,UAAW,IAAI,QAAQ,mCAAS,OAAO;AAC5C,QAAI,cAAc,CAAC,mBAAK,UAAS,IAAI,YAAY,GAAG;AAChD,yBAAK,UAAS,IAAI,cAAc,UAAU;AAAA,IAC9C;AACA,QAAI,CAAC,mBAAK,UAAS,IAAI,QAAQ,GAAG;AAC9B,yBAAK,UAAS,IAAI,UAAU,kBAAkB;AAC9C,yBAAK,UAAS,OAAO,UAAU,0BAA0B;AAAA,IAC7D;AACA,uBAAK,cAAe,mCAAU;AAC9B,SAAI,mCAAU,gBAAe,QAAW;AACpC,yBAAK,QAAS,mCAAU;AACxB,UAAI,iBAAiB,mCAAU,YAAY,mBAAK,aAAY,GAAG;AAC3D,2BAAK,gBAAiB,mBAAK,QAAO;AAClC,2BAAK,QAAS,kBAAkB,mBAAK,QAAO,IAAI;AAAA,MACpD;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,eAAe;AACX,WAAO,CAAC,CAAC,mBAAK;AAAA,EAClB;AAAA,EACA,cAAc;AACV,WAAO,OAAO,mBAAK,oBAAmB,WAChC,KAAK,IAAI,IAAI,mBAAK,kBAAiB,mBAAK,qBACxC;AAAA,EACV;AAAA,EACA,QAAQ;AACJ,WAAO,OAAO,mBAAK,oBAAmB,WAChC,KAAK,IAAI,IAAI,mBAAK,kBAAiB,mBAAK,gBACxC;AAAA,EACV;AAAA,EACA,OAAO;AAxGX,QAAAA;AAyGQ,YAAOA,MAAA,mBAAK,YAAL,gBAAAA,IAAa;AAAA,EACxB;AAAA,EACA,MAAM,OAAO,iBAAiB,OAAO;AACjC,QAAI,CAAC,mBAAK,WAAU,CAAC,KAAK,MAAM,GAAG;AAC/B,YAAM,KAAK,OAAO;AAAA,IACtB;AACA,QAAI;AACA,aAAO,MAAM,mBAAK,QAAL,WAAY,iBAAiB;AAAA,IAC9C,SACO,KAAK;AACR,UAAI,eAAe,mBAAmB;AAClC,YAAI,KAAK,YAAY,MAAM,OAAO;AAC9B,gBAAM,KAAK,OAAO;AAClB,iBAAO,mBAAK,QAAL,WAAY,iBAAiB;AAAA,QACxC;AAAA,MACJ;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA,EACA,MAAM,SAAS;AACX,QAAI,mBAAK,kBAAiB,oBAAoB,GAAG;AAC7C,yBAAK,eAAgB;AAAA,IACzB;AACA,uBAAK,kBAAL,mBAAK,eAAkB,UAAU,mBAAK,MAAK,MAAM,mBAAK,WAAU,YAAY,QAAQ,mBAAK,iBAAgB,GAAG,mBAAK,aAAY,EACxH,KAAK,CAAC,SAAS;AAChB,yBAAK,QAAS,kBAAkB,IAAI;AACpC,UAAI,mBAAK,SAAQ;AACb,2BAAK,QAAO,MAAM,KAAK,IAAI;AAC3B,2BAAK,QAAO,OAAO;AAAA,MACvB;AACA,yBAAK,gBAAiB,KAAK,IAAI;AAC/B,yBAAK,eAAgB;AAAA,IACzB,CAAC,EACI,MAAM,CAAC,QAAQ;AAChB,yBAAK,eAAgB;AACrB,YAAM;AAAA,IACV,CAAC;AACD,UAAM,mBAAK;AAAA,EACf;AACJ;AA1FI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAkFG,SAAS,mBAAmB,KAAK,SAAS;AAC7C,QAAM,MAAM,IAAI,aAAa,KAAK,OAAO;AACzC,QAAM,eAAe,OAAO,iBAAiB,UAAU,IAAI,OAAO,iBAAiB,KAAK;AACxF,SAAO,iBAAiB,cAAc;AAAA,IAClC,aAAa;AAAA,MACT,KAAK,MAAM,IAAI,YAAY;AAAA,MAC3B,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,OAAO;AAAA,MACH,KAAK,MAAM,IAAI,MAAM;AAAA,MACrB,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,QAAQ;AAAA,MACJ,OAAO,MAAM,IAAI,OAAO;AAAA,MACxB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,IACA,WAAW;AAAA,MACP,KAAK,MAAM,IAAI,aAAa;AAAA,MAC5B,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB;AAAA,IACA,MAAM;AAAA,MACF,OAAO,MAAM,IAAI,KAAK;AAAA,MACtB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACd;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;AClLA,IAAAC;AAIO,IAAM,eAAN,MAAmB;AAAA,EAEtB,YAAY,UAAU,CAAC,GAAG;AAD1B,uBAAAA,OAAA;AAEI,uBAAKA,OAAO,IAAI,iBAAiB,OAAO;AAAA,EAC5C;AAAA,EACA,SAAS;AACL,UAAM,SAAc,OAAO,KAAK,UAAU,EAAE,KAAK,OAAO,CAAC,CAAC;AAC1D,UAAM,UAAe,OAAO,mBAAKA,OAAK,KAAK,CAAC;AAC5C,WAAO,GAAG,MAAM,IAAI,OAAO;AAAA,EAC/B;AAAA,EACA,UAAU,QAAQ;AACd,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,WAAW,SAAS;AAChB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,UAAU;AAClB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO;AACV,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,aAAa,OAAO;AAChB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,OAAO;AACrB,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,YAAY,OAAO;AACf,uBAAKA,OAAK,MAAM;AAChB,WAAO;AAAA,EACX;AAAA,EACA,OAAO,OAAO,KAAK,SAAS;AACxB,QAAI,OAAO,QAAQ,UAAU;AACzB,YAAM,IAAI,WAAW,gCAAgC;AAAA,IACzD;AACA,UAAM,EAAE,GAAG,eAAe,GAAG,gBAAgB,GAAG,WAAW,OAAO,IAAI,IAAI,MAAM,GAAG;AACnF,QAAI,WAAW,KAAK,cAAc,IAAI;AAClC,YAAM,IAAI,WAAW,uBAAuB;AAAA,IAChD;AACA,QAAI;AACJ,QAAI;AACA,eAAS,KAAK,MAAM,QAAQ,OAAY,OAAO,aAAa,CAAC,CAAC;AAC9D,UAAI,OAAO,QAAQ;AACf,cAAM,IAAI,MAAM;AAAA,IACxB,QACM;AACF,YAAM,IAAI,WAAW,uBAAuB;AAAA,IAChD;AACA,UAAM,UAAU,kBAAkB,QAAa,OAAO,cAAc,GAAG,OAAO;AAC9E,WAAO,EAAE,SAAS,OAAO;AAAA,EAC7B;AACJ;AAzDIA,QAAA;;;ACFG,SAAS,sBAAsB,OAAO;AACzC,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC3B,UAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAI,MAAM,WAAW,KAAK,MAAM,WAAW,GAAG;AAC1C;AACA,OAAC,aAAa,IAAI;AAAA,IACtB;AAAA,EACJ,WACS,OAAO,UAAU,YAAY,OAAO;AACzC,QAAI,eAAe,OAAO;AACtB,sBAAgB,MAAM;AAAA,IAC1B,OACK;AACD,YAAM,IAAI,UAAU,2CAA2C;AAAA,IACnE;AAAA,EACJ;AACA,MAAI;AACA,QAAI,OAAO,kBAAkB,YAAY,CAAC,eAAe;AACrD,YAAM,IAAI,MAAM;AAAA,IACpB;AACA,UAAM,SAAS,KAAK,MAAM,QAAQ,OAAO,OAAK,aAAa,CAAC,CAAC;AAC7D,QAAI,CAAC,kBAAS,MAAM,GAAG;AACnB,YAAM,IAAI,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACX,QACM;AACF,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACtE;AACJ;;;AC7BO,SAAS,UAAU,KAAK;AAC3B,MAAI,OAAO,QAAQ;AACf,UAAM,IAAI,WAAW,+DAA+D;AACxF,QAAM,EAAE,GAAG,SAAS,OAAO,IAAI,IAAI,MAAM,GAAG;AAC5C,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,0DAA0D;AACnF,MAAI,WAAW;AACX,UAAM,IAAI,WAAW,aAAa;AACtC,MAAI,CAAC;AACD,UAAM,IAAI,WAAW,6BAA6B;AACtD,MAAI;AACJ,MAAI;AACA,cAAU,OAAK,OAAO;AAAA,EAC1B,QACM;AACF,UAAM,IAAI,WAAW,wCAAwC;AAAA,EACjE;AACA,MAAI;AACJ,MAAI;AACA,aAAS,KAAK,MAAM,QAAQ,OAAO,OAAO,CAAC;AAAA,EAC/C,QACM;AACF,UAAM,IAAI,WAAW,6CAA6C;AAAA,EACtE;AACA,MAAI,CAAC,kBAAS,MAAM;AAChB,UAAM,IAAI,WAAW,wBAAwB;AACjD,SAAO;AACX;;;AC9BA,SAAS,uBAAuB,SAAS;AACrC,QAAM,iBAAgB,mCAAS,kBAAiB;AAChD,MAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;AAC3D,UAAM,IAAI,iBAAiB,6FAA6F;AAAA,EAC5H;AACA,SAAO;AACX;AACA,eAAsB,gBAAgB,KAAK,SAAS;AAChD,MAAI;AACJ,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY;AAAA,QACR,MAAM;AAAA,QACN,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;AAAA,QAC1B,gBAAgB,IAAI,WAAW,CAAC,GAAM,GAAM,CAAI,CAAC;AAAA,QACjD,eAAe,uBAAuB,OAAO;AAAA,MACjD;AACA,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY;AAAA,QACR,MAAM;AAAA,QACN,MAAM,OAAO,IAAI,MAAM,EAAE,CAAC;AAAA,QAC1B,gBAAgB,IAAI,WAAW,CAAC,GAAM,GAAM,CAAI,CAAC;AAAA,QACjD,eAAe,uBAAuB,OAAO;AAAA,MACjD;AACA,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,kBAAY;AAAA,QACR,MAAM;AAAA,QACN,MAAM,OAAO,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE,KAAK,CAAC;AAAA,QAC7C,gBAAgB,IAAI,WAAW,CAAC,GAAM,GAAM,CAAI,CAAC;AAAA,QACjD,eAAe,uBAAuB,OAAO;AAAA,MACjD;AACA,kBAAY,CAAC,WAAW,aAAa,WAAW,SAAS;AACzD;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AACD,kBAAY,EAAE,MAAM,SAAS,YAAY,QAAQ;AACjD,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK,SAAS;AACV,kBAAY,CAAC,QAAQ,QAAQ;AAC7B,kBAAY,EAAE,MAAM,UAAU;AAC9B;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,kBAAkB;AACnB,kBAAY,CAAC,YAAY;AACzB,YAAM,OAAM,mCAAS,QAAO;AAC5B,cAAQ,KAAK;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,SAAS;AACV,sBAAY,EAAE,MAAM,QAAQ,YAAY,IAAI;AAC5C;AAAA,QACJ;AAAA,QACA,KAAK;AACD,sBAAY,EAAE,MAAM,SAAS;AAC7B;AAAA,QACJ;AACI,gBAAM,IAAI,iBAAiB,kGAAkG;AAAA,MACrI;AACA;AAAA,IACJ;AAAA,IACA;AACI,YAAM,IAAI,iBAAiB,8DAA8D;AAAA,EACjG;AACA,SAAO,OAAO,OAAO,YAAY,YAAW,mCAAS,gBAAe,OAAO,SAAS;AACxF;;;ACxFA,eAAsB,eAAe,KAAK,SAAS;AAC/C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,KAAK;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AACnC,kBAAY,EAAE,MAAM,QAAQ,MAAM,OAAO,MAAM,IAAI,OAAO;AAC1D,kBAAY,CAAC,QAAQ,QAAQ;AAC7B;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,EAAE,GAAG,EAAE;AACnC,aAAO,OAAO,gBAAgB,IAAI,WAAW,UAAU,CAAC,CAAC;AAAA,IAC7D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,kBAAY,EAAE,MAAM,UAAU,OAAO;AACrC,kBAAY,CAAC,WAAW,WAAW;AACnC;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACD,eAAS,SAAS,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE;AACrC,kBAAY,EAAE,MAAM,WAAW,OAAO;AACtC,kBAAY,CAAC,WAAW,SAAS;AACjC;AAAA,IACJ;AACI,YAAM,IAAI,iBAAiB,8DAA8D;AAAA,EACjG;AACA,SAAO,OAAO,OAAO,YAAY,YAAW,mCAAS,gBAAe,OAAO,SAAS;AACxF;;;ACVO,IAAM,gBAAgB;", "names": ["message", "_a", "_a", "tag", "getCryptoKey", "<PERSON><PERSON><PERSON>", "wrap", "unwrap", "bitLength", "getNamedCurve", "tag", "byteLength", "_a", "_b", "tag", "wrap", "unwrap", "tag", "bitLength", "unwrap", "tag", "_a", "_a", "_b", "<PERSON><PERSON><PERSON><PERSON>", "tag", "tag", "bitLength", "wrap", "tag", "_plaintext", "_protected<PERSON>eader", "_unprotectedHeader", "_aad", "_a", "_b", "jwe", "_a", "_payload", "_protected<PERSON>eader", "_unprotectedHeader", "_flattened", "_parent", "_payload", "_protected<PERSON>eader", "_a", "_cek", "_iv", "_keyManagementParameters", "_protected<PERSON>eader", "_jwt", "jwk", "_cached", "cache", "_a", "_jwt"]}