/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface AnalyticsOperation {
    /** This is the aggregation operation you want to perform. */
    operation: Vapi.AnalyticsOperationOperation;
    /** This is the columns you want to perform the aggregation operation on. */
    column: Vapi.AnalyticsOperationColumn;
    /** This is the alias for column name returned. Defaults to `${operation}${column}`. */
    alias?: string;
}
