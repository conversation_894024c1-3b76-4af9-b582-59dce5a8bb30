/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface TranscriberCost {
    /** This is the type of cost, always 'transcriber' for this class. */
    type: "transcriber";
    /**
     * This is the transcriber that was used during the call.
     *
     * This matches one of the below:
     * - `call.assistant.transcriber`,
     * - `call.assistantId->transcriber`,
     * - `call.squad[n].assistant.transcriber`,
     * - `call.squad[n].assistantId->transcriber`,
     * - `call.squadId->[n].assistant.transcriber`,
     * - `call.squadId->[n].assistantId->transcriber`.
     */
    transcriber: Record<string, unknown>;
    /** This is the minutes of `transcriber` usage. This should match `call.endedAt` - `call.startedAt` for single assistant calls, while squad calls will have multiple transcriber costs one for each assistant that was used. */
    minutes: number;
    /** This is the cost of the component in USD. */
    cost: number;
}
