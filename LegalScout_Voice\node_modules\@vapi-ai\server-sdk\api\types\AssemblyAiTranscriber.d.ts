/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as Vapi from "../index";
export interface AssemblyAiTranscriber {
    /** This is the transcription provider that will be used. */
    provider: "assembly-ai";
    /** This is the language that will be set for the transcription. */
    language?: "en";
    /** The WebSocket URL that the transcriber connects to. */
    realtimeUrl?: string;
    /** Add up to 2500 characters of custom vocabulary. */
    wordBoost?: string[];
    /** The duration of the end utterance silence threshold in milliseconds. */
    endUtteranceSilenceThreshold?: number;
    /**
     * Disable partial transcripts.
     * Set to `true` to not receive partial transcripts. Defaults to `false`.
     */
    disablePartialTranscripts?: boolean;
    /** This is the plan for voice provider fallbacks in the event that the primary voice provider fails. */
    fallbackPlan?: Vapi.FallbackTranscriberPlan;
}
