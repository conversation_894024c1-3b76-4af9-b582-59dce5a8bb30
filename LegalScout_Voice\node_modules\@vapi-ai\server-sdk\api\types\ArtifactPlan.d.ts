/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Vapi from "../index";
export interface ArtifactPlan {
    /**
     * This determines whether assistant's calls are recorded. Defaults to true.
     *
     * Usage:
     * - If you don't want to record the calls, set this to false.
     * - If you want to record the calls when `assistant.hipaaEnabled` (deprecated) or `assistant.compliancePlan.hipaaEnabled` explicity set this to true and make sure to provide S3 or GCP credentials on the Provider Credentials page in the Dashboard.
     *
     * You can find the recording at `call.artifact.recordingUrl` and `call.artifact.stereoRecordingUrl` after the call is ended.
     *
     * @default true
     */
    recordingEnabled?: boolean;
    /**
     * This determines the format of the recording. Defaults to `wav;l16`.
     *
     * @default 'wav;l16'
     */
    recordingFormat?: Vapi.ArtifactPlanRecordingFormat;
    /**
     * This determines whether the video is recorded during the call. Defaults to false. Only relevant for `webCall` type.
     *
     * You can find the video recording at `call.artifact.videoRecordingUrl` after the call is ended.
     *
     * @default false
     */
    videoRecordingEnabled?: boolean;
    /**
     * This determines whether the SIP packet capture is enabled. Defaults to true. Only relevant for `phone` type calls where phone number's provider is `vapi` or `byo-phone-number`.
     *
     * You can find the packet capture at `call.artifact.pcapUrl` after the call is ended.
     *
     * @default true
     */
    pcapEnabled?: boolean;
    /**
     * This is the path where the SIP packet capture will be uploaded. This is only used if you have provided S3 or GCP credentials on the Provider Credentials page in the Dashboard.
     *
     * If credential.s3PathPrefix or credential.bucketPlan.path is set, this will append to it.
     *
     * Usage:
     * - If you want to upload the packet capture to a specific path, set this to the path. Example: `/my-assistant-captures`.
     * - If you want to upload the packet capture to the root of the bucket, set this to `/`.
     *
     * @default '/'
     */
    pcapS3PathPrefix?: string;
    /** This is the plan for `call.artifact.transcript`. To disable, set `transcriptPlan.enabled` to false. */
    transcriptPlan?: Vapi.TranscriptPlan;
    /**
     * This is the path where the recording will be uploaded. This is only used if you have provided S3 or GCP credentials on the Provider Credentials page in the Dashboard.
     *
     * If credential.s3PathPrefix or credential.bucketPlan.path is set, this will append to it.
     *
     * Usage:
     * - If you want to upload the recording to a specific path, set this to the path. Example: `/my-assistant-recordings`.
     * - If you want to upload the recording to the root of the bucket, set this to `/`.
     *
     * @default '/'
     */
    recordingPath?: string;
}
